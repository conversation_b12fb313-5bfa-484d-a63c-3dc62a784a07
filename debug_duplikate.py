#!/usr/bin/python
# coding=utf-8

"""
Debug-Skript für die Duplikat-Erkennung
"""

import pandas as pd
from datetime import datetime

def create_test_data():
    """Erstellt die Testdaten aus dem Screenshot"""

    test_data = [
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2022, 4, 14, 18, 0, 0), 'aenderungsdatum': datetime(2021, 10, 14, 17, 50, 44)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2022, 6, 15, 18, 0, 0), 'aenderungsdatum': datetime(2022, 3, 21, 17, 10, 17)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2022, 8, 19, 18, 0, 0), 'aenderungsdatum': datetime(2022, 5, 19, 8, 55, 23)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2022, 11, 18, 18, 0, 0), 'aenderungsdatum': datetime(2022, 7, 21, 7, 59, 43)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 5, 16, 18, 19, 20), 'aenderungsdatum': datetime(2022, 12, 15, 15, 22, 31)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 9, 29, 18, 10, 1), 'aenderungsdatum': datetime(2023, 5, 9, 15, 18, 10)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 10, 29, 16)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 15, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 10, 29, 18)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 10, 29, 41)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 15, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 10, 31, 22)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 10, 49, 38)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 13, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 11, 0, 34)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 7, 11, 3, 53)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 15, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 7, 33)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 7, 34)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 15, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 7, 38)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 7, 41)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 15, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 7, 58)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 10, 20)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 13, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 13, 36)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2023, 12, 20, 18, 0, 0), 'aenderungsdatum': datetime(2023, 8, 8, 1, 14, 2)},
        {'requidbafa': 'w00000096461339', 'hist_betriebsber_korr': datetime(2024, 6, 6, 18, 0, 0), 'aenderungsdatum': datetime(2024, 1, 3, 14, 13, 18)},
    ]

    return pd.DataFrame(test_data)

def _find_consecutive_duplicates(group_sorted):
    """Findet aufeinanderfolgende Duplikate (Regel 1)"""
    if len(group_sorted) < 2:
        return pd.DataFrame()

    # Erstelle eine Spalte mit dem vorherigen hist_betriebsber_korr Wert
    group_sorted['prev_hist_betriebsber_korr'] = group_sorted['hist_betriebsber_korr'].shift(1)

    # Identifiziere Duplikate (wo aktueller Wert = vorheriger Wert)
    duplicates = group_sorted[
        (group_sorted['hist_betriebsber_korr'] == group_sorted['prev_hist_betriebsber_korr']) &
        (group_sorted['prev_hist_betriebsber_korr'].notna())
    ]

    if not duplicates.empty:
        result = duplicates[['requidbafa', 'hist_betriebsber_korr', 'aenderungsdatum']].copy()
        result['regel'] = 'aufeinanderfolgend'
        return result

    return pd.DataFrame()

def _find_time_based_duplicates(group_sorted):
    """Findet zeitbasierte Duplikate zwischen 0-2 Uhr (Regel 2)

    Regel: Wenn es für einen hist_betriebsber_korr Wert sowohl Tag- als auch Nacht-Datensätze gibt,
    dann lösche ALLE Nacht-Datensätze (0-2 Uhr) und behalte nur die Tag-Datensätze.
    """
    if len(group_sorted) < 2:
        return []

    duplicates_list = []

    print("=== Regel 2 Debug (Neue Logik) ===")

    # Gruppiere nach hist_betriebsber_korr Wert
    for hist_value, hist_group in group_sorted.groupby('hist_betriebsber_korr'):
        print(f"\nGruppe hist_betriebsber_korr = {hist_value}")
        print(f"Anzahl Datensätze in Gruppe: {len(hist_group)}")

        if len(hist_group) < 2:  # Keine Duplikate möglich
            print("  -> Übersprungen (weniger als 2 Datensätze)")
            continue

        # Sortiere nach aenderungsdatum
        hist_group_sorted = hist_group.sort_values('aenderungsdatum').reset_index(drop=True)

        print("  Alle Datensätze in dieser Gruppe:")
        for _, row in hist_group_sorted.iterrows():
            hour = row['aenderungsdatum'].hour
            tag_nacht = "NACHT" if 0 <= hour < 2 else "TAG"
            print(f"    {row['aenderungsdatum']} (Stunde: {hour}, {tag_nacht})")

        # Teile in Tag- und Nacht-Datensätze auf
        night_records = hist_group_sorted[
            (hist_group_sorted['aenderungsdatum'].dt.hour >= 0) &
            (hist_group_sorted['aenderungsdatum'].dt.hour < 2)
        ]

        day_records = hist_group_sorted[
            (hist_group_sorted['aenderungsdatum'].dt.hour >= 2) |
            (hist_group_sorted['aenderungsdatum'].dt.hour <= 23)
        ]

        print(f"  Tag-Datensätze (2-23h): {len(day_records)}")
        print(f"  Nacht-Datensätze (0-2h): {len(night_records)}")

        # Wenn es sowohl Tag- als auch Nacht-Datensätze gibt, lösche ALLE Nacht-Datensätze
        if not night_records.empty and not day_records.empty:
            print(f"  -> ALLE {len(night_records)} Nacht-Datensätze werden als Duplikate markiert")
            for _, row in night_records.iterrows():
                print(f"    Duplikat: {row['aenderungsdatum']}")
                duplicates_list.append({
                    'requidbafa': row['requidbafa'],
                    'hist_betriebsber_korr': row['hist_betriebsber_korr'],
                    'aenderungsdatum': row['aenderungsdatum'],
                    'regel': 'zeitbasiert_0-2h'
                })
        elif not night_records.empty and day_records.empty:
            print("  -> Nur Nacht-Datensätze, keine Tag-Datensätze -> keine Duplikate")
        elif night_records.empty and not day_records.empty:
            print("  -> Nur Tag-Datensätze, keine Nacht-Datensätze -> keine Duplikate")
        else:
            print("  -> Keine Datensätze gefunden")

    return duplicates_list

def test_duplicate_detection():
    """Testet die Duplikat-Erkennung"""

    print("=== Test der erweiterten Duplikat-Erkennung ===")

    # Erstelle Testdaten
    df_test = create_test_data()
    print(f"Testdaten erstellt: {len(df_test)} Datensätze")

    # Sortiere nach aenderungsdatum aufsteigend für beide Regeln
    group_sorted = df_test.sort_values('aenderungsdatum').reset_index(drop=True)

    print("\nAlle Datensätze sortiert nach aenderungsdatum:")
    for i, row in group_sorted.iterrows():
        hour = row['aenderungsdatum'].hour
        print(f"{i+1:2d}. {row['hist_betriebsber_korr']} | {row['aenderungsdatum']} (Stunde: {hour})")

    # === REGEL 1: Aufeinanderfolgende Duplikate ===
    print("\n" + "="*50)
    print("REGEL 1: Aufeinanderfolgende Duplikate")
    print("="*50)
    regel1_duplicates = _find_consecutive_duplicates(group_sorted.copy())

    if not regel1_duplicates.empty:
        print(f"Regel 1 gefunden: {len(regel1_duplicates)} Duplikate")
        for _, row in regel1_duplicates.iterrows():
            print(f"  {row['hist_betriebsber_korr']} | {row['aenderungsdatum']}")
    else:
        print("Regel 1: Keine Duplikate gefunden")

    # === REGEL 2: Zeitbasierte Duplikate (0-2 Uhr) ===
    print("\n" + "="*50)
    print("REGEL 2: Zeitbasierte Duplikate (0-2 Uhr)")
    print("="*50)
    regel2_duplicates_list = _find_time_based_duplicates(group_sorted.copy())

    if regel2_duplicates_list:
        print(f"\nRegel 2 gefunden: {len(regel2_duplicates_list)} Duplikate")
        for dup in regel2_duplicates_list:
            print(f"  {dup['hist_betriebsber_korr']} | {dup['aenderungsdatum']}")
    else:
        print("\nRegel 2: Keine Duplikate gefunden")

    # Kombiniere beide Regeln
    print("\n" + "="*50)
    print("KOMBINIERTE ERGEBNISSE")
    print("="*50)

    all_duplicates = []

    # Regel 1 Duplikate hinzufügen
    if not regel1_duplicates.empty:
        for _, row in regel1_duplicates.iterrows():
            all_duplicates.append({
                'requidbafa': row['requidbafa'],
                'hist_betriebsber_korr': row['hist_betriebsber_korr'],
                'aenderungsdatum': row['aenderungsdatum'],
                'regel': row['regel']
            })

    # Regel 2 Duplikate hinzufügen
    if regel2_duplicates_list:
        all_duplicates.extend(regel2_duplicates_list)

    if all_duplicates:
        # Entferne Überschneidungen - Regel 1 hat Priorität
        seen = set()
        unique_duplicates = []
        for dup in all_duplicates:
            key = (dup['requidbafa'], dup['aenderungsdatum'])
            if key not in seen:
                seen.add(key)
                unique_duplicates.append(dup)

        print(f"Insgesamt {len(unique_duplicates)} Duplikate gefunden:")
        for dup in unique_duplicates:
            print(f"  {dup['regel']}: {dup['hist_betriebsber_korr']} | {dup['aenderungsdatum']}")
    else:
        print("Keine Duplikate gefunden")

if __name__ == "__main__":
    test_duplicate_detection()
