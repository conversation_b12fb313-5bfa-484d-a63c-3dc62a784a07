import gc
import time
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from datetime import datetime
from os import path
from sys import exit

from dag import <PERSON>g<PERSON><PERSON><PERSON>
from verarbeitung.src.tables.check_1_wfmt.check_1_wfmt import run as Check1WfmtRun
from verarbeitung.src.tables.check_2_sma.check_2_sma import run as Check2SmaRun
from verarbeitung.src.tables.check_3__check_4__bafa_sma.check_3__check_4__bafa_sma import run as Check3Check4Run
from verarbeitung.src.tables.check_3__check_4__bafa_sma.check_3__check_4__bafa_sma import (
    run_post as Check3Check4RunPost,
)
from verarbeitung.src.tables.check_3__check_4__bafa_sma.check_3__check_4__bafa_sma import run_pre as Check3Check4RunPre
from verarbeitung.src.tables.check_5_kommentar_index.check_5_kommentar_index import (
    run_pre as Check5KommentarIndexRunPre,
)
from verarbeitung.src.tables.check_5_kommentar_index.check_5_kommentar_index import run as Check5KommentarIndexRun
from verarbeitung.src.tables.check_6_bbk_id.check_6_bbk_id import run as Check6bbkIdRun
from verarbeitung.src.tables.check_6_bbk_id.check_6_bbk_id import run_processing as Check6bbkIdRunProcessing
from verarbeitung.src.tables.check_7_basis_1_wfm_psl.check_7_basis_1_wfm_psl import run as Check7Run
from verarbeitung.src.tables.check_8_auftragsnrag.check_8_auftragsnrag import run_pre as Check8AuftragsnragPre
from verarbeitung.src.tables.check_8_auftragsnrag.check_8_auftragsnrag import run as Check8Auftragsnrag

# tODO
from verarbeitung.src.tables.Common.task import DeleteTaskConfig, IndexType, Status
from verarbeitung.src.tables.del_1_wfmt.del_1_wfmt import run as Del1WFMTRun
from verarbeitung.src.tables.del_1_wfmt_error.del_1_wfmt_error import run as Del1WFMTErrorRun
from verarbeitung.src.tables.del_2_sma.del_2_sma import run as Del2SMARun
from verarbeitung.src.tables.del_2_sma_error.del_2_sma_error import run as Del2SMAErrorRun
from verarbeitung.src.tables.del_3_kom_index.del_3_kom_index import run as Del3KomIndexRun
from verarbeitung.src.tables.del_4_aa_id.del_4_aa_id import run as Del4AAIDRun
from verarbeitung.src.tables.del_5_bbk_id.del_5_bbk_id import run as Del5BbkIdRun
from verarbeitung.src.tables.del_6_auftragsnrag.del_6_auftragsnrag import run as Del6AuftragsnragRun
from verarbeitung.src.tables.del_fr_tag.del_fr_tag import run as DelFrTagRun
from verarbeitung.src.tables.del_fr_tag_fin.del_fr_tag_fin import run as DelFrTagFinRun
from verarbeitung.src.tables.helper.helper import debugprint
from verarbeitung.src.tables.loeschung_nach_id.loeschung_nach_id import run as LoeschungByIdRun
from verarbeitung.src.tables.loeschung_optionswerte.loeschung_optionswerte import run as LoeschungOptionswerteRun
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Multiprocess
from verarbeitung.src.tables.validate_check_data.validate_check_data import run as ValidationRun
from verarbeitung.src.tables.validate_del_error.validate_del_error import run_aaid as ValidateDelAAIDErrorRun
from verarbeitung.src.tables.validate_del_error.validate_del_error import run_wfmt_del1 as ValidateDel1WFMTErrorRun
from verarbeitung.src.tables.validate_del_error.validate_del_error import run_wfmt_del2 as ValidateDel2SMAErrorRun
from verarbeitung.src.tables.validate_del_error.validate_del_error import run_bbk as ValidateDelBBKErrorRun
from verarbeitung.src.tables.tx_beast_domain_model_comment.tx_beast_domain_model_comment import (
    run as LoeschungKommentareRun,
)
from verarbeitung.src.tables.del_tx_domain_model_todo.del_tx_beast_domain_model_todo import run as LoeschungTodoRun

from tools.Notification.mail import send_mail
from tools.process_overview import is_processing_running, set_process_name

SCRIPTS_BASE_PATH = "verarbeitung/src/tables"
CURRENT_PATH = path.dirname(path.realpath(__file__))

MAIL_RECIPIENTS = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]


class Mailer:
    def __init__(self, args: Namespace):
        self.args = args
        self.start = datetime.now()
        self.year = self.start.year - args.year_offset

    def send_start_mail(self) -> None:
        message = f"""Die Löschroutine für BEAST für das Jahr {self.year} wurde am {self.start.strftime("%d.%m.%Y")} um {self.start.strftime("%H:%M")} Uhr gestartet"""
        subject = "Start der BEAST Löschroutine"
        send_mail(message=message, subject=subject, recipients=MAIL_RECIPIENTS)

    def send_stop_mail(self) -> None:
        now = datetime.now()
        _delta = now - self.start
        _days, _hours, _minutes = _delta.days, _delta.seconds // 3600, (_delta.seconds // 60) % 60

        message = f"""Die Löschroutine für BEAST für das Jahr {self.year} wurde beendet.
        
        Start am {self.start.strftime("%d.%m.%Y")} um {self.start.strftime("%H:%M")} Uhr
        Ende am {now.strftime("%d.%m.%Y")} um {now.strftime("%H:%M")} Uhr
        
        Dauer: {_days} Tage, {_hours} Stunden, {_minutes} Minuten
        
        """
        subject = "Stop der BEAST Löschroutine"
        send_mail(message=message, subject=subject, recipients=MAIL_RECIPIENTS)

    def send_error_mail(self) -> None:
        message = f"""Die Löschroutine für BEAST für das Jahr {self.year} wurde aufgrund eines Fehlers abgebrochen."""
        subject = "Abbruch der BEAST Löschroutine"
        send_mail(message=message, subject=subject, recipients=MAIL_RECIPIENTS)


class DeleteDagRunner(DagRunner):
    """Re-use existing dag"""

    def __init__(self, args):
        args.no_aggregation = True
        args.module = None
        super().__init__(args)
        if not args.do_delete and not args.only_delete:
            self.exclude_delete_tasks()
        if args.only_delete:
            self.exclude_non_delete_tasks()

    def on_error(self, message):
        # ignore traceback
        debugprint("Error occured, aborting ...")
        mailer.send_error_mail()
        self.shutdown()

    def exclude_delete_tasks(self):
        for task in self.tasks_dict:
            if self.tasks_dict[task].delete:
                self.tasks_dict[task].status = Status.DONE

    def exclude_non_delete_tasks(self):
        for task in self.tasks_dict:
            if not self.tasks_dict[task].delete:
                self.tasks_dict[task].status = Status.DONE

    def build_dag(self):
        check_1_wfmt = self.add_task(
            DeleteTaskConfig(
                name="check_1_wfmt",
                dependent=[],
                run_function=Check1WfmtRun,
                tables=["wmsti_psl_001", "basis_1_wms_psl", "check_9_wfmt_status_valid"],
            )
        )
        check_2_sma = self.add_task(
            DeleteTaskConfig(
                name="check_2_sma",
                dependent=[],
                run_function=Check2SmaRun,
                tables=["sm_auftrag_psl_allgemein", "psl_koa_obligo", "basis_1_wms_psl"],
            )
        )
        check_7_basis_1_wfm_psl = self.add_task(
            DeleteTaskConfig(
                name="check_7_basis_1_wfm_psl",
                dependent=[check_2_sma],
                run_function=Check7Run,
                tables=["basis_1_wms_psl"],
            )
        )
        check_3__check4_pre = self.add_task(
            DeleteTaskConfig(
                name="check3__check4_pre",
                dependent=[check_1_wfmt, check_2_sma, check_7_basis_1_wfm_psl],
                run_function=Check3Check4RunPre,
                tables=["check_1_wfmt", "check_2_sma"],
            )
        )
        check_3__check4 = self.add_task(
            DeleteTaskConfig(
                name="check3__check4",
                dependent=[check_1_wfmt, check_2_sma, check_3__check4_pre],
                run_function=Check3Check4Run,
                tables=["check_1_wfmt", "check_2_sma"],
                additional_data={"year_offset": args.year_offset},
            )
        )
        check_3__check4_post = self.add_task(
            DeleteTaskConfig(
                name="check3__check4_post",
                dependent=[check_3__check4],
                run_function=Check3Check4RunPost,
                tables=["check_3_bafa_sma", "check_4_sma_bafa", "check_7_basis_1_wfm_psl"],
            )
        )
        del_1_wfmt = self.add_task(
            DeleteTaskConfig(
                name="del_1_wfmt",
                dependent=[check_3__check4_post],
                run_function=Del1WFMTRun,
                tables=["check_3_wfmt"],
            )
        )
        del_1_wfmt_error = self.add_task(
            DeleteTaskConfig(
                name="del_1_wmft_error",
                dependent=[del_1_wfmt],
                run_function=Del1WFMTErrorRun,
                tables=["check_3_wfmt", "del_1_wfmt"],
            )
        )
        del_2_sma = self.add_task(
            DeleteTaskConfig(
                name="del_2_sma",
                dependent=[check_3__check4_post],
                run_function=Del2SMARun,
                tables=["check_4_sma_bafa"],
            )
        )
        del_2_sma_error = self.add_task(
            DeleteTaskConfig(
                name="del_2_sma_error",
                dependent=[del_2_sma],
                run_function=Del2SMAErrorRun,
                tables=["check_4_sma_bafa", "del_2_sma"],
            )
        )
        validate_del_1_wfmt_error = self.add_task(
            DeleteTaskConfig(
                name="validate_del_1_wfmt_error",
                dependent=[del_1_wfmt_error, del_2_sma_error],
                run_function=ValidateDel1WFMTErrorRun,
                tables=["del_1_wfmt_error"],
            )
        )
        validate_del_2_sma_error = self.add_task(
            DeleteTaskConfig(
                name="validate_del_2_sma_error",
                dependent=[del_1_wfmt_error, del_2_sma_error],
                run_function=ValidateDel2SMAErrorRun,
                tables=["del_2_sma_error"],
            )
        )
        check_5_kom_index_pre = self.add_task(
            DeleteTaskConfig(
                name="check_5_kom_index_pre",
                dependent=[],
                run_function=Check5KommentarIndexRunPre,
                tables=["ueweg_1_allgemein"],
            )
        )
        check_5_kom_index = self.add_task(
            DeleteTaskConfig(
                name="check_5_kom_index",
                dependent=[check_5_kom_index_pre, del_1_wfmt, validate_del_1_wfmt_error, validate_del_2_sma_error],
                run_function=Check5KommentarIndexRun,
                tables=["ueweg_1_allgemein"],
            )
        )
        del_3_kom_index = self.add_task(
            DeleteTaskConfig(
                name="del_3_kom_index",
                dependent=[check_5_kom_index],
                run_function=Del3KomIndexRun,
                tables=["check_5_kommentar_index"],
            )
        )
        del_4_aa_id = self.add_task(
            DeleteTaskConfig(
                name="del_4_aa_id",
                dependent=[del_1_wfmt, del_2_sma, validate_del_1_wfmt_error, validate_del_2_sma_error],
                run_function=Del4AAIDRun,
                tables=["b2b_zele_basis", "del_1_wfmt", "del_2_sma"],
            )
        )
        validate_del_aaid_error = self.add_task(
            DeleteTaskConfig(
                name="validate_del_aaid_error",
                dependent=[del_4_aa_id],
                run_function=ValidateDelAAIDErrorRun,
                tables=["del_4_aa_idr"],
            )
        )
        check_6_bbk_id_processing = self.add_task(
            DeleteTaskConfig(
                name="check_6_bbk_id_processing",
                dependent=[del_1_wfmt, validate_del_1_wfmt_error, validate_del_2_sma_error],
                run_function=Check6bbkIdRunProcessing,
                tables=["bbt_auftragsdaten", "bbt_aktivitaeten"],
            )
        )
        check_6_bbk_id = self.add_task(
            DeleteTaskConfig(
                name="check_6_bbk_id",
                dependent=[check_6_bbk_id_processing],
                run_function=Check6bbkIdRun,
                tables=["basis_1_wms_psl"],
            )
        )
        check_8_auftragsnrag_pre = self.add_task(
            DeleteTaskConfig(
                name="check_8_auftraqsnrag_pre",
                dependent=[],
                run_function=Check8AuftragsnragPre,
                tables=["wmsti_psl_001"],
            )
        )
        check_8_auftragsnrag = self.add_task(
            DeleteTaskConfig(
                name="check_8_auftraqsnrag",
                dependent=[del_1_wfmt, check_8_auftragsnrag_pre],
                run_function=Check8Auftragsnrag,
                tables=["wmsti_psl_001"],
            )
        )
        del_5_bbk_id = self.add_task(
            DeleteTaskConfig(
                name="del_5_bbk_id",
                dependent=[check_6_bbk_id],
                run_function=Del5BbkIdRun,
                tables=["check_6_bbk_id", "del_5_bbk_id"],
            )
        )
        del_6_auftragsnrag = self.add_task(
            DeleteTaskConfig(
                name="del_6_auftragsnrag",
                dependent=[check_8_auftragsnrag],
                run_function=Del6AuftragsnragRun,
                tables=["check_8_auftraqsnrag", "processing_check_8_auftraqsnrag"],
            )
        )
        validate_del_bbk_id_error = self.add_task(
            DeleteTaskConfig(
                name="validate_del_bbk_id_error",
                dependent=[del_5_bbk_id, del_3_kom_index],
                run_function=ValidateDelBBKErrorRun,
                tables=["del_5_bbk_id_error"],
            )
        )
        del_fr_tag = self.add_task(
            DeleteTaskConfig(
                name="del_fr_tag",
                dependent=[],
                run_function=DelFrTagRun,
                tables=["fr_tag"],
                delete=True,
            )
        )
        del_fr_tag_fin = self.add_task(
            DeleteTaskConfig(
                name="del_fr_tag_fin",
                dependent=[],
                run_function=DelFrTagFinRun,
                tables=["fr_tag_fin"],
                delete=True,
            )
        )
        validate_data = self.add_task(
            DeleteTaskConfig(
                name="validate_data",
                dependent=[
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                    validate_del_bbk_id_error,
                ],
                run_function=ValidationRun,
                tables=[
                    "check_1_wfmt",
                    "check_2_sma",
                    "check_3_bafa_sma",
                    "check_4_sma_bafa",
                ],
                additional_data={"year_offset": args.year_offset},
            )
        )
        loeschung_sma = self.add_task(
            DeleteTaskConfig(
                name="loeschung_sma",
                dependent=[
                    del_2_sma,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                    validate_data,
                    validate_del_bbk_id_error,
                ],
                run_function=LoeschungByIdRun,
                additional_data={
                    "delete_base_table": "del_2_sma",
                    "delete_base_column": "sma",
                    "delete_index_type": IndexType.sma,
                },
                delete=True,
            )
        )
        loeschung_bafa = self.add_task(
            DeleteTaskConfig(
                name="loeschung_bafa",
                dependent=[
                    validate_data,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                    validate_del_bbk_id_error,
                ],
                run_function=LoeschungByIdRun,
                additional_data={
                    "delete_base_table": "del_1_wfmt",
                    "delete_base_column": "requidbafa",
                    "delete_index_type": IndexType.bafa,
                },
                delete=True,
            )
        )
        loeschung_aa_id = self.add_task(
            DeleteTaskConfig(
                name="loeschung_aaid",
                dependent=[
                    validate_data,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                    validate_del_bbk_id_error,
                ],
                run_function=LoeschungByIdRun,
                additional_data={
                    "delete_base_table": "del_4_aa_id",
                    "delete_base_column": "aa_id",
                    "delete_index_type": IndexType.aaid,
                },
                delete=True,
            )
        )
        loeschung_bbk_id = self.add_task(
            DeleteTaskConfig(
                name="loeschung_bbkid",
                dependent=[
                    validate_data,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                    validate_del_bbk_id_error,
                ],
                run_function=LoeschungByIdRun,
                additional_data={
                    "delete_base_table": "del_5_bbk_id",
                    "delete_base_column": "bbk_id",
                    "delete_index_type": IndexType.bbkid,
                },
                delete=True,
            )
        )
        loeschung_optionswerte = self.add_task(
            DeleteTaskConfig(
                name="loeschung_optionswerte",
                run_function=LoeschungOptionswerteRun,
                dependent=[
                    validate_data,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                ],
                tables=[],
                delete=True,
            )
        )
        loeschung_kommentare = self.add_task(
            DeleteTaskConfig(
                name="loeschung_kommentare",
                run_function=LoeschungKommentareRun,
                dependent=[
                    validate_data,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_aaid_error,
                    del_6_auftragsnrag,
                ],
                tables=[],
                delete=True,
            )
        )
        loeschung_todo = self.add_task(
            DeleteTaskConfig(
                name="loeschung_todo",
                run_function=LoeschungTodoRun,
                dependent=[
                    validate_data,
                    validate_del_1_wfmt_error,
                    validate_del_2_sma_error,
                    validate_del_bbk_id_error,
                ],
                tables=[],
                delete=True,
            )
        )


def init_arguments() -> Namespace:

    parser = ArgumentParser(
        description="""Only one of the following parameters
    \"--start-at, --exclude or --only --do_delete --only_delete\" can be used.
    \n
    """.format(
            ","
        )
    )

    parser.add_argument("-bar", "--bar", dest="processbar", default=False, action="store_true", help="Show Processbar")

    parser.add_argument(
        "-exclude", "--exclude", dest="exclude", nargs="+", help="Start all task without the excluded tasks"
    )

    parser.add_argument("-only", "--only", dest="only", nargs="+", help="Start only the given tasks")

    parser.add_argument(
        "-do_delete", "--do_delete", dest="do_delete", action="store_true", help="Run tasks, including delete tasks"
    )

    parser.add_argument(
        "-only_delete", "--only_delete", dest="only_delete", action="store_true", help="ONLY run delete tasks"
    )

    parser.add_argument(
        "-year_offset",
        "--year_offset",
        dest="year_offset",
        nargs="?",
        help="Year offset for deletion",
        type=int,
        default=3,
    )

    args = parser.parse_args()

    args.start_at = None
    args.run_to = None

    return args


if __name__ == "__main__":

    args = init_arguments()

    mailer = Mailer(args)

    def on_error(cls, error_message):
        debugprint("Error occured, aborting ...")
        mailer.send_error_mail()

    # overload multiprocessing on_error
    Multiprocess.on_error = on_error

    if not gc.isenabled():
        gc.enable()
    if args.only and args.exclude:
        debugprint("Please use only one of the following Parameter: [exclude or only]")

    if is_processing_running():
        debugprint("Processing is running, aborting...")
        exit(1)

    start = time.time()
    debugprint("Start at {}".format(datetime.now()))

    # set a recognizable process name but not the same as the original dag
    set_process_name("BEAST_GET_DELETABLE")
    deldag = DeleteDagRunner(args)

    mailer.send_start_mail()

    deldag.start_dag()

    debugprint("Done at {}".format(datetime.now()))
    debugprint("\nProjects runtime:")
    for task in deldag.tasks_dict:
        debugprint(
            "- {name}: {time:0.0f}sec | End time:{end}".format(
                name=deldag.tasks_dict[task].name,
                time=deldag.tasks_dict[task].get_running_time(),
                end=deldag.tasks_dict[task].end_time,
            )
        )
    debugprint("---")
    debugprint("Processing Summery Time: {time:0.0f}sec".format(time=time.time() - start))

    deldag.shutdown()

    mailer.send_stop_mail()
