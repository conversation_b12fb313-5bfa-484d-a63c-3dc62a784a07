import gc
import time
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from importlib import import_module
from os import getpid
from random import randint
from resource import RUSAGE_CHILDREN, RUSAGE_SELF, getrusage
from threading import Thread
from typing import Callable, Dict

from Modules.beast_modules import TaskType
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.helper.helper import debugprint, format_exception_message
from verarbeitung.rabbitmq import DebugQueue, Ping, StatusQueue, WorkQueue, WorkerMessage
from verarbeitung.dag_modules import DagModules
from verarbeitung.task import TaskConfig, check_ram
from multiprocessing import Event, Process

# TODO get from env ?
MAX_WORKERS = 15
MAX_RAM_USAGE_MODULE_START_PERCENT = 65

TASK_KWARGS = ["additional_data", "num_processes", "max_package_size", "min_package_size", "needs_lock", "ram_factor"]

DEBUG = False


def import_run_function(run_function_path: str, run_function_name: str) -> Callable:
    """import run functions from modules"""
    _module = import_module(run_function_path)
    _run_function = getattr(_module, run_function_name)
    return _run_function


def expand_task_run_functions(tasks_dict: Dict[str, TaskConfig]) -> Dict[str, TaskConfig]:
    """expand run_function_path and run_function_name into a real function and set it as run_function"""
    for _name, task in tasks_dict.items():

        if task.run_function_path:
            task.run_function = import_run_function(task.run_function_path, task.run_function_name)
        elif task.run_function:
            # has already a run function and does not need to be expanded, like aggregations
            pass

    return tasks_dict


def load_tasks() -> Dict[str, TaskConfig]:
    """load and prepare tasks"""
    dag = DagModules()
    tasks_dict = expand_task_run_functions(dag.tasks_dict)
    return tasks_dict


class Runner:

    def __init__(self, dummy: bool = False):

        self.dummy = dummy

        self.db_manager = DatabaseManager()
        self.stop_event = Event()

        self.rabbitmq_work_queue = None
        self.work_queue = None
        self.status_queue = None

        self.process_list = []

        self.pid = getpid()

        # setup runner
        self.init_queues()

    def handle_worker_message(self, message: WorkerMessage) -> None:

        tasks_dict = load_tasks()
        task = message.map_to_task(tasks_dict)
        try:
            self.execute_module(task, stop_event=self.stop_event)

        except Exception as error:
            formated_exception = format_exception_message(error)
            task.error = formated_exception
            message = WorkerMessage(task=task)
            status_queue = StatusQueue()
            status_queue.publish(message.raw_message)

            if DEBUG:
                # debug stuff
                debug_queue = DebugQueue()
                debug_queue.publish(message.raw_message)

    def init_queues(self) -> None:
        self.rabbitmq_work_queue = WorkQueue()
        self.rabbitmq_work_queue.handle_message = self.handle_worker_message

    def execute_module(
        self,
        task: TaskConfig,
        stop_event: Event,
    ):
        if stop_event.is_set():
            debugprint(f"Skipping {task.name} since stopping is in progress")
            return
        try:
            task.start_time = time.time()
            if self.dummy:
                # don't actually do something, debug mode

                time.sleep(randint(1, 5))

                task.end_time = time.time()
                message = WorkerMessage(task=task)

                status_queue = StatusQueue()
                status_queue.publish(message.raw_message)

                debugprint(f"End DUMMY Project {task.name}, pid: {self.pid}")

                return

            if task.task_type == TaskType.Aggregation:
                task.run_function(stop_event=stop_event, processbar=None, sql=task.sql)
            else:
                # adapt kwargs if needed
                _kwargs = {
                    "stop_event": stop_event,
                    "processbar": None,
                }
                for _kwarg in TASK_KWARGS:
                    if getattr(task, _kwarg):
                        _kwargs[_kwarg] = getattr(task, _kwarg)
                    # TODO
                    # if getattr(self, "ERROR_MAIL_RECIPIENTS"):
                    #    _kwargs["ERROR_MAIL_RECIPIENTS"] = self.ERROR_MAIL_RECIPIENTS
                # start it
                debugprint(f"Running Task {task.name}, pid: {self.pid}")

                if not task.ram_factor and not task.num_processes:
                    # we really don't want to use swap if we can prevent it
                    # fall back to defaults
                    check_ram(self.stop_event, max_current_memory_usage_percent=MAX_RAM_USAGE_MODULE_START_PERCENT)
                elif task.ram_factor:
                    # get predicted ram usage
                    if not task.num_processes:
                        # single process
                        predicted_ram = task.ram_factor
                    else:
                        # multi process
                        predicted_ram = task.ram_factor * (task.num_processes)

                    debugprint(f"Predicted ram usage for task {task.name} is {predicted_ram}")
                    # adding 10% buffer
                    predicted_ram = predicted_ram * 1.15
                    debugprint(f"Setting min free ram for task {task.name} to {predicted_ram}")
                    check_ram(self.stop_event, min_free_ram=predicted_ram)

                task.run_function(**_kwargs)
                gc.collect()
        except Exception as error:
            formated_exception = format_exception_message(error)
            # self.on_error(formated_exception)
            task.error = formated_exception
        task.end_time = time.time()

        task.ram_usage = (getrusage(RUSAGE_SELF).ru_maxrss + getrusage(RUSAGE_CHILDREN).ru_maxrss) / 1024

        # channel.basic_ack(delivery_tag=method.delivery_tag)

        # send result to dag
        message = WorkerMessage(task=task)

        status_queue = StatusQueue()
        status_queue.publish(message.raw_message)

        debugprint(f"End Project {task.name}, pid: {self.pid}")

    def run(self) -> None:
        # do stuff
        debugprint(f"running worker process with pid {self.pid}")
        self.rabbitmq_work_queue.consume()
        debugprint(f"worker process with pid {self.pid} stopped consuming")


def run_runner(dummy: bool = False) -> None:
    runner = Runner(dummy=dummy)
    runner.run()
    debugprint("runner done")


class ProcessPool:
    """makeshift multiprocessing pool since the real Pool does not allow nested multiprocesses
    and even if it does (when we subclass it) workers are somehow not restarted after they finish so resources
     never get freed"""

    def __init__(self, max_workers: int = MAX_WORKERS, dummy: bool = False):
        self.max_workers = max_workers
        self.pool = []

        self.rabbitmq_status_queue = StatusQueue()
        self.rabbitmq_status_queue.purge_queue()

        self.pool_stop = Event()
        self.dummy = dummy

    def stop(self):

        self.pool_stop.set()

    def run_loop(self, target: callable) -> None:
        # stop each process after it is "done" with its task and spawn a new worker
        debugprint(f"Pool active")
        while not self.pool_stop.is_set():

            for _index in range(0, len(self.pool)):
                _proc = self.pool[_index]
                _proc.join(0.5)
                if _proc.exitcode is not None:
                    print(f"process {_proc.pid} finished, restarting")
                    # kill old process
                    _proc.terminate()
                    if not self.pool_stop.is_set():
                        # delete traces of old process
                        _proc = self.pool.pop(_index)
                        del _proc
                        gc.collect()
                        # spawn new process
                        _proc = Process(target=target, daemon=False, kwargs={"dummy": self.dummy})
                        self.pool.insert(_index, _proc)
                        _proc.start()

    def init_pool(self, target: callable) -> None:
        debugprint(f"Init main worker pool with {self.max_workers} workers")
        for i in range(0, MAX_WORKERS):
            proc = Process(target=target, daemon=False, kwargs={"dummy": self.dummy})
            self.pool.append(proc)

        for proc in self.pool:
            proc.start()

    def run(self, target: Callable) -> None:
        self.init_pool(target)
        self.run_loop(target)


class AnswerPing(Thread):

    def run(self):
        self.ping_queue = Ping()
        self.ping_queue.consume()
        print("stopped answerping")


def run(dummy: bool = False) -> None:

    # run several instances of the runner, each gets it's own connection etc to be kind of threading save
    pool = ProcessPool(dummy=dummy)
    AnswerPing().start()
    pool.run(target=run_runner)


def arguments() -> Namespace:
    parser = ArgumentParser()
    parser.add_argument("--dummy", dest="dummy", default=False, action="store_true", help="Be a dummy")

    return parser.parse_args()


if __name__ == "__main__":
    args = arguments()
    run(dummy=args.dummy)
