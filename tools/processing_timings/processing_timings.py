from argparse import ArgumentParser, Namespace
from datetime import datetime, date
from os import path, makedirs
from time import time
from typing import Dict, List, Tuple
import pandas as pd
import plotly.express as px

from verarbeitung.src.tables.Database.DatabaseManager import FrontendDatabaseManager
from verarbeitung.task import Status


DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

PROCESSING_TABLE = "beast_processing"
TIMING_TABLE = "beast_processing_time"


def create_dir(dirname) -> None:
    """Create a directory if it does not exist."""
    makedirs(dirname, exist_ok=True)


class TableTiming:
    def __init__(self, table_name: str, start: datetime = None, end: datetime = None):
        self.table_name = table_name
        self.start = start
        self.end = end


class Timings:
    def __init__(self):
        self.start = datetime.now()
        self.stop = None
        self.tables = {}
        self.id = None
        pass

    def init_tables(self, task_dict: Dict) -> None:
        """Init tables dict from task_dict"""
        for table_name, values in task_dict.items():
            # only add tasks which are supposed tu run
            if values.status == Status.WAIT_FOR_RUN:
                self.tables[table_name] = TableTiming(table_name)

    def update_table_data(self, table_name: str, start: time = None, end: time = None):
        if start:
            self.tables[table_name].start = datetime.fromtimestamp(start)
        if end:
            self.tables[table_name].end = datetime.fromtimestamp(end)

    def to_db(self):
        db = FrontendDatabaseManager()
        # insert processing into db and get id afterwards
        processing_query = f"""INSERT INTO {PROCESSING_TABLE} (start, stop) VALUES('{self.start.strftime(DATETIME_FORMAT)}', '{self.stop.strftime(DATETIME_FORMAT)}')"""
        db.deadlock_save_execute(processing_query)

        id_query = f"""SELECT processing_id FROM {PROCESSING_TABLE}
                    WHERE start = '{self.start.strftime(DATETIME_FORMAT)}'
                     and stop = '{self.stop.strftime(DATETIME_FORMAT)}'
        """
        data = db.deadlock_save_execute(id_query).first()
        if data:
            # get id and update
            self.id = data[0]

            query = f"""INSERT INTO {TIMING_TABLE} 
                    VALUES {",".join([f"('{table.table_name}', {self.id}, '{table.start}', '{table.end}')" for table in self.tables.values()])}"""

            db.deadlock_save_execute(query)


def get_dependencies(dag_data: Dict, target_task_names: List) -> List:
    dependencies = []
    for target in target_task_names:
        dependencies += [task.name for task in dag_data[target].dependend_tasks]
    dependencies = list(set(dependencies))
    if dependencies:
        return list(set(dependencies + get_dependencies(dag_data, dependencies)))
    else:
        return dependencies


def generate_reverse_dag() -> Tuple[Dict, object]:
    from dag import DagRunner, DummyArgs

    _dummy_args = DummyArgs()
    _dummy_args.no_aggregation = False
    dag = DagRunner(_dummy_args, all_types=True)
    dependended_on_tasks = []
    tasks = []
    for _task in dag.tasks_dict.values():
        tasks.append(_task.name)
        dependended_on_tasks += [_dep.name for _dep in _task.dependend_tasks if not _dep.name in dependended_on_tasks]
    non_depended = [task for task, _ in dag.tasks_dict.items() if not task in dependended_on_tasks]

    dependencies = {}

    # build dependency tree
    for base in non_depended:
        _deps = get_dependencies(dag.tasks_dict, [base])
        dependencies[base] = _deps

    return dependencies, dag


def generate_graphs(target_date: date) -> None:
    # get data
    # get target processings
    query = f"""Select * FROM {PROCESSING_TABLE} as processing
                WHERE DATE(processing.start) = '{str(target_date)}' OR DATE(processing.stop) = '{str(target_date)}'"""
    db = FrontendDatabaseManager()
    df_processings = db.get_dataframe_by_query(query)

    timing_query = f"""Select * from {TIMING_TABLE} WHERE processing_id in ({",".join([str(_id) for _id in df_processings["processing_id"].tolist()])})"""
    df_timing_data = db.get_dataframe_by_query(timing_query)

    _dependencies, _dag = generate_reverse_dag()

    df_timing_data["duration"] = ((df_timing_data["stop"] - df_timing_data["start"]).dt.seconds).apply(
        lambda x: f"{x} seconds"
    )

    # prevent errors if a report has been removed
    _dependend_tasks = {
        task.name: [subtask.name for subtask in task.dependend_tasks] for task in _dag.tasks_dict.values()
    }

    df_timing_data["dependencies"] = df_timing_data["table"].apply(lambda x: ",".join(_dependend_tasks.get(x, [])))

    for index in df_processings.index:
        processing_id = df_processings["processing_id"][index]
        start = df_processings["start"][index]
        stop = df_processings["stop"][index]
        df_processing_data = df_timing_data[df_timing_data["processing_id"] == processing_id].sort_values(by=["start"])
        if not df_processing_data.empty:

            folder_name = f"processing_data__{start}__{stop}"
            base_folder = path.join(path.dirname(path.dirname(__file__)), folder_name)
            create_dir(base_folder)

            # create main graph
            generate_single_graph(df_processing_data, base_folder, start, stop)

            for _table, table_dependencies in _dependencies.items():
                # generate one graph per aggregation
                if _table in df_processing_data["table"].tolist():
                    _df_table_data = df_processing_data[
                        (df_processing_data["table"].isin([_table] + table_dependencies))
                    ]
                    generate_single_graph(_df_table_data, base_folder, None, None, custom_name=f"{_table}")


def generate_single_graph(
    df_data: pd.DataFrame, folder: str, start: datetime = None, stop: datetime = None, custom_name: str = ""
) -> None:
    """Generate and write interactive diagram"""
    fig = px.timeline(
        df_data, x_start="start", x_end="stop", y="table", color="table", hover_data=["duration", "dependencies"]
    )
    if not custom_name:
        _name = f"processing__{start.strftime(DATETIME_FORMAT)}__{stop.strftime(DATETIME_FORMAT)}.html"
    else:
        _name = f"{custom_name}.html"
    fig.write_html(f"{folder}/{_name}")
    print(f"Written graph {_name} to {folder}")


def arguments() -> Namespace:
    parser = ArgumentParser()
    parser.add_argument("--day", dest="target_day")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = arguments()
    if not args.target_day:
        target_date = datetime.now().date()
    else:
        target_date = datetime.strptime(args.target_day, "%Y.%m.%d")
    generate_graphs(target_date)
