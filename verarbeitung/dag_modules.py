from typing import Dict, List

from Modules.beast_modules import TaskType
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.task import (
    IndexColumn,
    IndexType,
    TaskConfig,
    build_tasks,
    get_sql_definitions_from_db_for_dynamic_aggregation_tasks,
)
import pandas as pd

MODULES_TABLE = "dag_modules"


class __DagModules:
    """THESE ARE THE TASK DEFINITIONS WHICH WILL BE STORED IN THE DB"""

    def __init__(self):
        self.tasks_dict = {}
        self.build_dag()

    def add_task(self, task):
        """

        :type task: object
        """
        self.tasks_dict[task.name] = task
        return task

    def build_dag(self):
        orge = self.add_task(
            TaskConfig(
                name="orge",
                run_function_path="verarbeitung.src.tables.orge.orge",
                run_function_name="run",
                tables=["ref_orge", "ref_ni_schema"],
                maincolumn=[],
                task_type=[TaskType.WFMT],
            )
        )

        manual_processing_tables = self.add_task(
            TaskConfig(
                name="manual_processing_tables",
                maincolumn=[],
                run_function_path="verarbeitung.src.tables.manual_processing_tables.manual_processing_tables",
                run_function_name="run",
                task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.DWH, TaskType.BBT, TaskType.BZM, TaskType.KLS],
                num_processes=0,
            ),
        )

        basis_1_wms_psl = self.add_task(
            TaskConfig(
                name="basis_1_wms_psl",
                maincolumn=[
                    IndexColumn(index_type=IndexType.bafa, column="requidbafa"),
                    IndexColumn(index_type=IndexType.sma, column="sma_verbunden"),
                ],
                run_function_path="verarbeitung.src.tables.basis_1_wms.basis_1_wms",
                run_function_name="run",
                dependend_tasks=[orge, manual_processing_tables],
                tables=["wmsti_psl_001"],
                result_tables=["processing_sma"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
                min_package_size=50000,
            ),
        )

        prepare_processing_ba = self.add_task(
            TaskConfig(
                name="processing_ba",
                run_function_path="verarbeitung.src.tables.processing_ba.processing_ba",
                run_function_name="run",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="ba")],
                dependend_tasks=[basis_1_wms_psl],
                tables=["processing_bafa", "basis_1_wms_psl"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        processing_bafa_by_sma = self.add_task(
            TaskConfig(
                name="processing_bafa_by_sma",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="bafa")],
                dependend_tasks=[basis_1_wms_psl],
                run_function_path="verarbeitung.src.tables.processing_bafa_by_sma.processing_bafa_by_sma",
                run_function_name="run",
                task_type=[TaskType.WFMT],
                max_package_size=100000,
                min_package_size=10000,
            )
        )

        processing_bafa_complete_without_agbs_pabgs = self.add_task(
            TaskConfig(
                name="processing_bafa_complete_without_agbs_pabgs",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="bafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.processing_bafa_complete_without_agbs_pabgs.processing_bafa_complete_without_agbs_pabgs",
                run_function_name="run",
                tables=["statusergaenzungen", "wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        processing_bafa_complete_without_agbs_tagbs_pabgs = self.add_task(
            (
                TaskConfig(
                    name="processing_bafa_complete_without_agbs_tagbs_pabgs",
                    maincolumn=[IndexColumn(index_type=IndexType.bafa, column="bafa")],
                    dependend_tasks=[manual_processing_tables],
                    run_function_path="verarbeitung.src.tables.processing_bafa_complete_without_agbs_tagbs_pabgs.processing_bafa_complete_without_agbs_tagbs_pabgs",
                    run_function_name="run",
                    tables=["wmsti_psl_001"],
                    task_type=[TaskType.WFMT],
                    num_processes=0,
                )
            )
        )

        psl_01 = self.add_task(
            TaskConfig(
                name="psl_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.psl_01.psl_01",
                run_function_name="run",
                tables=["sm_auftrag_psl_allgemein"],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        psl_02 = self.add_task(
            TaskConfig(
                name="psl_2_psp",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.psl_02.psl_02",
                run_function_name="run",
                tables=["sm_auftrag_psl_allgemein"],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        psl_03 = self.add_task(
            TaskConfig(
                name="psl_3_termine",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.psl_03.psl_03",
                run_function_name="run",
                tables=["sm_auftrag_psl_allgemein"],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        psl_04 = self.add_task(
            TaskConfig(
                name="psl_4_status",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.psl_04.psl_04",
                run_function_name="run",
                tables=["sm_auftrag_psl_allgemein"],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        psl_05 = self.add_task(
            TaskConfig(
                name="psl_5_meldungen",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.psl_05.psl_05",
                run_function_name="run",
                tables=["psl_meldungen", "sm_auftrag_psl_allgemein"],
                task_type=[TaskType.BI4PSL],
                max_package_size=1000,
            )
        )
        psl_06 = self.add_task(
            TaskConfig(
                name="psl_6_finanz",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.psl_06.psl_06",
                run_function_name="run",
                tables=["psl_koa_plan_ist", "psl_koa_obligo"],
                task_type=[TaskType.BI4PSL, TaskType.DWH],
                max_package_size=10000,
            )
        )

        wms_01 = self.add_task(
            TaskConfig(
                name="wms_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wms_01.wms_01",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wms_02 = self.add_task(
            TaskConfig(
                name="wms_2_termine",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_by_sma, basis_1_wms_psl, psl_03],
                run_function_path="verarbeitung.src.tables.wms_02.wms_02",
                run_function_name="run",
                tables=["wmsti_psl_001", "sm_auftrag_psl_allgemein"],
                task_type=[TaskType.WFMT, TaskType.BI4PSL],
                max_package_size=10000,
                min_package_size=10000,
            )
        )

        wms_04 = self.add_task(
            TaskConfig(
                name="wms_4_arbeitsplan",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[wms_01, basis_1_wms_psl, processing_bafa_by_sma],
                run_function_path="verarbeitung.src.tables.wms_04.wms_4",
                run_function_name="run",
                tables=["wmsti_psl_001", "psl_geschaeftsfall", "statusergaenzungen", "referenz_gewerke_ausnahme"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=20000,
                min_package_size=10000,
            )
        )

        wms_05 = self.add_task(
            TaskConfig(
                name="wms_5_prodreif",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wms_05.wms_05",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wms_06 = self.add_task(
            TaskConfig(
                name="wms_6_master",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[wms_01],
                run_function_path="verarbeitung.src.tables.wms_06.wms_06",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=10000,
            )
        )

        wms_08 = self.add_task(
            TaskConfig(
                name="wms_8_prio",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wms_08.wms_08",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wms_09 = self.add_task(
            TaskConfig(
                name="wms_9_zusatz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wms_09.wms_09",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wfm_12 = self.add_task(
            TaskConfig(
                name="wfm_12_technisch",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_12.wfm_12",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wfm_13 = self.add_task(
            TaskConfig(
                name="wfm_13_storno",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_13.wfm_13",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wfm_14 = self.add_task(
            TaskConfig(
                name="wfm_14_kunde",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_14.wfm_14",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wfm_15 = self.add_task(
            TaskConfig(
                name="wfm_15_asp",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_15.wfm_15",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wfm_16 = self.add_task(
            TaskConfig(
                name="wfm_16_termine_mhl",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_16.wfm_16",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wfm_17 = self.add_task(
            TaskConfig(
                name="wfm_17_nvt",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_17_nvt.wfm_17_nvt",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        ueweg_1 = self.add_task(
            TaskConfig(
                name="ueweg_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[prepare_processing_ba, basis_1_wms_psl],
                run_function_path="verarbeitung.src.tables.ueweg_1_allgemein.ueweg_1_allgemein",
                run_function_name="run",
                tables=["wmsti_psl_001", "ref_ende_ue_wege", "ref_prod_stoer_tafel", "ref_af_container"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        wms_10 = self.add_task(
            TaskConfig(
                name="wms_10_steuernd",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[basis_1_wms_psl, wms_01, wms_02, ueweg_1, wms_04],
                tables=["sm_auftrag_psl_allgemein", "ref_ni_schema"],
                run_function_path="verarbeitung.src.tables.wms_10.wms_10",
                run_function_name="run",
                # task_type=[TaskType.WFMT, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=5000,
                min_package_size=1000,
            )
        )

        phasen_2 = self.add_task(
            TaskConfig(
                name="phasen_2_diagnose",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_tagbs_pabgs],
                run_function_path="verarbeitung.src.tables.phasen_02.phasen_2_diagnose",
                run_function_name="run",
                tables=["statusergaenzungen", "wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        phasen_0_arbeitsplan = self.add_task(
            TaskConfig(
                name="phasen_0_arbeitsplan",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.phasen_0.phasen_0_arbeitsplan",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge", "processing_bafa"],
                task_type=[TaskType.WFMT],
            )
        )

        phasen_3 = self.add_task(
            TaskConfig(
                name="phasen_3_angebot",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_03.phasen_03_angebot",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
            )
        )

        phasen_4 = self.add_task(
            TaskConfig(
                name="phasen_4_projektierung",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_04.phasen_4_projektierung",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
            )
        )

        phasen_5 = self.add_task(
            TaskConfig(
                name="phasen_5_realisierung",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_05.phasen_5_realisierung",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
            )
        )

        phasen_6 = self.add_task(
            TaskConfig(
                name="phasen_6_inbetriebnahme",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_06.phasen_6_inbetriebnahme",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
                min_package_size=50000,
            )
        )

        phasen_7 = self.add_task(
            TaskConfig(
                name="phasen_7_dokumentation",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_07.phasen_07_dokumentation",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
                min_package_size=50000,
            )
        )

        phasen_9 = self.add_task(
            TaskConfig(
                name="phasen_9_esel",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_09.phasen_9_esel",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
            )
        )

        phasen_13 = self.add_task(
            TaskConfig(
                name="phasen_13_ausk_status",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[phasen_0_arbeitsplan],
                run_function_path="verarbeitung.src.tables.phasen_13.phasen_13",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.WFMT],
                max_package_size=10000,
            )
        )

        phasen_8 = self.add_task(
            TaskConfig(
                name="phasen_8_abschluss",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    processing_bafa_by_sma,
                    phasen_0_arbeitsplan,
                    phasen_3,
                    phasen_4,
                    phasen_5,
                    phasen_6,
                    phasen_7,
                ],
                run_function_path="verarbeitung.src.tables.phasen_08.phasen_8_abschluss",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen", "iwan_ap_serg_reihenfolge"],
                # task_type=[TaskType.WFMT, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=200000,
                min_package_size=50000,
            )
        )

        phasen_12 = self.add_task(
            TaskConfig(
                name="phasen_12_pslserg",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_by_sma, phasen_2, phasen_8, psl_04, basis_1_wms_psl],
                run_function_path="verarbeitung.src.tables.phasen_12.phasen_12",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        phasen_10 = self.add_task(
            TaskConfig(
                name="phasen_10_gefaehrdet",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, wms_01, wms_02, wms_04, phasen_7],
                run_function_path="verarbeitung.src.tables.phasen_10.phasen_10_gefaehrdet",
                run_function_name="run",
                tables=["iwan_ap_serg_reihenfolge", "iwan_bezugsdauer"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=200000,
            )
        )

        phasen_11 = self.add_task(
            TaskConfig(
                name="phasen_11_sergaktuell",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    phasen_2,
                    phasen_3,
                    phasen_4,
                    phasen_5,
                    phasen_6,
                    phasen_7,
                    phasen_8,
                    phasen_9,
                    phasen_12,
                ],
                run_function_path="verarbeitung.src.tables.phasen_11.phasen_11_serg_aktuell",
                run_function_name="run",
                tables=["statusergaenzungen", "wmsti_psl_001"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        phasen_01 = self.add_task(
            TaskConfig(
                name="phasen_1_daprida",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    processing_bafa_by_sma,
                    phasen_0_arbeitsplan,
                    phasen_3,
                    phasen_4,
                    phasen_5,
                    phasen_6,
                    phasen_7,
                    phasen_8,
                    phasen_9,
                    phasen_12,
                ],
                run_function_path="verarbeitung.src.tables.phasen_01.phasen_1_daprida",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        phasen_02_lz = self.add_task(
            TaskConfig(
                name="phasen_2_diagnose_lz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_2],
                run_function_path="verarbeitung.src.tables.phasen_02.phasen_2_diagnose_lz",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        phasen_03_lz = self.add_task(
            TaskConfig(
                name="phasen_3_angebot_lz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_3],
                run_function_path="verarbeitung.src.tables.phasen_03.phasen_3_angebot_lz",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        phasen_04_lz = self.add_task(
            TaskConfig(
                name="phasen_4_projektierung_lz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_4],
                run_function_path="verarbeitung.src.tables.phasen_04.phasen_4_projektierung_lz",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        phasen_05_lz = self.add_task(
            TaskConfig(
                name="phasen_5_realisierung_lz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_5],
                run_function_path="verarbeitung.src.tables.phasen_05.phasen_5_realisierung_lz",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        phasen_06_lz = self.add_task(
            TaskConfig(
                name="phasen_6_inbetriebnahme_lz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_6],
                run_function_path="verarbeitung.src.tables.phasen_06.phasen_6_inbetriebnahme_lz",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        phasen_07_lz = self.add_task(
            TaskConfig(
                name="phasen_7_dokumentation_lz",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_7],
                run_function_path="verarbeitung.src.tables.phasen_07.phasen_7_dokumentation_lz",
                run_function_name="run",
                tables=["wmsti_psl_001", "statusergaenzungen"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        wms_03 = self.add_task(
            TaskConfig(
                name="wms_3_lieferzeit",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[processing_bafa_complete_without_agbs_pabgs, phasen_8],
                run_function_path="verarbeitung.src.tables.wms_03.wms_3_lieferzeit",
                run_function_name="run",
                tables=["statusergaenzungen", "wmsti_psl_001"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
                min_package_size=100000,
            )
        )

        prod_steht_1 = self.add_task(
            TaskConfig(
                name="prod_steht_1",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.prod_steht_1.prod_steht_1",
                run_function_name="run",
                tables=["wmsti_psl_001", "wfmt_ps"],
                task_type=[TaskType.WFMT],
            )
        )

        wms_07 = self.add_task(
            TaskConfig(
                name="wms_7_gefaehrdet",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    processing_bafa_complete_without_agbs_tagbs_pabgs,
                    phasen_2,
                    phasen_8,
                    phasen_10,
                    prod_steht_1,
                ],
                run_function_path="verarbeitung.src.tables.wms_07.wms_7_gefaehrdet",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.BI4PSL],
                max_package_size=200000,
            )
        )

        nap_1_processing_bafa = self.add_task(
            TaskConfig(
                name="nap_1_processing_bafa",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="bafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.nap_1_processing_bafa.nap_1_processing_bafa",
                run_function_name="run",
                tables=["wmsti_psl_001"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        processing_bafa_vrt_termin = self.add_task(
            TaskConfig(
                name="processing_bafa_vrt",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="bafa")],
                dependend_tasks=[wms_06, wms_01],
                run_function_path="verarbeitung.src.tables.processing_bafa_vrt_termin.processing_bafa_vrt_termin",
                run_function_name="run",
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        vrt_termin = self.add_task(
            TaskConfig(
                name="vrt_termin",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    wms_03,
                    wms_04,
                    wms_06,
                    ueweg_1,
                    basis_1_wms_psl,
                    phasen_12,
                    processing_bafa_vrt_termin,
                ],
                run_function_path="verarbeitung.src.tables.vrt_termin.vrt_termin",
                run_function_name="run",
                tables=["wmsti_psl_001", "uewege_lsz_ausnahme"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=10000,
                min_package_size=10000,
            )
        )

        nap_1 = self.add_task(
            TaskConfig(
                name="nap_1_analyse",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    phasen_01,
                    phasen_2,
                    phasen_3,
                    phasen_4,
                    phasen_5,
                    phasen_6,
                    phasen_7,
                    phasen_8,
                    nap_1_processing_bafa,
                    wms_04,
                ],
                run_function_path="verarbeitung.src.tables.nap_1.nap_1",
                run_function_name="run",
                tables=[
                    "wmsti_psl_001",
                    "statusergaenzungen",
                    "iwan_projektgruppe",
                    "iwan_bezugsdauer",
                    "iwan_ap_serg_reihenfolge",
                ],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        nap_full = self.add_task(
            TaskConfig(
                name="nap_full",
                maincolumn=[],
                dependend_tasks=[nap_1, basis_1_wms_psl],
                run_function_path="verarbeitung.src.tables.nap_full.nap_full",
                run_function_name="run",
                tables=["ref_ni_schema", "iwan_projektgruppe"],
                # task_type=[TaskType.WFMT, TaskType.IWAN, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                num_processes=0,
            )
        )

        fin_1_berechnet = self.add_task(
            TaskConfig(
                name="fin_1_berechnet",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                run_function_path="verarbeitung.src.tables.fin_1_berechnet.fin_1_berechnet",
                run_function_name="run",
                dependend_tasks=[psl_06],
                task_type=[TaskType.BI4PSL, TaskType.DWH],
                # task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        fr_1_wms = self.add_task(
            TaskConfig(
                name="fr_1_wms",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[
                    basis_1_wms_psl,
                    processing_bafa_by_sma,
                    wms_04,
                    wms_02,
                    ueweg_1,
                    psl_02,
                    psl_05,
                    psl_04,
                ],
                run_function_path="verarbeitung.src.tables.fr_1_wms.fr_1_wms",
                run_function_name="run",
                tables=["wmsti_psl_001", "ref_fr_ivh", "iwan_bezugsdauer"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        gf_1_allgemein = self.add_task(
            TaskConfig(
                name="gf_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.gf_1_allgemein.gf_1_allgemein",
                run_function_name="run",
                tables=["iwan_gf_pgz_detail", "psl_geschaeftsfall"],
                # task_type=[TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        gf_2_detail = self.add_task(
            TaskConfig(
                name="gf_2_detail",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_gf")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.gf_2_detail.gf_2_detail",
                run_function_name="run",
                tables=["iwan_gf_pgz_detail", "psl_geschaeftsfall"],
                # task_type=[TaskType.BI4PSL, TaskType.IWAN],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        mega_1_techabschl = self.add_task(
            TaskConfig(
                name="mega_1_techabschl",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.mega_1_techabschl.mega_1_techabschl",
                run_function_name="run",
                tables=["linientechnische_veraenderung"],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        fr_2_psl = self.add_task(
            TaskConfig(
                name="fr_2_psl",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[
                    wms_04,
                    basis_1_wms_psl,
                    processing_bafa_by_sma,
                    processing_bafa_complete_without_agbs_pabgs,
                    wms_10,
                    prod_steht_1,
                    psl_05,
                    fr_1_wms,
                    fin_1_berechnet,
                    mega_1_techabschl,
                ],
                run_function_path="verarbeitung.src.tables.fr_2_psl.fr_2_psl",
                run_function_name="run",
                tables=["wmsti_psl_001", "sm_auftrag_psl_allgemein"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN, TaskType.DWH],
                task_type=[TaskType.BI4PSL],
                max_package_size=100000,
            )
        )

        fr_tag = self.add_task(
            TaskConfig(
                name="fr_tag",
                maincolumn=[],
                # make sure fr_tag runs AFTER potentially big processing steps to prevent MemoryErrors
                dependend_tasks=[
                    fr_2_psl,
                    processing_bafa_by_sma,
                    basis_1_wms_psl,
                    psl_05,
                    nap_full,
                    wms_03,
                    phasen_11,
                    wms_10,
                ],
                run_function_path="verarbeitung.src.tables.fr_tag.fr_tag",
                run_function_name="run",
                tables=["ref_ni_schema", "sm_auftrag_psl_allgemein"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN, TaskType.DWH],
                task_type=[TaskType.BI4PSL],
                num_processes=0,
            )
        )
        fr_tag_fin = self.add_task(
            TaskConfig(
                name="fr_tag_fin",
                maincolumn=[],
                # make sure fr_tag runs AFTER potentially big processing steps to prevent MemoryErrors
                dependend_tasks=[
                    fr_2_psl,
                    processing_bafa_by_sma,
                    basis_1_wms_psl,
                    psl_05,
                    nap_full,
                    wms_03,
                    phasen_11,
                    wms_10,
                    fin_1_berechnet,
                ],
                run_function_path="verarbeitung.src.tables.fr_tag_fin.fr_tag_fin",
                run_function_name="run",
                tables=["ref_ni_schema", "sm_auftrag_psl_allgemein"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL, TaskType.IWAN, TaskType.DWH],
                task_type=[TaskType.BI4PSL],
                num_processes=0,
            )
        )

        b2b_zele_basis_del_flag = self.add_task(
            TaskConfig(
                name="b2b_zele_basis_del",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.b2b_zele_basis.b2b_zele_basis_del_flag",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        b2b_1_allgemein_del_flag = self.add_task(
            TaskConfig(
                name="b2b_1_allgemein_del",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.b2b_1_allgemein.b2b_1_allgemein_del_flag",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        b2b_2_termine_del_flag = self.add_task(
            TaskConfig(
                name="b2b_2_termine_del",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.b2b_2_termine.b2b_2_termine_del_flag",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        prepare_processing_arbeitsauftrags_id_tables_del_flag = self.add_task(
            TaskConfig(
                name="processing_arbeitsauftrags_id_del",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.prepare_processing_tables.prepare_processing_arbeitsauftrags_id_tables_with_del_flag",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        b2b_zele_basis = self.add_task(
            TaskConfig(
                name="b2b_zele_basis",
                maincolumn=[IndexColumn(index_type=IndexType.aaid, column="aa_id")],
                dependend_tasks=[
                    manual_processing_tables,
                    prepare_processing_arbeitsauftrags_id_tables_del_flag,
                    b2b_zele_basis_del_flag,
                ],
                run_function_path="verarbeitung.src.tables.b2b_zele_basis.b2b_zele_basis",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        b2b_1_allgemein = self.add_task(
            TaskConfig(
                name="b2b_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.aaid, column="aa_id")],
                dependend_tasks=[
                    manual_processing_tables,
                    prepare_processing_arbeitsauftrags_id_tables_del_flag,
                    b2b_1_allgemein_del_flag,
                ],
                run_function_path="verarbeitung.src.tables.b2b_1_allgemein.b2b_1_allgemein",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        b2b_2_termine = self.add_task(
            TaskConfig(
                name="b2b_2_termine",
                maincolumn=[IndexColumn(index_type=IndexType.aaid, column="aa_id")],
                dependend_tasks=[
                    manual_processing_tables,
                    prepare_processing_arbeitsauftrags_id_tables_del_flag,
                    b2b_2_termine_del_flag,
                ],
                run_function_path="verarbeitung.src.tables.b2b_2_termine.b2b_2_termine",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        b2b_3_rv_del_flag = self.add_task(
            TaskConfig(
                name="b2b_3_rv_del",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.b2b_3_rv.b2b_3_rv_del_flag",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                num_processes=0,
            )
        )

        b2b_3_rv = self.add_task(
            TaskConfig(
                name="b2b_3_rv",
                maincolumn=[IndexColumn(index_type=IndexType.aaid, column="aa_id")],
                dependend_tasks=[
                    manual_processing_tables,
                    prepare_processing_arbeitsauftrags_id_tables_del_flag,
                    b2b_3_rv_del_flag,
                ],
                run_function_path="verarbeitung.src.tables.b2b_3_rv.b2b_3_rv",
                run_function_name="run",
                tables=["b2b_zele_fa"],
                task_type=[TaskType.WFMT],
                max_package_size=100000,
            )
        )

        bbt_basis = self.add_task(
            TaskConfig(
                name="bbt_basis",
                maincolumn=[IndexColumn(index_type=IndexType.bbkid, column="bbk_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bbt_basis.bbt_basis",
                run_function_name="run",
                task_type=[TaskType.BBT],
                tables=["bbt_auftragsdaten"],
                max_package_size=100000,
            )
        )

        bbt_allgemein = self.add_task(
            TaskConfig(
                name="bbt_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.bbkid, column="bbk_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bbt_allgemein.bbt_allgemein",
                run_function_name="run",
                task_type=[TaskType.BBT],
                tables=["bbt_auftragsdaten"],
                max_package_size=100000,
            )
        )

        bbt_baustelle = self.add_task(
            TaskConfig(
                name="bbt_baustelle",
                maincolumn=[IndexColumn(index_type=IndexType.bbkid, column="bbk_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bbt_baustelle.bbt_baustelle",
                run_function_name="run",
                task_type=[TaskType.BBT],
                tables=["bbt_auftragsdaten"],
                max_package_size=100000,
            )
        )

        bbt_termine = self.add_task(
            TaskConfig(
                name="bbt_termine",
                maincolumn=[IndexColumn(index_type=IndexType.bbkid, column="bbk_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bbt_termine.bbt_termine",
                run_function_name="run",
                task_type=[TaskType.BBT],
                tables=["bbt_auftragsdaten"],
                max_package_size=100000,
            )
        )

        bzm_detail = self.add_task(
            TaskConfig(
                name="bzm_1_detail",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bzm_1_detail.bzm_1_detail",
                run_function_name="run",
                task_type=[TaskType.BZM],
                tables=["bzm_light_mengen"],
                max_package_size=100000,
            )
        )

        bzm_2_summen = self.add_task(
            TaskConfig(
                name="bzm_2_summen",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_psl")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bzm_2_summen.bzm_2_summen",
                run_function_name="run",
                task_type=[TaskType.BZM],
                tables=["bzm_light_mengen", "bzm_light_termine", "ref_kreditor"],
                max_package_size=100000,
            )
        )

        betr_korr_1_historie = self.add_task(
            TaskConfig(
                name="betr_korr_1_historie",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.betr_korr_1_historie.betr_korr_1_historie",
                run_function_name="run",
                tables=["wmsti_psl_001", "wfmt_betr_korr", "wfmt_vzg_hist"],
                task_type=[TaskType.WFMT],
            )
        )

        betr_korr_2_auftrag = self.add_task(
            TaskConfig(
                name="betr_korr_2_auftrag",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.betr_korr_2_auftrag.betr_korr_2_auftrag",
                run_function_name="run",
                tables=["wmsti_psl_001", "wfmt_betr_korr", "workflow_auftrag_zeitpunkte", "constants"],
                # task_type=[TaskType.WFMT, TaskType.BI4PSL],
                task_type=[TaskType.BI4PSL],
            )
        )
        betr_korr_3_1vzg = self.add_task(
            TaskConfig(
                name="betr_korr_3_1vzg",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.betr_korr_3_1vzg.betr_korr_3_1vzg",
                run_function_name="run",
                tables=["wmsti_psl_001", "wfmt_betr_korr", "wfmt_vzg_hist", "constants"],
                task_type=[TaskType.WFMT],
            )
        )

        kollo_basis = self.add_task(
            TaskConfig(
                name="kollo_basis",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.kollo_basis.kollo_basis",
                run_function_name="run",
                tables=["kollo_offene_auftraege"],
                task_type=[TaskType.KOLLODB],
            )
        )

        kollo_offene_auftr = self.add_task(
            TaskConfig(
                name="kollo_offene_auftr",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.kollo_offene_auftr.kollo_offene_auftr",
                run_function_name="run",
                tables=["kollo_offene_auftraege"],
                task_type=[TaskType.KOLLODB],
            )
        )

        bbt_akt_aktiv = self.add_task(
            TaskConfig(
                name="bbt_akt_aktiv",
                maincolumn=[IndexColumn(index_type=IndexType.bbkid, column="bbk_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.bbt_akt_aktiv.bbt_akt_aktiv",
                run_function_name="run",
                task_type=[TaskType.BBT],
                tables=["bbt_aktivitaeten"],
                max_package_size=100000,
            )
        )

        trom_1_beweg = self.add_task(
            TaskConfig(
                name="trom_1_beweg",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.trom_1_bewegt.trom_1_bewegt",
                run_function_name="run",
                task_type=[TaskType.TROMMEL],
                tables=["trommelbeweg"],
            )
        )

        trom_2_verlust = self.add_task(
            TaskConfig(
                name="trom_2_verlust",
                maincolumn=[],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.trom_2_verlust.trom_2_verlust",
                run_function_name="run",
                task_type=[TaskType.TROMMEL],
                tables=["trom_matcharge"],
            )
        )
        trom_basis_bewegt = self.add_task(
            TaskConfig(
                name="trom_basis_bewegt",
                maincolumn=[],
                dependend_tasks=[trom_1_beweg],
                run_function_path="verarbeitung.src.tables.trom_basis.trom_basis",
                run_function_name="run_trom1",
                task_type=[TaskType.TROMMEL],
                tables=["trommelbeweg"],
            )
        )

        trom_basis_verlust = self.add_task(
            TaskConfig(
                name="trom_basis_verlust",
                maincolumn=[],
                dependend_tasks=[trom_2_verlust],
                run_function_path="verarbeitung.src.tables.trom_basis.trom_basis",
                run_function_name="run_trom2",
                task_type=[TaskType.TROMMEL],
                tables=["trom_matcharge"],
            )
        )

        trom_3_status = self.add_task(
            TaskConfig(
                name="trom_3_status",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="trom_sma")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.trom_3_status.trom_3_status",
                run_function_name="run",
                task_type=[TaskType.TROMMEL],
                tables=["trommelbeweg"],
            )
        )

        trom_4_sma_anzahl = self.add_task(
            TaskConfig(
                name="trom_4_sma_anzahl",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma_mit_trom")],
                dependend_tasks=[trom_3_status, trom_2_verlust],
                run_function_path="verarbeitung.src.tables.trom_4_sma_anzahl.trom_4_sma_anzahl",
                run_function_name="run",
                task_type=[TaskType.TROMMEL],
                tables=["trommelbeweg", "trom_matcharge"],
                min_package_size=10000,
                max_package_size=20000,
            )
        )

        gbgs_fol = self.add_task(
            TaskConfig(
                name="gbgs_fol",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.gbgs_fol.gbgs_fol",
                run_function_name="run",
                tables=["ggs_dtag_fol", "ggs_gfp_rop_fol"],
                task_type=[TaskType.GBGS],
            )
        )

        ggs_bauaufgabe = self.add_task(
            TaskConfig(
                name="ggs_bauaufgabe",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.ggs_bauaufgabe.ggs_bauaufgabe",
                run_function_name="run",
                tables=["ggs_dtag_bauaufgabe", "ggs_gfp_rop_bauaufgabe"],
                task_type=[TaskType.GBGS],
            )
        )

        gbgs_nvt_area = self.add_task(
            TaskConfig(
                name="gbgs_nvt_area",
                maincolumn=[IndexColumn(index_type=IndexType.area_nr, column="area_nr")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.gbgs_nvt_area.gbgs_nvt_area",
                run_function_name="run",
                tables=["ggs_dtag_nvt_area", "ggs_gfp_rop_nvt_area"],
                task_type=[TaskType.GBGS],
            )
        )

        gbgs_cio = self.add_task(
            TaskConfig(
                name="gbgs_cio",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.gbgs_cio.gbgs_cio",
                run_function_name="run",
                tables=["ggs_dtag_cio", "ggs_gfp_rop_cio"],
                task_type=[TaskType.GBGS],
            )
        )

        gbgs_bulk = self.add_task(
            TaskConfig(
                name="gbgs_bulk",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.gbgs_bulk.gbgs_bulk",
                run_function_name="run",
                tables=["ggs_dtag_bulk", "ggs_gfp_rop_bulk"],
                task_type=[TaskType.GBGS],
            )
        )

        gbgs_6_ba_ne3_4 = self.add_task(
            TaskConfig(
                name="gbgs_6_ba_ne3_4",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_6_ba_ne3_4.gbgs_6_ba_ne3_4",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_basis_3_fol_id = self.add_task(
            TaskConfig(
                name="gbgs_basis_3_fol_id",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_basis_3_fol_id.gbgs_basis_3_fol_id",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_basis_1_area = self.add_task(
            TaskConfig(
                name="gbgs_basis_1_area",
                maincolumn=[IndexColumn(index_type=IndexType.klsid, column="kls_id")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.gbgs_basis_1_area.gbgs_basis_1_area",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_fol_log1_area_status = self.add_task(
            TaskConfig(
                name="ggs_fol_log1_area_status",
                maincolumn=[IndexColumn(index_type=IndexType.area_nr, column="area_nr")],
                dependend_tasks=[gbgs_fol, gbgs_cio, gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_fol_log1_area_status.ggs_fol_log1_area_status",
                run_function_name="run",
                tables=["gbgs_cio", "gbgs_bulk", "gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_5_ba_allgemein = self.add_task(
            TaskConfig(
                name="gbgs_5_ba_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_5_ba_allgemein.gbgs_5_ba_allgemein",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_7_ba_wo_ausk = self.add_task(
            TaskConfig(
                name="gbgs_7_ba_wo_ausk",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_7_ba_wo_ausk.gbgs_7_ba_wo_ausk",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_8_ba_wo_ne3 = self.add_task(
            TaskConfig(
                name="gbgs_8_ba_wo_ne3",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_8_ba_wo_ne3.gbgs_8_ba_wo_ne3",
                run_function_name="run",
                task_type=[TaskType.GBGS],
                tables=["gbgs_cio"],
                num_processes=0,
            )
        )

        gbgs_9_ba_wo_ne4 = self.add_task(
            TaskConfig(
                name="gbgs_9_ba_wo_ne4",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_9_ba_wo_ne4.gbgs_9_ba_wo_ne4",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_1_area_status = self.add_task(
            TaskConfig(
                name="gbgs_1_area_status",
                maincolumn=[IndexColumn(index_type=IndexType.area_nr, column="area_nr")],
                dependend_tasks=[ggs_fol_log1_area_status, gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_1_area_status.gbgs_1_area_status",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_21_fol_area = self.add_task(
            TaskConfig(
                name="gbgs_21_fol_area",
                maincolumn=[IndexColumn(index_type=IndexType.area_nr, column="area_nr")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.gbgs_21_fol_area.gbgs_21_fol_area",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_22_fol_detail = self.add_task(
            TaskConfig(
                name="gbgs_22_fol_detail",
                maincolumn=[IndexColumn(index_type=IndexType.area_nr, column="area_nr")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.gbgs_22_fol_detail.gbgs_22_fol_detail",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_23_supplier = self.add_task(
            TaskConfig(
                name="gbgs_23_supplier",
                maincolumn=[IndexColumn(index_type=IndexType.supplier_partyid, column="supplier_partyid")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.gbgs_23_supplier.gbgs_23_supplier",
                run_function_name="run",
                tables=["gbgs_supplier"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_24_ba_allgemein2 = self.add_task(
            TaskConfig(
                name="gbgs_24_ba_allgemein2",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[gbgs_cio],
                run_function_path="verarbeitung.src.tables.gbgs_24_ba_allgemein2.gbgs_24_ba_allgemein2",
                run_function_name="run",
                tables=["gbgs_cio"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        kio_1_geo = self.add_task(
            TaskConfig(
                name="kio_1_geo",
                maincolumn=[IndexColumn(index_type=IndexType.klsid, column="kls_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.kio_1_geo.kio_1_geo",
                run_function_name="run",
                tables=["adressen_inland"],
                task_type=[TaskType.KLS],
            )
        )

        kio_2_onkz_asb = self.add_task(
            TaskConfig(
                name="kio_2_onkz_asb",
                maincolumn=[IndexColumn(index_type=IndexType.klsid, column="kls_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.kio_2_onkz_asb.kio_2_onkz_asb",
                run_function_name="run",
                tables=["adressen_inland"],
                task_type=[TaskType.KLS],
            )
        )

        kio_3_adresse_inl = self.add_task(
            TaskConfig(
                name="kio_3_adresse_inl",
                maincolumn=[IndexColumn(index_type=IndexType.klsid, column="kls_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.kio_3_adresse_inl.kio_3_adresse_inl",
                run_function_name="run",
                tables=["adressen_inland"],
                task_type=[TaskType.KLS],
            )
        )

        wfm_11 = self.add_task(
            TaskConfig(
                name="wfm_11_montageort",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[kio_3_adresse_inl, manual_processing_tables],
                run_function_path="verarbeitung.src.tables.wfm_11.wfm_11",
                run_function_name="run",
                task_type=[TaskType.WFMT, TaskType.KLS],
                tables=["wmsti_psl_001"],
                max_package_size=500000,
                num_processes=1,
            )
        )

        kio_basis_1 = self.add_task(
            TaskConfig(
                name="kio_basis_1",
                maincolumn=[IndexColumn(index_type=IndexType.klsid, column="kls_id")],
                dependend_tasks=[manual_processing_tables],
                run_function_path="verarbeitung.src.tables.kio_basis_1.kio_basis_1",
                run_function_name="run",
                tables=["adressen_inland"],
                task_type=[TaskType.KLS],
            )
        )

        gbgs_25_nvt_lang = self.add_task(
            TaskConfig(
                name="gbgs_25_nvt_lang",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[gbgs_fol, kio_2_onkz_asb],
                run_function_path="verarbeitung.src.tables.gbgs_25_nvt_lang.gbgs_25_nvt_lang",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_26_baa_allgem = self.add_task(
            TaskConfig(
                name="gbgs_26_baa_allgem",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_26_baa_allgem.gbgs_26_baa_allgem",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_27_baa_allgem_2 = self.add_task(
            TaskConfig(
                name="gbgs_27_baa_allgem_2",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_27_baa_allgem_2.gbgs_27_baa_allgem_2",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_28_baa_gebaude_stich = self.add_task(
            TaskConfig(
                name="gbgs_28_baa_gebaude_stich",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_28_baa_gebaude_stich.gbgs_28_baa_gebaude_stich",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_29_baa_vzk_einbringen = self.add_task(
            TaskConfig(
                name="gbgs_29_baa_vzk_einbringen",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_29_baa_vzk_einbringen.gbgs_29_baa_vzk_einbringen",
                run_function_name="run",
                tables=["ggs_bauafgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_30_baa_nvt_spleiss = self.add_task(
            TaskConfig(
                name="gbgs_30_baa_nvt_spleiss",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_30_baa_nvt_spleiss.gbgs_30_baa_nvt_spleiss",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_31_baa_gfap = self.add_task(
            TaskConfig(
                name="gbgs_31_baa_gfap",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_31_baa_gfap.gbgs_31_baa_gfap",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_32_baa_auskundung = self.add_task(
            TaskConfig(
                name="gbgs_32_baa_auskundung",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_32_baa_auskundung.gbgs_32_baa_auskundung",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        gbgs_33_baa_steigleitung = self.add_task(
            TaskConfig(
                name="gbgs_33_baa_steigleitung",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.gbgs_33_baa_steigleitung.gbgs_33_baa_steigleitung",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        wfm_ggs_1_area_processing = self.add_task(
            TaskConfig(
                name="wfm_ggs_1_area_processing",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[wfm_11, gbgs_cio, gbgs_bulk],
                run_function_path="verarbeitung.src.tables.wfm_ggs_1_area.wfm_ggs_1_area",
                run_function_name="fill_processing_table",
                tables=["gbgs_cio", "gbgs_fol", "gbgs_bulk", "wmsti_psl_001"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
            )
        )

        wfm_ggs_1_area_klsid_a = self.add_task(
            TaskConfig(
                name="wfm_ggs_1_area_klsid_a",
                maincolumn=[],
                dependend_tasks=[wfm_ggs_1_area_processing, gbgs_basis_1_area, gbgs_cio, gbgs_bulk],
                run_function_path="verarbeitung.src.tables.wfm_ggs_1_area.wfm_ggs_1_area",
                run_function_name="run_a",
                tables=["gbgs_cio", "gbgs_fol", "gbgs_bulk", "wmsti_psl_001"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
                min_package_size=5,
                max_package_size=50000,
            )
        )

        wfm_ggs_1_area_klsid_mont_a = self.add_task(
            TaskConfig(
                name="wfm_ggs_1_area_klsid_mont_a",
                maincolumn=[],
                dependend_tasks=[wfm_ggs_1_area_processing, gbgs_basis_1_area, gbgs_cio],
                run_function_path="verarbeitung.src.tables.wfm_ggs_1_area.wfm_ggs_1_area",
                run_function_name="run_mont_a",
                tables=["gbgs_cio", "gbgs_fol", "gbgs_bulk", "wmsti_psl_001"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
            )
        )

        wfm_ggs_1_area_pre = self.add_task(
            TaskConfig(
                name="wfm_ggs_1_area_pre",
                maincolumn=[],
                dependend_tasks=[wfm_ggs_1_area_klsid_a, wfm_ggs_1_area_klsid_mont_a, gbgs_cio, gbgs_bulk],
                run_function_path="verarbeitung.src.tables.wfm_ggs_1_area.wfm_ggs_1_area",
                run_function_name="run",
                tables=["gbgs_cio", "gbgs_fol", "gbgs_bulk", "wmsti_psl_001"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
                max_package_size=50000,
            )
        )

        wfm_ggs_1_area = self.add_task(
            TaskConfig(
                name="wfm_ggs_1_area",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[wfm_ggs_1_area_pre],
                run_function_path="verarbeitung.src.tables.wfm_ggs_1_area.wfm_ggs_1_area",
                run_function_name="run_kaskade",
                tables=["gbgs_cio", "gbgs_fol", "gbgs_bulk", "wmsti_psl_001"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
                min_package_size=5,
                max_package_size=10000,
            )
        )

        wfm_ggs_2_nvt_area_pre = self.add_task(
            TaskConfig(
                name="wfm_ggs_2_nvt_area_pre",
                maincolumn=[],
                dependend_tasks=[wfm_17, gbgs_25_nvt_lang, wfm_ggs_1_area, gbgs_cio],
                run_function_path="verarbeitung.src.tables.wfm_ggs_2_nvt_area.wfm_ggs_2_nvt_area",
                run_function_name="run",
                tables=["wmsti_psl_001", "gbgs_fol"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
                max_package_size=50000,
            )
        )

        wfm_ggs_2_nvt_area = self.add_task(
            TaskConfig(
                name="wfm_ggs_2_nvt_area",
                maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
                dependend_tasks=[wfm_ggs_2_nvt_area_pre],
                run_function_path="verarbeitung.src.tables.wfm_ggs_2_nvt_area.wfm_ggs_2_nvt_area",
                run_function_name="run_nvt_area_wfm_ggs",
                tables=["wmsti_psl_001", "gbgs_fol"],
                task_type=[TaskType.GBGS, TaskType.WFMT],
            )
        )

        ggs_basis_1_fol_pre = self.add_task(
            TaskConfig(
                name="ggs_basis_1_fol_pre",
                maincolumn=[],
                dependend_tasks=[gbgs_fol, gbgs_bulk, ggs_bauaufgabe, gbgs_cio],
                run_function_path="verarbeitung.src.tables.ggs_basis_1_fol.ggs_basis_1_fol",
                run_function_name="run_pre",
                tables=["gbgs_fol", "gbgs_cio", "gbgs_bulk", "ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_basis_1_fol = self.add_task(
            TaskConfig(
                name="ggs_basis_1_fol",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[gbgs_bulk, ggs_basis_1_fol_pre, ggs_bauaufgabe, gbgs_cio],
                run_function_path="verarbeitung.src.tables.ggs_basis_1_fol.ggs_basis_1_fol",
                run_function_name="run",
                tables=["gbgs_fol", "gbgs_cio", "gbgs_bulk", "ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                max_package_size=100000,
            )
        )

        ggs_fol_log2_ausbau_status = self.add_task(
            TaskConfig(
                name="ggs_fol_log2_ausbau_status",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.ggs_fol_log2_ausbau_status.ggs_fol_log2_ausbau_status",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_fol_3_ausbau_status_processing = self.add_task(
            TaskConfig(
                name="ggs_fol_3_ausbau_status_processing",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[gbgs_fol, ggs_fol_log2_ausbau_status],
                run_function_path="verarbeitung.src.tables.ggs_fol_3_ausbau_status.ggs_fol_3_ausbau_status",
                run_function_name="run_processing",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_fol_3_ausbau_status = self.add_task(
            TaskConfig(
                name="ggs_fol_3_ausbau_status",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_fol_3_ausbau_status_processing],
                run_function_path="verarbeitung.src.tables.ggs_fol_3_ausbau_status.ggs_fol_3_ausbau_status",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
            )
        )

        ggs_nvt_area_1_pre = self.add_task(
            TaskConfig(
                name="ggs_nvt_area_1_pre",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[ggs_fol_3_ausbau_status, gbgs_25_nvt_lang, gbgs_cio],
                run_function_path="verarbeitung.src.tables.ggs_nvt_area_1.ggs_nvt_area_1",
                run_function_name="run_pre",
                tables=["gbgs_fol", "gbgs_cio"],
                task_type=[TaskType.GBGS],
                max_package_size=5000,
            )
        )

        ggs_nvt_area_1 = self.add_task(
            TaskConfig(
                name="ggs_nvt_area_1",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[ggs_nvt_area_1_pre],
                run_function_path="verarbeitung.src.tables.ggs_nvt_area_1.ggs_nvt_area_1",
                run_function_name="run",
                tables=["gbgs_fol", "gbgs_cio"],
                task_type=[TaskType.GBGS],
            )
        )

        hashtag_1_bulk = self.add_task(
            TaskConfig(
                name="hashtag_1_bulk",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="bulk_order_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.hashtag_1_bulk.hashtag_1_bulk",
                run_function_name="run",
                tables=["gbgs_hashtag"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        hashtag_2_ibt_geb = self.add_task(
            TaskConfig(
                name="hashtag_2_ibt_Geb",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.hashtag_2_ibt_geb.hashtag_2_ibt_geb",
                run_function_name="run",
                tables=["gbgs_hashtag"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        hashtag_3_ibt_auft = self.add_task(
            TaskConfig(
                name="hashtag_3_ibt_auft",
                maincolumn=[IndexColumn(index_type=IndexType.ba_nummer, column="ba_nummer")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.hashtag_3_ibt_auft.hashtag_3_ibt_auft",
                run_function_name="run",
                tables=["gbgs_hashtag"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_fol_2_detail = self.add_task(
            TaskConfig(
                name="ggs_fol_2_detail",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.ggs_fol_2_detail.ggs_fol_2_detail",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_fol_1_hhsto = self.add_task(
            TaskConfig(
                name="ggs_fol_1_hhsto",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.ggs_fol_1_hhsto.ggs_fol_1_hhsto",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_basis_2_fol_bulk = self.add_task(
            TaskConfig(
                name="ggs_basis_2_fol_bulk",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_basis_2_fol_bulk.ggs_basis_2_fol_bulk",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_area_1 = self.add_task(
            TaskConfig(
                name="ggs_area_1",
                maincolumn=[IndexColumn(index_type=IndexType.area_nr, column="area_nr")],
                dependend_tasks=[gbgs_fol],
                run_function_path="verarbeitung.src.tables.ggs_area_1.ggs_area_1",
                run_function_name="run",
                tables=["gbgs_fol"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_basis_6_orderid_projbulk = self.add_task(
            TaskConfig(
                name="ggs_basis_6_orderid_projbulk",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_basis_6_orderid_projbulk.ggs_basis_6_orderid_projbulk",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_1_allgemein = self.add_task(
            TaskConfig(
                name="ggs_bulk_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_1_allgemein.ggs_bulk_1_allgemein",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_2_ne3_4 = self.add_task(
            TaskConfig(
                name="ggs_bulk_2_ne3_4",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_2_ne3_4.ggs_bulk_2_ne3_4",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_3_menge = self.add_task(
            TaskConfig(
                name="ggs_bulk_3_menge",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_3_menge.ggs_bulk_3_menge",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_4_ausk = self.add_task(
            TaskConfig(
                name="ggs_bulk_4_ausk",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_4_ausk.ggs_bulk_4_ausk",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_5_dpu = self.add_task(
            TaskConfig(
                name="ggs_bulk_5_dpu",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_5_dpu.ggs_bulk_5_dpu",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_6_ne3 = self.add_task(
            TaskConfig(
                name="ggs_bulk_6_ne3",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_6_ne3.ggs_bulk_6_ne3",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_7_ne4 = self.add_task(
            TaskConfig(
                name="ggs_bulk_7_ne4",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_7_ne4.ggs_bulk_7_ne4",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_8_projekt = self.add_task(
            TaskConfig(
                name="ggs_bulk_8_projekt",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_8_projekt.ggs_bulk_8_projekt",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_9_wg = self.add_task(
            TaskConfig(
                name="ggs_bulk_9_wg",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_9_wg.ggs_bulk_9_wg",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_log4_status_adresse = self.add_task(
            TaskConfig(
                name="ggs_bulk_log4_status_adresse",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_log4_status_adresse.ggs_bulk_log4_status_adresse",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_10_status_adresse = self.add_task(
            TaskConfig(
                name="ggs_bulk_10_status_adresse",
                maincolumn=[IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")],
                dependend_tasks=[gbgs_bulk, ggs_bulk_log4_status_adresse],
                run_function_path="verarbeitung.src.tables.ggs_bulk_10_status_adresse.ggs_bulk_10_status_adresse",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_log3_status_bulk = self.add_task(
            TaskConfig(
                name="ggs_bulk_log3_status_bulk",
                maincolumn=[IndexColumn(index_type=IndexType.projekt_nr, column="proj_nr")],
                dependend_tasks=[gbgs_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_log3_status_bulk.ggs_bulk_log3_status_bulk",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_bulk_11_status_bulk = self.add_task(
            TaskConfig(
                name="ggs_bulk_11_status_bulk",
                maincolumn=[IndexColumn(index_type=IndexType.projekt_nr, column="proj_nr")],
                dependend_tasks=[gbgs_bulk, ggs_bulk_log3_status_bulk],
                run_function_path="verarbeitung.src.tables.ggs_bulk_11_status_bulk.ggs_bulk_11_status_bulk",
                run_function_name="run",
                tables=["gbgs_bulk"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        filter_baa_1_aktuell = self.add_task(
            TaskConfig(
                name="filter_baa_1_aktuell",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[ggs_bauaufgabe],
                run_function_path="verarbeitung.src.tables.filter_baa_1_aktuell.filter_baa_1_aktuell",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_basis_1_nvt = self.add_task(
            TaskConfig(
                name="ggs_basis_1_nvt",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="nvt_area")],
                dependend_tasks=[gbgs_nvt_area],
                run_function_path="verarbeitung.src.tables.ggs_basis_1_nvt.ggs_basis_1_nvt",
                run_function_name="run",
                tables=["gbgs_nvt_area"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_nvt_1_allgemein = self.add_task(
            TaskConfig(
                name="ggs_nvt_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[gbgs_nvt_area],
                run_function_path="verarbeitung.src.tables.ggs_nvt_1_allgemein.ggs_nvt_1_allgemein",
                run_function_name="run",
                tables=["gbgs_nvt_area"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_nvt_2_regel = self.add_task(
            TaskConfig(
                name="ggs_nvt_2_regel",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[gbgs_nvt_area],
                run_function_path="verarbeitung.src.tables.ggs_nvt_2_regel.ggs_nvt_2_regel",
                run_function_name="run",
                tables=["gbgs_nvt_area"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_nvt_3_termine = self.add_task(
            TaskConfig(
                name="ggs_nvt_3_termine",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[gbgs_nvt_area],
                run_function_path="verarbeitung.src.tables.ggs_nvt_3_termine.ggs_nvt_3_termine",
                run_function_name="run",
                tables=["gbgs_nvt_area"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_nvt_4_supplier = self.add_task(
            TaskConfig(
                name="ggs_nvt_4_supplier",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[gbgs_nvt_area],
                run_function_path="verarbeitung.src.tables.ggs_nvt_4_supplier.ggs_nvt_4_supplier",
                run_function_name="run",
                tables=["gbgs_nvt_area"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_nvt_7_anzahl_pa = self.add_task(
            TaskConfig(
                name="ggs_nvt_7_anzahl_pa",
                maincolumn=[IndexColumn(index_type=IndexType.nvt_area, column="nvt_area")],
                dependend_tasks=[gbgs_nvt_area],
                run_function_path="verarbeitung.src.tables.ggs_nvt_7_anzahl_ap.ggs_nvt_7_anzahl_ap",
                run_function_name="run",
                tables=["gbgs_nvt_area"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_baa_08_weges = self.add_task(
            TaskConfig(
                name="ggs_baa_08_weges",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.ggs_baa_08_weges.ggs_baa_08_weges",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_baa_09_kmr = self.add_task(
            TaskConfig(
                name="ggs_baa_09_kmr",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.ggs_baa_09_kmr.ggs_baa_09_kmr",
                run_function_name="run",
                tables=["ggs_bauaufgabe"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        dbbgl_1_valid = self.add_task(
            TaskConfig(
                name="dbbgl_1_valid",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.dbbgl_1_valid_invalid.dbbgl_1_valid_invalid",
                run_function_name="run_valid",
                tables=["dbbgl_1_valid_temp"],
                task_type=[TaskType.DBBGL],
                num_processes=0,
            )
        )

        dbbgl_1_invalid = self.add_task(
            TaskConfig(
                name="dbbgl_1_invalid",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.dbbgl_1_valid_invalid.dbbgl_1_valid_invalid",
                run_function_name="run_invalid",
                tables=["dbbgl_1_invalid_temp"],
                task_type=[TaskType.DBBGL],
                num_processes=0,
            )
        )

        dbbgl_1_menge = self.add_task(
            TaskConfig(
                name="dbbgl_1_menge",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[dbbgl_1_valid],
                run_function_path="verarbeitung.src.tables.dbbgl_1_menge_fehlermenge.dbbgl_1_menge_fehlermenge",
                run_function_name="run_menge",
                tables=["dbbgl_1_valid"],
                task_type=[TaskType.DBBGL],
                num_processes=0,
            )
        )

        dbbgl_1_fehlermenge = self.add_task(
            TaskConfig(
                name="dbbgl_1_fehlermenge",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[dbbgl_1_invalid],
                run_function_path="verarbeitung.src.tables.dbbgl_1_menge_fehlermenge.dbbgl_1_menge_fehlermenge",
                run_function_name="run_fehlermenge",
                tables=["dbbgl_1_invalid"],
                task_type=[TaskType.DBBGL],
                num_processes=0,
            )
        )

        dbbgl_2 = self.add_task(
            TaskConfig(
                name="dbbgl_2",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.dbbgl_2.dbbgl_2",
                run_function_name="run",
                tables=["dbbgl_2"],
                task_type=[TaskType.DBBGLDATA],
                num_processes=0,
            )
        )

        dbbgl_2_1_detailmenge = self.add_task(
            TaskConfig(
                name="dbbgl_2_1_detailmenge",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[dbbgl_2],
                run_function_path="verarbeitung.src.tables.dbbgl_2_1_detailmenge.dbbgl_2_1_detailmenge",
                run_function_name="run",
                tables=["dbbgl_2"],
                task_type=[TaskType.DBBGLDATA],
                num_processes=0,
            )
        )

        dbbgl_2_2_laenge = self.add_task(
            TaskConfig(
                name="dbbgl_2_2_laenge",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[dbbgl_2],
                run_function_path="verarbeitung.src.tables.dbbgl_2_2_laenge.dbbgl_2_2_laenge",
                run_function_name="run",
                tables=["dbbgl_2"],
                task_type=[TaskType.DBBGLDATA],
                num_processes=0,
            )
        )

        dbbgl_2_3_stueck = self.add_task(
            TaskConfig(
                name="dbbgl_2_3_stueck",
                maincolumn=[IndexColumn(index_type=IndexType.sma, column="sma")],
                dependend_tasks=[dbbgl_2],
                run_function_path="verarbeitung.src.tables.dbbgl_2_3_stueck.dbbgl_2_3_stueck",
                run_function_name="run",
                tables=["dbbgl_2"],
                task_type=[TaskType.DBBGLDATA],
                num_processes=0,
            )
        )

        ggs_gfap_1_allgemein = self.add_task(
            TaskConfig(
                name="ggs_gfap_1_allgemein",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.ggs_gfap_1_allgemein.ggs_gfap_1_allgemein",
                run_function_name="run",
                tables=["gbgs_gfap"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )

        ggs_gfap_2_ausbau = self.add_task(
            TaskConfig(
                name="ggs_gfap_2_ausbau",
                maincolumn=[IndexColumn(index_type=IndexType.fol_id, column="fol_id")],
                dependend_tasks=[],
                run_function_path="verarbeitung.src.tables.ggs_gfap_2_ausbau.ggs_gfap_2_ausbau",
                run_function_name="run",
                tables=["gbgs_gfap"],
                task_type=[TaskType.GBGS],
                num_processes=0,
            )
        )


#######################################################################################################################


class DagModules:
    # ERROR_MAIL_RECIPIENTS = None

    def __init__(self):
        self.tasks_dict = {}
        self.build_dag()

    def add_task(self, task):
        """

        :type task: object
        """
        self.tasks_dict[task.name] = task
        return task

    def build_dag(self):

        # get non aggregation tasks from db
        self.tasks_dict = load_modules_from_db()

        for dynamic_task in self.create_dynamic_aggregation_tasks():
            self.add_task(dynamic_task)

    def create_dynamic_aggregation_tasks(self, module=None):
        """

        Returns:

        """

        dynamic_sql_aggregation_data_from_db = get_sql_definitions_from_db_for_dynamic_aggregation_tasks()
        dynamic_aggregation_projects = []

        for dataset in dynamic_sql_aggregation_data_from_db:
            tasks = build_tasks(dataset, self.tasks_dict, module=module)
            dynamic_aggregation_projects += tasks

        return dynamic_aggregation_projects


class InputTableMapping:
    """Additional mapping to cover input tables"""

    mapping = {}
    mapping["sm_auftrag_psl_allgemein"] = [IndexColumn(index_type=IndexType.sma, column="sm_auftragsnummer")]
    mapping["psl_geschaeftsfall"] = [IndexColumn(index_type=IndexType.sma, column="sm_auftragsnummer")]
    mapping["sm_auftrag_psl_finanzen"] = [IndexColumn(index_type=IndexType.sma, column="sm_auftragsnummer")]
    mapping["psl_koa_obligo"] = [IndexColumn(index_type=IndexType.sma, column="sma_psl")]
    mapping["psl_meldungen"] = [IndexColumn(index_type=IndexType.sma, column="sma")]
    mapping["linientechnische_veraenderung"] = [IndexColumn(index_type=IndexType.sma, column="sma")]
    mapping["bzm_light_mengen"] = [IndexColumn(index_type=IndexType.sma, column="sma_psl")]
    mapping["bzm_light_termine"] = [IndexColumn(index_type=IndexType.sma, column="sma_psl")]
    mapping["wmsti_psl_001"] = [IndexColumn(index_type=IndexType.bafa, column="wmsti_requidbafa")]
    mapping["wfmt_bemerkung"] = [IndexColumn(index_type=IndexType.bafa, column="wmsti_requidbafa")]
    mapping["wfmt_betr_korr"] = [IndexColumn(index_type=IndexType.bafa, column="requidbafa")]
    mapping["wfmt_vzg_hist"] = [IndexColumn(index_type=IndexType.bafa, column="requidbafa")]
    mapping["wfmt_ps"] = [IndexColumn(index_type=IndexType.bafa, column="ref_ba")]
    mapping["statusergaenzungen"] = [IndexColumn(index_type=IndexType.bafa, column="ref_bafa_requestid")]
    mapping["workflow_auftrag_zeitpunkte"] = [IndexColumn(index_type=IndexType.bafa, column="twf_auftrag_id")]
    mapping["b2b_zele_fa"] = [IndexColumn(index_type=IndexType.aaid, column="arbeitsauftrags_id")]
    # bbt
    mapping["bbt_aktivitaeten"] = [IndexColumn(index_type=IndexType.bbkid, column="bbk_id")]
    mapping["bbt_auftragsdaten"] = [IndexColumn(index_type=IndexType.bbkid, column="bbk_id")]

    # gbgs
    # mapping["gbgs_cio"] = [IndexColumn(index_type=IndexType.fol_id, column="fol_id")]
    # mapping["gbgs_bulk"] = [IndexColumn(index_type=IndexType.fol_id, column="fol_id")]
    # mapping["gbgs_bulk"] = [IndexColumn(index_type=IndexType.bulk_order_id, column="bulk_order_id")]
    # mapping["gbgs_fol"] = [IndexColumn(index_type=IndexType.fol_id, column="fol_id")]
    mapping["gbgs_supplier"] = [IndexColumn(index_type=IndexType.supplier_partyid, column="supplier_partyid")]
    # mapping["gbgs_bauaufgabe"] = [IndexColumn(index_type=IndexType.fol_id, column="fol_id")]
    # mapping["gbgs_hashtag"] = [IndexColumn(index_type=IndexType.fol_id, column="fol_id")]  # kein prmary key

    ## addiditonal old tables
    mapping["bzm_basis"] = [IndexColumn(index_type=IndexType.sma, column="sma_psl")]


def get_main_column_from_tables(column_type: IndexType, only_processing: bool = False) -> dict:
    """Get main columns for target tables from dag runner.
    Optionally filter by main column type.

    Args:
        tables (list): list containing target tables names
        column_type (IndexType): IndexType filter

    Returns:
        dict {'<table name>': '<main column>'}
    """
    # default args, does not really matter

    from argparse import ArgumentParser

    _dag = DagModules()

    mapping = {}

    for task_name, task_values in _dag.tasks_dict.items():
        for _task in task_values.maincolumn:
            if _task.index_type == column_type:
                mapping[task_name] = _task.column

    if not only_processing:
        for table_name, table_values in InputTableMapping.mapping.items():
            for _table in table_values:
                if _table.index_type == column_type:
                    mapping[table_name] = _table.column

    return mapping


TaskConfigProperty = [
    "name",
    "run_function_path",
    "run_function_name",
    "num_processes",
    "max_package_size",
    "min_package_size",
]

TaskConfigPropertyLists = ["task_type", "maincolumn", "tables", "result_tables", "dependend_tasks"]


def task_config_to_db_entry(config: TaskConfig) -> Dict:
    result = {}

    for property in TaskConfigProperty:
        result[property] = getattr(config, property)

    for property in TaskConfigPropertyLists:
        if property in ["maincolumn", "task_type", "dependend_tasks"]:
            result[property] = ",".join([col.to_str() for col in getattr(config, property, []) if col])
        else:
            result[property] = ",".join(str(_part) for _part in getattr(config, property, []) if _part)
    return result


def get_TaskType_by_string(data_string: str) -> TaskType:
    if not data_string:
        return
    _id, _name = data_string.split("|")
    return TaskType.get_by_id(int(_id))


def get_index_col_by_string(data_string: str) -> IndexColumn:
    if not data_string:
        return
    _index, _col = data_string.split("|")
    return IndexType(int(_index))


def db_entry_to_task_config(entry_dict: Dict) -> TaskConfig:
    """get a TaskConfig from a db entry, keep dependent_tasks as strings"""
    for property in TaskConfigPropertyLists:
        if property in ["maincolumn", "task_type"]:
            if property == "task_type":
                entry_dict[property] = [
                    get_TaskType_by_string(item) for item in entry_dict[property].split(",") if item
                ]
            else:
                entry_dict[property] = [
                    get_index_col_by_string(item) for item in entry_dict[property].split(",") if item
                ]
        else:
            entry_dict[property] = [item for item in entry_dict[property].split(",") if item]

    return TaskConfig(**entry_dict)


def tasks_to_sql() -> None:
    """Generate a new dag_modules table from __DagModules"""
    _dag = __DagModules()
    _config_list = []

    for task_name, task_values in _dag.tasks_dict.items():
        if not TaskType.MultithreadedAggregation in task_values.task_type:
            _config_list.append(task_config_to_db_entry(task_values))

    df_config = pd.DataFrame.from_records(_config_list)
    df_config.to_sql_replace(MODULES_TABLE, force_complete_replace=True)


def load_modules_from_db() -> List[TaskConfig]:
    db = DatabaseManager()
    df_dag_modules = db.get_dataframe_by_table(
        MODULES_TABLE, int_columns=["num_processes", "max_package_size", "min_package_size"]
    )
    raw_modules_list = df_dag_modules.to_dict(orient="records")
    tasks_dict = {}
    for _module in raw_modules_list:
        _config = db_entry_to_task_config(_module)
        tasks_dict[_config.name] = _config

    # build dependency tree by using "dependend_tasks" strings
    for task_name, task in tasks_dict.items():
        task.dependend_tasks = [tasks_dict[_task_name] for _task_name in task.dependend_tasks]

    return tasks_dict


if __name__ == "__main__":
    tasks_to_sql()
