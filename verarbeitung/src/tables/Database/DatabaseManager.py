# -*- coding: utf-8 -*-
from __future__ import absolute_import

import glob
from os import chmod, getcwd, getpid, path, remove
import re
import stat
import sys
from typing import Any, List, Union

from sqlalchemy import MetaData, Table, inspect, delete, update, select
from sqlalchemy.orm import Session

from ..Common.filesystem import get_unique_file_name
from ..Common.env import get_from_env

# supress sqlalchemy warnings
import warnings
from time import sleep

import pandas as pd

with warnings.catch_warnings():
    import sqlalchemy
    import MySQLdb
warnings.filterwarnings("ignore")

from verarbeitung.src.tables.helper.helper import debugprint

POOL_RECYCLE = 5
POOL_TIMEOUT = 1
POOL_SIZE = 2

DEADLOCK_RETRY_COUNT = 25
TIMEOUT_RETRY_COUNT = 10


class Datatype:
    db_types_int = ["tinyint", "smallint", "mediumint", "int", "bigint"]
    db_types_float = ["decimal", "double", "float"]
    db_types_char = ["char", "tinytext", "mediumtext", "text", "longtext", "varchar"]
    db_types_date = ["datetime", "timestamp", "date", "time"]

    """DB field Datatype meta class."""

    def __init__(self, name, size, size_per_item=None):
        self.name = name
        self.size = size  # size in bytes
        self.size_per_item = size_per_item  # size i.e. per char

    def __str__(self):
        return self.name

    def byte_size(self, length=1):
        """Calculate length of String in Bytes

        Args:
            length(int): length of DatatType in Bytes

        Returns:
            int Size in Bytes
        """
        if not isinstance(length, int):
            length = int(length)
        if not self.size_per_item:
            return self.size * length
        else:
            return self.size + (self.size_per_item * length)


FIELD_BYTE_SIZE = 20  # estimated byte size per db field

# sizes taken from https://mariadb.com/kb/en/library/data-type-storage-requirements/

INT = Datatype("int", 4)
BIGINT = Datatype("bigint", 8)
CHAR = Datatype("char", 3)
VARCHAR = Datatype("varchar", 2, 1)
TEXT = Datatype("text", 2, 1)
DATE = Datatype("date", 3)
DATETIME = Datatype("datetime", 8)
TIME = Datatype("time", 3)
TIMESTAMP = Datatype("timestamp", 4)

DATATYPES = [
    CHAR,
    VARCHAR,
    TEXT,
    DATE,
    DATETIME,
    TIME,
    TIMESTAMP,
]


class FolderNotFoundException(Exception):
    pass


class NoDBConnectionException(Exception):
    pass


class DataFormatMismatchException(Exception):
    pass


class NoDatabaseConnection(Exception):
    """Raised if db connection could not be established."""

    pass


def _stringify(item):
    """Returns the item as string

    Args:
        item(int, str,...):Item to stringify

    Returns:
        string
    """
    if isinstance(item, int):
        return str(item)
    else:
        return "'" + str(item) + "'"


def get_data_type_size(data_type):
    """Try to determine the data ty

    Args:
        data_type:

    Returns:
        field size or a rough estimate
    """
    for _type in DATATYPES:
        if data_type[0] == str(_type):
            if len(data_type) > 1:
                return _type.byte_size(length=data_type[1])
            else:
                return _type.byte_size()

    # unknown type or size uknown, return estimate
    return FIELD_BYTE_SIZE


def get_table_column_sizes(engine, table):
    """Estimate the size of all columns of a table in bytes.

    Args:
        engine(): db connection
        table(str): table name

    Returns:
        dict with keys = column names and values = column Datatype

    """
    table_info_query = "SHOW FIELDS FROM {table};".format(table=table)
    with engine.begin() as connection:
        table_info = connection.execute(sqlalchemy.text(table_info_query)).all()
    table_info_dict = {}
    column_sizes_dict = {}
    for item in table_info:
        table_info_dict[item[0]] = item[1].lower()
    for _type in table_info_dict.keys():
        data_type = table_info_dict[_type].split("(")
        if len(data_type) > 1:
            data_type = [field.strip(")") for field in data_type]
        else:
            if data_type[0] == "text":
                size_query = "SELECT MAX(length('{field}')) from {table};".format(field=_type, table=table)
                with engine.begin() as connection:
                    size_result = connection.execute(sqlalchemy.text(size_query)).all()
                try:
                    # get longest text string from table
                    data_type.append(size_result[0][0])
                except IndexError:
                    # use estimate on error
                    pass
        size = get_data_type_size(data_type)
        column_sizes_dict[_type] = size
    return column_sizes_dict


def get_estimated_row_size(table, engine):
    """Try to estimate a tabel row size in bytes based on column data types and length

    Args:
        table: target table
        engine: database connection

    Returns:
        estimated row size

    """
    # estimate a maximum field_byte_size per row
    column_sizes_dict = get_table_column_sizes(engine, table)

    row_size = 0
    for column, size in column_sizes_dict.items():
        row_size += get_data_type_size((column, size))

    return row_size


def sanitize_list(_list):
    """Replace python's "None" with "NULL" for sql

    Args:
        _list: list_to_sanitize

    Returns:
        sanitized _list

    """
    result = ["NULL" if item is None else item for item in _list]
    return result


def to_sql_replace(
    data_frame,
    table,
    index=False,
    index_label=None,
    force_complete_replace=False,
    deduplicate=True,
    database_manager=None,
    retries=None,
):
    """Function to update an existing table from a dataframe without dropping old data.

    Table will be created if it does not exist.
    Args:
        data_frame (Dataframe): dataframe object
        table (str): table name
        index:
        index_label:
        force_complete_replace (bool): drop table prior to inserting, defaults to False
        deduplicate (bool): deduplicate dataframe entries? defaults to True
        database_manager (DatabaseManager): optional database manager to be used
        retries (int): max number of retries on deadlock before failing

    Returns:

    """
    if not database_manager:
        database_manager = DatabaseManager()
    if deduplicate:
        data_frame = data_frame.drop_duplicates()

    inspector = inspect(database_manager.engine)
    if not inspector.has_table(table) or force_complete_replace:
        # replace table by drop / insert
        if force_complete_replace:
            # create new table
            database_manager.execute(f"""Create table new_{table} LIKE {table}""")
            database_manager.execute(f"""Drop TABLE {table}""")
            database_manager.execute(f"""RENAME TABLE new_{table} to {table}""")
            data_frame.to_sql(table, database_manager.engine, if_exists="append", index=index, index_label=index_label)

        else:
            data_frame.to_sql(table, database_manager.engine, if_exists="replace", index=index, index_label=index_label)
    else:

        # get existing column headers
        column_name_query = (
            "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE from information_schema.columns "
            "WHERE table_schema = '{schema}' AND table_name = '{table}'".format(
                schema=database_manager.database, table=table
            )
        )
        with database_manager.engine.begin() as connection:
            column_name_row_proxy_list = connection.execute(sqlalchemy.text(column_name_query)).all()

        column_name_list = []
        csv_column_list = []
        set_statement = []

        _dataframe_columns = list(data_frame.columns.values)
        for field in column_name_row_proxy_list:
            column_name = field[0]
            if column_name in _dataframe_columns:
                column_name_list.append(field[0])
                if field[1] in Datatype.db_types_int and field[2] == "YES":
                    csv_column_list.append("@nullable_{field}".format(field=field[0]))
                    set_statement.append("{field} = nullif(@nullable_{field},'')".format(field=field[0]))
                else:
                    csv_column_list.append(field[0])

        # re-order dataframe columns
        data_frame = data_frame[column_name_list]

        # create a unique name for a tempfile
        path_to_csv = get_unique_file_name()
        data_frame.to_csv(path_to_csv, index=index, index_label=index_label, sep=";", quoting=1)
        del data_frame

        # make file readable for mysql
        chmod(path_to_csv, stat.S_IRWXU | stat.S_IRWXG | stat.S_IROTH)

        set_statement_clause = ""
        if len(set_statement) > 0:
            set_statement_clause = "SET {}".format(",".join(set_statement))

        column_string = ",".join(csv_column_list)
        # create query
        query = "LOAD DATA LOCAL INFILE '{path}' REPLACE INTO TABLE {table} CHARACTER SET UTF8 FIELDS TERMINATED BY ';'  OPTIONALLY ENCLOSED BY '\"'  ESCAPED BY '' IGNORE 1 LINES ({columns}) {set_statement};".format(
            path=path_to_csv,
            table=table,
            columns=column_string,
            set_statement=set_statement_clause,
        )

        """
        LOAD DATA LOCAL infile '/tmp/testdata.txt'
            INTO TABLE moo
            CHARACTER SET UTF8 
            fields terminated BY ","
            lines terminated BY "\n"
            (@vone, @vtwo, @vthree, @vfour, @vfive)
            SET
            one = nullif(@vone,''),
            two = nullif(@vtwo,''),
            three = nullif(@vthree,''),
            four = nullif(@vfour,'')
            ;

        """

        try:
            # execute query
            database_manager.execute(query, retries=retries)
            # delete tempfile
            if path.isfile(path_to_csv):
                remove(path_to_csv)
        except Exception as e:
            debugprint("DB engine error {}".format(e), level="exception")
            debugprint("Query: {}".format(query), level="error")


# monkey patch pandas Dataframe to append the function
pd.DataFrame.to_sql_replace = to_sql_replace


def get_value_or_none(_object, value_name):
    """Wrapper function to get a value from a pandas object.

    Any occurrences of Pandas NaT or NaN will be converted to None
    Args:
        _object: Pandas object (i.e. a Series)
        value_name: name of the value

    Returns:
        value or none
    """
    value = getattr(_object, value_name, None)
    if pd.isnull(value):
        value = None
    return value


# monkey patch pandas Series to append the function
pd.Series.get_value_or_none = get_value_or_none


def drop_all_indexes(table_name):
    db = DatabaseManager()
    with db.engine.begin() as connection:
        # _indexes = db.engine.execute(f"SHOW INDEX FROM {table_name}").fetchall()
        _indexes = connection.execute(sqlalchemy.text(f"SHOW INDEX FROM {table_name}")).all()
        indexes = [index[2] for index in _indexes]
        _drops = ",".join([f"DROP INDEX `{index}`" for index in indexes])
        drop_query = f"""ALTER TABLE {table_name} {_drops};"""
        # db.execute(drop_query)
        connection.execute(sqlalchemy.text(drop_query))


class DatabaseManager(object):
    _instance = {}
    _pid = None

    _test_db = "beast_aggregation_testdata"
    _called_from_test = False

    DB_USER = get_from_env("PROCESSING_DB_USER", "root")
    DATABASE = get_from_env("PROCESSING_DATABASE", "beast_aggregation")
    DB_HOST = get_from_env("PROCESSING_DB_HOST", "127.0.0.1")
    DB_PASSWORD = get_from_env("PROCESSING_DB_PASSWORD", "mysql")
    DB_PORT = get_from_env("PROCESSING_DB_PORT", "3306")

    def __set_for_current_pid(self):
        current_pid = getpid()
        for _key in ["url", "database", "engine"]:
            if not getattr(self._instance[current_pid], _key) == getattr(self, _key):
                setattr(self._instance[current_pid], _key, getattr(self, _key))

    def __new__(cls, *args, **kwargs):

        current_pid = getpid()

        if kwargs.get("singleton", True) == False:
            return object.__new__(cls)

        database = kwargs.get("database", cls.DATABASE)

        if (
            hasattr(sys, "_called_from_test")
            or hasattr(kwargs, "_called_from_test")
            or getattr(cls, "_called_from_test", False)
        ):
            database = cls._test_db

        if hasattr(sys, "_set_database"):
            database = sys._set_database
            if current_pid not in cls._instance or cls._instance[current_pid].database != database:
                cls._instance[current_pid] = None

        if current_pid != cls._pid:
            cls._instance[current_pid] = None
            cls._pid = getpid()

        if (
            cls._pid not in cls._instance
            or cls._instance[current_pid] is None
            or database not in cls._instance[current_pid].url
        ):
            cls._instance[current_pid] = object.__new__(cls)
            try:
                # print('connecting to {} Database...'.format(database))
                url = "mysql+mysqldb://{1}:{2}@{0}:{3}/{4}?local_infile=1&charset=utf8mb4".format(
                    cls.DB_HOST,
                    cls.DB_USER,
                    cls.DB_PASSWORD,
                    cls.DB_PORT,
                    database,
                )
                db_engine = sqlalchemy.create_engine(
                    url,
                    pool_recycle=POOL_RECYCLE,
                    pool_timeout=POOL_TIMEOUT,
                    pool_pre_ping=True,
                    pool_size=POOL_SIZE,
                    connect_args={"ssl": True},
                )
                cls._instance[current_pid].url = url
                # wait to avoid parallel connection from to many processes
                cls._instance[current_pid].engine = db_engine
                cls._instance[current_pid].database = database
            except Exception as error:
                print("Error: connection not established with url: {url}\nError: {error}".format(error=error, url=url))
                cls._instance[current_pid] = None
                raise NoDatabaseConnection(error)

            else:
                pass
                # print('connection from Process {pid} to Database {name} established'.format(
                #    name=DatabaseManager.DATABASE, pid=current_pid))
            return cls._instance[cls._pid]
        else:
            return object.__new__(cls)

    def __init__(self, database=None, singleton=True, base_folder=None, _called_from_test=False):
        self.singleton = singleton
        if database is None:
            database = self.DATABASE
        if singleton:
            current_pid = getpid()
            self.url = self._instance[current_pid].url
            self.engine = self._instance[current_pid].engine
            self.database = self._instance[current_pid].database
        elif database is not None:
            self.create_new_connection(database)
        if _called_from_test and (not self.database == self._test_db or not self.url in str(self.engine.url)):
            self.create_new_connection(self._test_db)
        self.base_folder = base_folder
        self.sql_commands = []

    def __del__(self):
        pass
        # if self.engine.connection and self.engine.connection.closed is False:
        # self.engine.connection.close()

    def create_nonspecific_connection(self):
        """DANGEROUS!!!! onyl use if really needed!!!!"""
        url = "mysql+mysqldb://{1}:{2}@{0}:{3}/?local_infile=1&charset=utf8mb4".format(
            self.DB_HOST,
            self.DB_USER,
            self.DB_PASSWORD,
            self.DB_PORT,
        )
        db_engine = sqlalchemy.create_engine(
            url,
            pool_recycle=POOL_RECYCLE,
            pool_timeout=POOL_TIMEOUT,
            pool_pre_ping=True,
            pool_size=POOL_SIZE,
            connect_args={"ssl": True},
        )
        return db_engine

    def create_new_connection(self, database):
        try:
            if hasattr(sys, "_called_from_test"):
                database = self._test_db
            print("connecting to {} Database...".format(database))
            url = "mysql+mysqldb://{1}:{2}@{0}:{3}/{4}?local_infile=1&charset=utf8mb4".format(
                self.DB_HOST,
                self.DB_USER,
                self.DB_PASSWORD,
                self.DB_PORT,
                database,
            )
            db_engine = sqlalchemy.create_engine(
                url,
                pool_recycle=POOL_RECYCLE,
                pool_timeout=POOL_TIMEOUT,
                pool_pre_ping=True,
                pool_size=POOL_SIZE,
                connect_args={"ssl": True},
            )
            self.url = url
            self.engine = db_engine
            self.database = database
            self.__set_for_current_pid()
        except Exception as error:
            print("Error: connection not established {}".format(error))
            self._instance = None

        else:
            print("connection established\n{}".format(self.DATABASE))

    def load_all_sql_files_from_folder(self, folder, filter="*.sql", replace_keywords=None, delimiter=None):
        """Load all sql files from a folder.

        Args:
            folder: path of target folder, can be realtive or absolute
            filter: wildcard filter to be applied to target folder files
            replace_keywords: replace keywords in sql code via python's "format"
            delimiter: optional delimiter for spliting sql code, defaults to ";"

        Returns:
            combined list of sql commands
        """
        commands = []
        _folder = self.sql_path(folder)
        if not path.isdir(_folder):
            if path.isdir(path.join(getcwd(), _folder)):
                _folder = path.join(getcwd(), _folder)
            else:
                raise FolderNotFoundException("Folder {} not found".format(_folder))
        if filter:
            _folder = path.join(_folder, filter)
        # sort files according to title
        _folder_content = glob.glob(_folder)
        _folder_content.sort()
        for _file in _folder_content:
            debugprint("Loading commands from file: {}".format(_file))
            statement = self.load_sql_file(_file, replace_keywords=replace_keywords, delimiter=delimiter)
            if not isinstance(statement, list):
                debugprint("Appending commands '{}' to commandlist".format(statement))
                commands.append(statement)
            else:
                commands = commands + statement
        return commands

    def load_sql_file(self, file, replace_keywords=None, delimiter=None):
        """Load sql code from a file

        Args:
            file: path of target file
            replace_keywords: replace keywords in sql code via python's "format"
            delimiter: optional delimiter for spliting sql code, defaults to ";"

        Returns:
            list of sql commands
        """
        fd = open(file, "r")
        sql_file = fd.read()
        fd.close()

        # replace keyword occurrences prior to splitting into commands
        if replace_keywords:
            sql_file.format(**replace_keywords)

        if not delimiter:
            delimiter = ";"
        sql_commands = [self.stripComments(line) for line in sql_file.strip().split(delimiter) if line]

        return sql_commands

    def get_dataframe_by_table(self, table=None, date_columns=None, int_columns=[]):
        """Read a complete table from sql and return as dataframe

        Args:
            table(str): table name

        Returns:
            dataframe
        """
        try:
            df_result = pd.read_sql_table(table, self.engine, parse_dates=date_columns)
            for _col in int_columns:
                df_result[_col] = df_result[_col].astype(pd.Int32Dtype(), errors="ignore")
        except Exception as e:  # TODO use more specific exceptions
            print("Command unknown exception {}".format(e))
        else:
            return df_result

    def get_dataframe_by_query(self, query=None):
        """get Datatrame by sql Query

        Args:
            query(str): sql query

        Returns:
            Dataframe
        """
        return pd.read_sql(self.sanitize(query), self.engine)

    def stripComments(self, code):
        """Strip comments from sql code

        Args:
            code(str): code to Strip

        Returns:
            stripped code
        """
        code = str(code)
        code = re.sub(r"(?m)--.*\n", "", code)
        return re.sub(r"(?m)^ *#.*\n?", "", code)

    def sanitize(self, command):
        """SQL-Escape commands here

        Args:
            command(str): command to sanitize

        Returns:
            sanitized commands
        """
        # ugly ugly replace
        command_lines = command.split("\n")
        uncommented_lines = []
        for line in command_lines:
            uncommented_lines.append(line.split("#")[0])
        command = " ".join(uncommented_lines)
        command_list = command.split(" ")
        sanitized_command_list = []
        for item in command_list:
            if item:
                if "%" in item and "%%" not in item:
                    sanitized_command_list.append(item.replace("%", "%%"))
                else:
                    sanitized_command_list.append(item)
        command = " ".join(sanitized_command_list)
        return command

    def delete_where_in(self, table: str, index_col: str, where_items: List[str]) -> None:
        """Delete data"""
        metadata = MetaData()
        with self.engine.begin() as connection:
            _table = Table(table, metadata, autoload_with=connection)

            _col = getattr(_table.c, index_col)
            delete_query = delete(_table).where(_col.in_(where_items))

            connection.execute(delete_query)
            # connection.commit()

    def update_where_in(self, table: str, index_col: str, where_items: List[str], value: Any) -> None:
        """Set index column to valzue if condition is met. Used for data deletion"""
        metadata = MetaData()
        with self.engine.begin() as connection:
            _table = Table(table, metadata, autoload_with=connection)

            _col = getattr(_table.c, index_col)

            update_query = update(_table).values({index_col: value}).where(_col.in_(where_items))

            connection.execute(update_query)
            # connection.commit()

    def delete_where_in_multi_column(self, table: str, index_cols: List[str], where_item: Union[str, int]) -> None:
        """Delete data"""
        metadata = MetaData()
        with self.engine.begin() as connection:
            _table = Table(table, metadata, autoload_with=connection)
            columns = [getattr(_table.c, col) for col in index_cols]
            delete_statement = _table.delete()
            for col in columns:
                delete_statement = delete_statement.where(col == where_item)

            connection.execute(delete_statement)

    def deadlock_save_execute(self, command, retries=None, return_result=True):
        """Execute sql statement and retry on deadlock."""

        if retries is None:
            retries = DEADLOCK_RETRY_COUNT

        timeout_attempts = 0
        deadlock_attempts = 0

        while deadlock_attempts <= retries and timeout_attempts <= TIMEOUT_RETRY_COUNT:
            try:
                # with self.engine.connect() as connection:
                with self.engine.begin() as connection:
                    # self.engine.execute(command)
                    result = connection.execute(sqlalchemy.text(command))
                    # connection.commit()
                if return_result:
                    return result
                else:
                    return
            except (sqlalchemy.exc.OperationalError, MySQLdb.OperationalError) as error:
                if "Deadlock found" in str(error):
                    print("Deadlock occurred, retrying ({} of {})".format(deadlock_attempts, retries))
                    deadlock_attempts += 1
                    sleep(5)
                    if deadlock_attempts > retries:
                        raise
                    continue
                if "Lock wait timeout exceeded" in str(error):
                    print("Timeout occurred, retrying ({} of {})".format(timeout_attempts, TIMEOUT_RETRY_COUNT))
                    timeout_attempts += 1
                    if timeout_attempts > TIMEOUT_RETRY_COUNT:
                        raise
                    continue
                raise
            except Exception:
                raise

    def execute(self, statement=None, skip_on_error=False, retries=None):
        """Execute given sql statement

        Args:
            statement(str): sql statment
            retries(int): max number of retries before fail

        Returns:
            dataframe
        """
        if statement is None and self.sql_commands:
            statement = self.sql_commands

        if not isinstance(statement, (list, tuple)):
            statement = [statement]

        # Execute every command from the input file
        for command in statement:
            # This will skip and report errors
            # For example, if the tables do not yet exist, this will skip over
            # the DROP TABLE commands

            try:
                if command and len(command) > 0:
                    command = self.sanitize(self.stripComments(command))
                    self.deadlock_save_execute(command, retries=retries)
            except MySQLdb.OperationalError as msg:
                print("Command skipped: {}".format(msg))
                # re-raise Exception
                raise
            except AttributeError:
                raise NoDBConnectionException("Error using Database connection, is DB connected?")
            except Exception as e:
                print("Command unknown exception {}, {}".format(e, command))
                # re-raise Exception
                if not skip_on_error:
                    raise
            except KeyboardInterrupt as e:
                print("KeyboardInterrupt on execute query {}".format(command))

    def cleanup(self, tables):
        """Drop temp tables if they still exist

        Args:
            tables(list): Database tables to Drop

        Returns:

        """
        debugprint("Cleaning up database..")
        commands = ["DROP TABLE IF EXISTS `{}`.`{}`;".format(self.database, name) for name in tables]
        debugprint("Commands: {}".format(commands))
        self.execute(commands)
        debugprint("Cleaning done")

    def truncate(self, tables: List[str]):
        """Truncate given tables.

        Args:
            tables (list): list with table names
        """
        debugprint("Truncating tables: {}..".format(tables))
        commands = ["TRUNCATE TABLE `{}`.`{}`;".format(self.database, name) for name in tables]
        debugprint("Commands: {}".format(commands))
        self.execute(commands)
        debugprint("Truncating done")

    def sql_path(self, folder):
        """Find Path so SQL folder

        Args:
            folder(str): relative Folder for the sql query

        Returns:
            absolute Folder
        """
        if self.base_folder:
            return path.join(self.base_folder, folder)
        return folder

    def close(self):
        """Dispose sql connection

        Returns:

        """
        # TODO
        if self.engine:
            self.engine.dispose()


class FrontendDatabaseManager(DatabaseManager):
    """Databasemanager Stub for additional Database connection"""

    _instance = {}
    _pid = None

    _test_db = "beast_aggregation_frontend_testdata"

    DB_USER = get_from_env("FRONTEND_DB_USER", "root")
    DATABASE = get_from_env("FRONTEND_DATABASE", "beast_aggregation")
    DB_HOST = get_from_env("FRONTEND_DB_HOST", "127.0.0.1")
    DB_PASSWORD = get_from_env("FRONTEND_DB_PASSWORD", "mysql")
    DB_PORT = get_from_env("FRONTEND_DB_PORT", "3306")


class Typo3DatabaseManager(DatabaseManager):
    """Databasemanager Stub for additional Database connection"""

    _instance = {}
    _pid = None

    _test_db = "beast_typo3_testdata"

    DB_USER = get_from_env("FRONTEND_DB_USER", "root")
    DATABASE = get_from_env("FRONTEND_TYPO3_DATABASE", "beast_typo3")
    DB_HOST = get_from_env("FRONTEND_DB_HOST", "127.0.0.1")
    DB_PASSWORD = get_from_env("FRONTEND_DB_PASSWORD", "mysql")
    DB_PORT = get_from_env("FRONTEND_DB_PORT", "3306")


def get_table_columns(table_name: str, database_manager: DatabaseManager = None) -> tuple:
    """Get all columns of a db table.

    Args:
        table_name (str): target table
        database_manager (DatabaseManager): if not provided default DatabaseManager will be used

    Returns:
        list with column names and list with column names without "*__"
    """
    if not database_manager:
        database_manager = DatabaseManager()
    query = """
        Select column_name from information_schema.columns
        where TABLE_SCHEMA = '{database}'
         and table_name = '{table}';""".format(
        database=database_manager.database, table=table_name
    ).lower()
    with database_manager.engine.begin() as connection:
        result = connection.execute(sqlalchemy.text(query)).all()
    columns = [column[0] for column in result]
    csv_columns = [part.split("__")[-1] for part in columns]
    return columns, csv_columns


def delete_from_table(_dataframe: pd.DataFrame, table: str, database_manager: DatabaseManager = None):
    """Delete contents from a dataframe from a target table"""
    if not database_manager:
        database_manager = DatabaseManager()

    columns = ",".join(_dataframe.columns)
    rows = [str(tuple(str(field) for field in row)) for row in _dataframe.values]

    query = f"""DELETE FROM {table} WHERE ({columns}) IN ({",".join(rows)})"""

    database_manager.execute(query)


# monkeypatch pandas
pd.DataFrame.delete_from_table = delete_from_table

if __name__ == "__main__":
    pass
