#!/usr/bin/python
# coding=utf-8
import sys
from os import remove

import pandas as pd
from verarbeitung.src.tables.Common.filesystem import get_unique_file_name
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.helper.helper import catchall
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess

from tools.Notification.mail import send_mail


def send_data_error(df_error: pd.DataFrame) -> None:
    """

    Args:
        df_error (pd.DataFrame): dataframe containing data that should not be

    Returns:
        None
    """
    # dump dataframe to csv
    temp_csv = f"{get_unique_file_name()}.csv"
    df_error.to_csv(temp_csv)

    # send mail
    send_mail(
        message="There was some data within check_1_wfmt which did not comply with the rules for the filed 'gruppe' that should be analyzed",
        subject="Check_1_wfmt data error",
        attachment=temp_csv,
    )

    # remove temp file
    remove(temp_csv)


class Check1WFMT(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.df_result = pd.DataFrame()

    def load_data(self):

        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT
                    psl_001.wmsti_requidbafa as requidbafa,
                    psl_001.wmsti_statusbafa,
                    psl_001.wmsti_bafaaenddatum,
                    psl_001.wmsti_zustres,
                    psl_001.wmsti_bafa,
                    psl_001.wmsti_refidba,
                    psl_001.wmsti_master_ba,
                    basis.requidbafa as basis_requidbafa,                    
                    psl_001_master.wmsti_refidba as master_refidba,
                    check_9.check_9_requidbafa
                FROM (Select * from wmsti_psl_001 as psl_001 LIMIT {self.offset},{self.limit}) as psl_001
                LEFT JOIN basis_1_wms_psl as basis on basis.requidbafa = psl_001.wmsti_requidbafa
                LEFT JOIN (SELECT wmsti_requidbafa, wmsti_refidba FROM wmsti_psl_001) as psl_001_master 
                ON psl_001.wmsti_master_ba = psl_001_master.wmsti_requidbafa
                LEFT JOIN check_9_wfmt_status_valid as check_9
                ON check_9.check_9_requidbafa = psl_001.wmsti_requidbafa
            """
        )

    def worker(self):
        # # abschluss_jahr
        #
        # 1. Ist das Feld [wmsti_psl_001].[wmsti_statusbafa] gleich "Abgeschlossen", so wird das Feld
        # [check_1_wfmt].[abschluss_jahr] mit der Jahresangabe aus dem Datum [wmsti_psl_001].[wmsti_bafaaenddatum] gefüllt.
        #
        # 2. Ist das Feld [wmsti_psl_001].[wmsti_statusbafa] nicht gleich "Abgeschlossen" UND der Eintrag im Feld
        # Ressort [WMSTI_PSL_001].[Zuständiger BBN] entspricht nicht den Filterkriterien #51 der für die Eingangstabelle
        # definiert ist, so wird das Feld [check_1_wfmt].[abschluss_jahr] mit dem Wert "2000" gefüllt.
        # (Aufträge welche dem Filter nicht entsprechen, werden nicht mehr aktualisiert und gehen somit ggf.
        # nicht mehr in den Status Abgeschlossen.)
        #
        # 3. Ist das Feld [wmsti_psl_001].[wmsti_statusbafa] nicht gleich "Abgeschlossen" UND das Feld
        # [check_1_wfmt].[abschluss_jahr] nicht mit dem Wert "2000" gefüllt UND die Request-ID
        # [wmsti_psl_001].[requidbafa] ist in der Tabelle [check_9_wfmt_status] im Feld
        # [check_9_wfmt_status].[check_9_requidbafa] enthalten, so wird das Feld [check_1_wfmt].[abschluss_jahr]
        # mit dem Wert "2000" gefüllt. (Aufträge aus der [check_9_wfmt_status] sind in WFMT nicht mehr vorhanden und
        # liefern somit auch keine weiteren Änderungen. Die Tabelle [check_9_wfmt_status] darf nur verwendet werden,
        # wenn diese zuvor freigegeben wurde.
        #
        # Trift Regel 1 bis 3 nicht zu, so wird das Feld [check_1_wfmt].[abschluss_jahr] mit dem Wert "" gefüllt.

        # Regel 1
        df_abgeschlossen = (
            self.df_source_data[
                (self.df_source_data["wmsti_statusbafa"] == "Abgeschlossen")
                & (
                    ~(self.df_source_data["wmsti_bafaaenddatum"].isnull())
                    & ~(self.df_source_data["wmsti_bafaaenddatum"] == "")
                )
            ].rename(columns={"wmsti_bafaaenddatum": "abschluss_jahr"})
        )[["requidbafa", "abschluss_jahr"]]
        df_abgeschlossen["abschluss_jahr"] = df_abgeschlossen["abschluss_jahr"].transform(
            lambda _datetime: str(_datetime.year)
        )

        # Regel 2
        df_not_abgeschlossen = self.df_source_data[
            ~(self.df_source_data["wmsti_statusbafa"] == "Abgeschlossen")
            & ~(
                self.df_source_data["wmsti_zustres"].str.startswith("20")
                | self.df_source_data["wmsti_zustres"].str.startswith("25")
                | self.df_source_data["wmsti_zustres"].str.startswith("2701")
                | self.df_source_data["wmsti_zustres"].str.startswith("28")
                | self.df_source_data["wmsti_zustres"].isin(["2702_CCN_01", "2707_CCC_01", "3062_CS_ATM"])
            )
        ][["requidbafa"]]
        df_not_abgeschlossen["abschluss_jahr"] = "2000"

        # Regel 3
        df_check_9 = self.df_source_data[
            ~(self.df_source_data["requidbafa"].isin(df_abgeschlossen["requidbafa"]))
            & ~(self.df_source_data["requidbafa"].isin(df_not_abgeschlossen["requidbafa"]))
            & (self.df_source_data["check_9_requidbafa"].notnull())
        ]
        df_check_9["abschluss_jahr"] = "2000"

        # Regel 4
        df_rule_4 = self.df_source_data[
            ~(self.df_source_data["requidbafa"].isin(df_abgeschlossen["requidbafa"]))
            & ~(self.df_source_data["requidbafa"].isin(df_not_abgeschlossen["requidbafa"]))
            & ~(self.df_source_data["requidbafa"].isin(df_check_9["requidbafa"]))
        ][["requidbafa"]]
        df_rule_4["abschluss_jahr"] = ""

        df_abschluss_jahr = pd.concat([df_abgeschlossen, df_not_abgeschlossen, df_check_9, df_rule_4])

        # create result dataframe
        self.df_result = pd.merge(
            left=self.df_source_data,
            right=df_abschluss_jahr,
            left_on=["requidbafa"],
            right_on=["requidbafa"],
            how="left",
        )

        # # in_basis_1_wms_psl
        # Prüfung ob der Auftrag in der Verarbeitungstabelle [basis_1_wms_PSL] enthalten ist und
        # somit in einem Basisbericht verwendet werden kann.
        #
        # 1. Suche alle BA/FA „requidbafa“ [wmsti_psl_001].[wmsti_requidbafa] die auch in der Verarbeitungstabelle
        # [basis_1_wms_psl], Feld [basis_1_wms_psl].[requidbafa] enthalten sind und trage in das Feld
        # [check_1_wfmt].[in_basis_1_wms_psl] eine 1 für Ja ein.
        #
        # 2. Suche alle BA/FA „requidbafa“ [wmsti_psl_001].[wmsti_requidbafa] die in der Verarbeitungstabelle
        # [basis_1_wms_psl], [basis_1_wms_psl].[requidbafa] nicht enthalten sind und trage in das Feld
        # [check_1_wfmt].[in_basis_1_wms_psl] eine 0 für Nein ein.

        df_is_in_basis_1_wms_psl = self.df_source_data[~(self.df_source_data["basis_requidbafa"].isnull())][
            ["requidbafa"]
        ]
        df_is_in_basis_1_wms_psl["in_basis_1_wms_psl"] = 1

        df_is_not_in_basis_1_wms_psl = self.df_source_data[(self.df_source_data["basis_requidbafa"].isnull())][
            ["requidbafa"]
        ]
        df_is_not_in_basis_1_wms_psl["in_basis_1_wms_psl"] = 0

        df_in_basis_1_wms_psl = pd.concat([df_is_in_basis_1_wms_psl, df_is_not_in_basis_1_wms_psl])

        # merge into result
        self.df_result = pd.merge(
            left=self.df_result,
            right=df_in_basis_1_wms_psl,
            left_on=["requidbafa"],
            right_on=["requidbafa"],
            how="left",
        )

        # # gruppe
        # Gruppe von Aufträgen die nur gemeinsamm gelöscht werden dürfen.
        # 1. Suche alle Betriebsaufträge [wmsti_psl_001].[wmsti_bafa] = "BA" mit „requidbafa“ [wmsti_psl_001].[wmsti_requidbafa]
        # die keinen Eintrag im Feld Master BA [wmsti_psl_001].[wmsti_master_ba] haben, also kein Slave sind und trage
        # in das Feld [check_1_wfmt].[gruppe] den Wert aus „requidbafa“ [wmsti_psl_001].[wmsti_requidbafa] ein.
        # Suche zu diesem BA die untergeordneten Fertigungsaufträge (FA). Die untergeordneten FA haben die
        # Request-ID [requidbafa] des Ursprungs-BA im Feld [wmsti_psl_001].[wmsti_refidba] und trage in das Feld
        # [check_1_wfmt].[gruppe] den Wert aus „requidbafa“ [wmsti_psl_001].[wmsti_requidbafa] ein..
        # Gültiger Eintrag im Feld Master BA [wmsti_psl_001].[wmsti_master_ba beginnt mit einem Buchstaben (klein / groß) gefolgt von 14 Ziffern
        #
        # 2. Suche alle Aufträge mit „requidbafa“
        # [wmsti_psl_001].[wmsti_requidbafa] die einen Eintrag im Feld Master BA [wmsti_psl_001].[wmsti_master_ba]
        # haben, dadurch ein Slave sind und trage in das Feld [check_1_wfmt].[gruppe] die Request ID aus dem Feld
        # [wmsti_psl_001].[wmsti_refidba] des Masters ein. Die Request-ID des Master steht in dem Feld Master BA
        # [wmsti_psl_001].[wmsti_master_ba] des Slave Auftrages. Ist das Feld Feld [wmsti_psl_001].[wmsti_refidba]
        # des Masters leer so trage die Request ID aus dem Feld Master BA [wmsti_psl_001].[wmsti_master_ba] in das
        # Feld [check_1_wfmt].[gruppe] ein.
        # Gültiger Eintrag im Feld Master BA [wmsti_psl_001].[wmsti_master_ba beginnt mit einem Buchstaben (klein / groß) gefolgt von 14 Ziffern
        #
        # 3. Suche alle Fertigungsaufträge [wmsti_psl_001].[wmsti_bafa] = "FA" mit „requidbafa“
        # [wmsti_psl_001].[wmsti_requidbafa] , welche noch keinen Eintrag im Feld [check_1_wfmt].[gruppe] haben und
        # trage in das Feld [check_1_wfmt].[gruppe] die Request ID des Ursprungs-BA aus dem Feld
        # [wmsti_psl_001].[wmsti_refidba] ein. Ist das Feld [wmsti_psl_001].[wmsti_refidba] leer, so trage in das Feld
        # [check_1_wfmt].[gruppe] die Request ID des FA [wmsti_psl_001].[wmsti_requidbafa] ein.
        #
        # Hinweis: Regel 1 bis 3 sind nacheinander zu durchlaufen.
        # Am Ende darf es keine [check_1_wfmt]].[requidbafa] ohne einen Eintrag im Feld [check_1_wfmt].[gruppe] geben.
        # Ist dies der Fall oder die Werte sind gleich 0 bzw. "NULL müssen diese Aufträge
        # analysiert werden und bei Bedarf die Regeln Ergänzt bzw. die Datensätze von Hand korrigiert werden.
        #
        # Folgende Voraussetzungen liegen dem Feld [check_1_wfmt].[gruppe] zu Grunde:
        # für Regel 1.: Als Ausgangspunkt für jede BA/FA Kaskade gibt es immer einen BA. Diesem können weitere FA zugeordnet sein.
        # für Regel 2.: Slave Aufträge werden immer dem BA aus der Kaskade zugeordnet, wenn dieser anhand der Angaben
        # aus dem Master BA ermittelt werden kann. Kann der BA aus der Kaskade nicht ermittelt werden, wird der Slave dem Master als Gruppe zugeordnet
        # für Regel 3.: FA für die keine BA in der Eingangstabelle der BEAST Datenbank zu finden sind,
        # werden dem Ursprungs BA zugeordnet, sofern dies möglich ist. Ist diese Zuordnung nicht möglich bildet
        # der FA ggf. eine eigene Gruppe.

        # Regel 1
        df_no_master_ba = self.df_source_data[
            (self.df_source_data["wmsti_bafa"] == "BA")
            & (
                (self.df_source_data["wmsti_master_ba"].isnull())
                | (self.df_source_data["wmsti_master_ba"] == "")
                | ~(self.df_source_data["wmsti_master_ba"].str.fullmatch("[a-z,A-Z]{1}[0-9]{14}"))
            )
        ]
        df_no_master_ba["gruppe"] = df_no_master_ba["requidbafa"]
        df_no_master_ba = df_no_master_ba[["requidbafa", "gruppe"]]

        df_fa = self.df_source_data[
            (self.df_source_data["wmsti_bafa"] == "FA")
            & (self.df_source_data["wmsti_refidba"].isin(df_no_master_ba["requidbafa"]))
        ]
        df_fa["gruppe"] = df_fa["wmsti_refidba"]
        df_fa = df_fa[["requidbafa", "gruppe"]]

        df_regel_1 = pd.concat([df_no_master_ba, df_fa])

        # Regel 2
        df_slave_ba = self.df_source_data[
            ~(self.df_source_data["wmsti_master_ba"].isnull())
            & ~(self.df_source_data["wmsti_master_ba"] == "")
            & (self.df_source_data["wmsti_master_ba"].str.fullmatch("[a-z,A-Z]{1}[0-9]{14}"))
        ]

        if not df_slave_ba.empty:

            # try to find the master for the slave and use his wmsti_refidba IF it is set,
            # else just use the requidbafa of the master
            df_master_ba = df_slave_ba[
                (~(df_slave_ba["master_refidba"].isnull()) & ~(df_slave_ba["master_refidba"] == ""))
            ]
            df_master_ba = df_master_ba.rename(columns={"master_refidba": "gruppe"})
            df_master_ba = df_master_ba[["wmsti_master_ba", "gruppe"]]

            df_slave_ba_no_master = df_slave_ba[~(df_slave_ba["wmsti_master_ba"].isin(df_master_ba["wmsti_master_ba"]))]
            df_slave_ba_no_master = df_slave_ba_no_master.rename(columns={"wmsti_master_ba": "gruppe"})
            df_slave_ba_no_master = df_slave_ba_no_master[["requidbafa", "gruppe"]]

            df_slave_ba_master = pd.merge(
                left=df_slave_ba,
                right=df_master_ba,
                left_on=["wmsti_master_ba"],
                right_on=["wmsti_master_ba"],
                how="left",
            )
            df_slave_ba_master = df_slave_ba_master[
                ~(df_slave_ba_master["gruppe"].isnull()) & ~(df_slave_ba_master["gruppe"] == "")
            ][["requidbafa", "gruppe"]]

            df_regel_2 = pd.concat([df_slave_ba_no_master, df_slave_ba_master])
        else:
            df_regel_2 = pd.DataFrame(
                columns=[
                    "requidbafa",
                    "wmsti_statusbafa",
                    "wmsti_bafaaenddatum",
                    "wmsti_zustres",
                    "wmsti_bafa",
                    "wmsti_refidba",
                    "wmsti_master_ba",
                    "basis_requidbafa",
                    "gruppe",
                ]
            )

        # Regel 3
        df_fa = self.df_source_data[
            (self.df_source_data["wmsti_bafa"] == "FA")
            & ~(self.df_source_data["requidbafa"].isin(df_fa["requidbafa"]))
            & (~(self.df_source_data["requidbafa"].isin(df_regel_2["requidbafa"])))
        ]
        df_fa_ba = df_fa[~(df_fa["wmsti_refidba"].isnull()) & ~(df_fa["wmsti_refidba"] == "")]
        df_fa_ba["gruppe"] = df_fa_ba["wmsti_refidba"]
        df_fa_ba = df_fa_ba[["requidbafa", "gruppe"]]

        df_fa_no_refidba = df_fa[(df_fa["wmsti_refidba"].isnull()) | (df_fa["wmsti_refidba"] == "")]
        df_fa_no_refidba["gruppe"] = df_fa_no_refidba["requidbafa"]
        df_fa_no_refidba = df_fa_no_refidba[["requidbafa", "gruppe"]]

        df_regel_3 = pd.concat([df_fa_ba, df_fa_no_refidba])

        df_gruppe = pd.concat([df_regel_1, df_regel_2, df_regel_3])

        self.df_result = pd.merge(
            left=self.df_result, right=df_gruppe, left_on=["requidbafa"], right_on=["requidbafa"], how="left"
        )

        df_error = self.df_result[
            (self.df_result["gruppe"].isnull()) | (self.df_result["gruppe"] == "") | (self.df_result["gruppe"] == 0)
        ]
        if not df_error.empty:
            # do send a notification
            send_data_error(df_error)

        # set dummy values, this will be processed in the nex multiprocessing step
        self.df_result["abschluss_gruppe"] = ""
        self.df_result["grp_in_basis_1_wms_psl"] = 0

        self.df_result = self.df_result.drop_duplicates()

        # force int and then string for column or else it might be a float
        self.df_result["abschluss_jahr"] = self.df_result["abschluss_jahr"].astype(int, errors="ignore")
        self.df_result["abschluss_jahr"] = self.df_result["abschluss_jahr"].astype(str)
        self.df_result["abschluss_gruppe"] = self.df_result["abschluss_gruppe"].astype(int, errors="ignore")
        self.df_result["abschluss_gruppe"] = self.df_result["abschluss_gruppe"].astype(str)

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("check_1_wfmt")


class Check1WFMTPOST(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)

    def load_data(self):

        self.df_result = self.db.get_dataframe_by_query(
            f"""
                SELECT
                    check_1.requidbafa,
                    check_1.abschluss_jahr,
                    check_1.in_basis_1_wms_psl,
                    check_1.gruppe
                FROM (
                Select DISTINCT gruppe from check_1_wfmt as check_1 LIMIT {self.offset},{self.limit}
                ) as _gruppe
                INNER JOIN check_1_wfmt as check_1 on _gruppe.gruppe = check_1.gruppe
            """
        )

    def worker(self):
        # Folgende Regeln gelten für [check_1_wfmt].[abschluss_gruppe]: Jahr in dem die Gruppe Abgeschlossen wurde
        #
        # 1. Suche alle Aufträge die zu einer Gruppe [check_1_wfmt].[gruppe] gehören. Sind in dieser Gruppe Aufträge
        # mit dem Eintrag 1 = Ja im Feld [check_1_wfmt].[in_basis_1_wms_psl] vorhanden, so prüfe alle Aufträge dieser
        # Gruppe welche im Feld [check_1_wfmt].[in_basis_1_wms_psl] den Eintrag 1 = Ja haben, ob diese im Feld Feld
        # [check_1_wfmt].[abschluss_jahr] eine Jahreszahl eingetragen haben. Ist dies der Fall so ermittle die höchste
        # Jahreszahl aus dem Feld [check_1_wfmt].[abschluss_jahr] und trage diese bei allen Aufträgen dieser Gruppe,
        # unabhängig vom Eintrag in [check_1_wfmt].[in_basis_1_wms_psl], in das Feld [check_1_wfmt].[abschluss_gruppe]
        # bei allen Aufträgen dieser Gruppe ein. Haben nicht alle der so gefundenen Aufträge einen Eintrag im Feld
        # [check_1_wfmt].[abschluss_jahr], so wird das Feld [check_1_wfmt].[abschluss_gruppe] bei allen Aufträgen dieser
        # Gruppe mit "" gefüllt.
        #
        # 2. Suche alle Aufträge die zu einer Gruppe [check_1_wfmt].[gruppe] gehören, Sind in dieser Gruppe nur
        # Aufträge mit dem Eintrag 0 = Nein im Feld [check_1_wfmt].[in_basis_1_wms_psl] vorhanden, so prüfe alle Aufträge
        # dieser Gruppe ob diese im Feld Feld [check_1_wfmt].[abschluss_jahr] eine Jahreszahl eingetragen haben. Ist dies
        # der Fall so ermittle die höchste Jahreszahl aus dem Feld [check_1_wfmt].[abschluss_jahr] und trage diese bei
        # allen Aufträgen dieser Gruppe in das Feld [check_1_wfmt].[abschluss_gruppe] bei allen Aufträgen dieser Gruppe
        # ein. Haben nicht alle der so gefundenen Aufträge einen Eintrag im Feld [check_1_wfmt].[abschluss_jahr], so wird
        # das Feld [check_1_wfmt].[abschluss_gruppe] bei allen Aufträgen dieser Gruppe mit "" gefüllt.

        # Regel 1
        sr_abschluss_jahr_max_exclude_gruppe = self.df_result[
            (self.df_result["in_basis_1_wms_psl"] == 1)
            & ((self.df_result["abschluss_jahr"].isnull()) | (self.df_result["abschluss_jahr"] == ""))
        ]["gruppe"].drop_duplicates()
        df_abschluss_jahr_max_in = (
            self.df_result[
                (self.df_result["in_basis_1_wms_psl"] == 1)
                & ~(self.df_result["gruppe"].isin(sr_abschluss_jahr_max_exclude_gruppe))
            ]
            .sort_values(by=["abschluss_jahr"])
            .groupby(["gruppe"])
            .last()
            .reset_index()
        )
        df_abschluss_jahr_max = df_abschluss_jahr_max_in[["gruppe", "abschluss_jahr"]].rename(
            columns={"abschluss_jahr": "abschluss_gruppe"}
        )

        # Regel 2
        sr_abschluss_jahr_max_not_in_exclude_gruppe = self.df_result[
            (self.df_result["in_basis_1_wms_psl"] == 0)
            & ((self.df_result["abschluss_jahr"].isnull()) | (self.df_result["abschluss_jahr"] == ""))
        ]["gruppe"].drop_duplicates()

        df_abschluss_jahr_max_not_in = (
            self.df_result[
                ~(self.df_result["gruppe"].isin(df_abschluss_jahr_max["gruppe"]))
                & ~(self.df_result["gruppe"].isin(sr_abschluss_jahr_max_exclude_gruppe))
                & ~(self.df_result["gruppe"].isin(sr_abschluss_jahr_max_not_in_exclude_gruppe))
                & (self.df_result["in_basis_1_wms_psl"] == 0)
            ]
            .sort_values(by=["abschluss_jahr"])
            .groupby(["gruppe"])
            .last()
            .reset_index()
        )
        df_abschluss_jahr_max_not_in = (
            df_abschluss_jahr_max_not_in[["gruppe", "abschluss_jahr"]]
            .rename(columns={"abschluss_jahr": "abschluss_gruppe"})
            .reset_index(drop=True)
        )

        df_abschluss_gruppe = pd.concat([df_abschluss_jahr_max, df_abschluss_jahr_max_not_in])

        self.df_result = pd.merge(
            left=self.df_result,
            right=df_abschluss_gruppe,
            left_on=["gruppe"],
            right_on=["gruppe"],
            how="left",
        )

        self.df_result = self.df_result.fillna({"abschluss_gruppe": ""})

        # Folgende Regeln gelten für [check_1_wfmt].[grp_in_basis_1_wms_psl]: Prüfung ob mindestens ein Auftrag der
        # Gruppe in der Tabelle [basis_1_wms_psl] enthalten ist
        #
        # 1. Suche alle Aufträge die zu einer Gruppe [check_1_wfmt].[gruppe] gehören. Sind in dieser Gruppe Aufträge
        # mit dem Eintrag 1 = Ja im Feld [check_1_wfmt].[in_basis_1_wms_psl] vorhanden, so wird bei allen Aufträgen
        # dieser Gruppe, unabhängig vom Eintrag im Feld [check_1_wfmt].[in_basis_1_wms_psl], das Feld
        # [check_1_wfmt].[grp_in_basis_1_wms_psl] mit 1 = Ja gefüllt
        #
        # 2. Suche alle Aufträge die zu einer Gruppe [check_1_wfmt].[gruppe] gehören. Sind in dieser Gruppe keine
        # Aufträge mit dem Eintrag 0 = Nein im Feld [check_1_wfmt].[in_basis_1_wms_psl] vorhanden, so wird bei allen
        # Aufträgen dieser Gruppe, unabhängig vom Eintrag im Feld [check_1_wfmt].[in_basis_1_wms_psl], das Feld
        # [check_1_wfmt].[grp_in_basis_1_wms_psl] mit 0 = Nein gefüllt

        df_gruppe_in_basis_1 = (
            self.df_result[(self.df_result["in_basis_1_wms_psl"] == 1)]
            .groupby(["gruppe"])
            .first()
            .reset_index()
            .rename(columns={"in_basis_1_wms_psl": "grp_in_basis_1_wms_psl"})
        )[["gruppe", "grp_in_basis_1_wms_psl"]]

        self.df_result = pd.merge(
            left=self.df_result,
            right=df_gruppe_in_basis_1,
            left_on=["gruppe"],
            right_on=["gruppe"],
            how="left",
        ).fillna({"grp_in_basis_1_wms_psl": 0})

        self.df_result = self.df_result[
            [
                "requidbafa",
                "abschluss_jahr",
                "in_basis_1_wms_psl",
                "gruppe",
                "abschluss_gruppe",
                "grp_in_basis_1_wms_psl",
            ]
        ]
        self.df_result = self.df_result.drop_duplicates()

        # force int and then string for column or else it might be a float
        self.df_result["abschluss_jahr"] = self.df_result["abschluss_jahr"].astype(int, errors="ignore")
        self.df_result["abschluss_jahr"] = self.df_result["abschluss_jahr"].astype(str)
        self.df_result["abschluss_gruppe"] = self.df_result["abschluss_gruppe"].astype(int, errors="ignore")
        self.df_result["abschluss_gruppe"] = self.df_result["abschluss_gruppe"].astype(str)

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("check_1_wfmt")


@catchall
def run(stop_event=None, processbar=True, max_package_size=100000, min_package_size=50000):

    # truncate target table
    db = DatabaseManager()
    db.truncate(["check_1_wfmt"])

    # create groups
    mp = Multiprocess(
        stop_event=stop_event,
        processbar=processbar,
        max_package_size=max_package_size,
        min_package_size=min_package_size,
    )
    mp.start_multiplexer(table_name="wmsti_psl_001", processing_class=Check1WFMT)
    mp.start_progressbar(table_name="wmsti_psl_001")

    # apply group wide rules
    post_mp = Multiprocess(
        stop_event=stop_event,
        processbar=processbar,
        max_package_size=max_package_size,
        min_package_size=min_package_size,
    )
    post_mp.start_multiplexer(table_name="check_1_wfmt", distinct_column="gruppe", processing_class=Check1WFMTPOST)
    post_mp.start_progressbar(table_name="check_1_wfmt", distinct_column="gruppe")


if __name__ == "__main__":
    sys._mp_off = True

    run()
