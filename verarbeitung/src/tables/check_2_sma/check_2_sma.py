#!/usr/bin/python
# coding=utf-8
import sys

import pandas as pd
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.helper.helper import catchall
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess


def set_type_as_datetime_if_needed(dataframe, column):
    if dataframe[column].dtype != "datetime64[ns]":
        dataframe[column] = pd.to_datetime(dataframe[column], errors="coerce")
    return dataframe


class Check2SMA(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.df_result = pd.DataFrame()

    def load_data(self):
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT
                    sma.sm_auftragsnummer,
                    sma.systemstatus,
                    sma.aenderungsdatum,
                    sma.t_ladedatum,
                    basis.sma_verbunden
                FROM (Select * from sm_auftrag_psl_allgemein as sma LIMIT {self.offset},{self.limit}) as sma
                LEFT JOIN basis_1_wms_psl as basis on sma.sm_auftragsnummer = basis.sma_verbunden
                GROUP BY sma.sm_auftragsnummer
            """
        )
        self.df_source_data = set_type_as_datetime_if_needed(self.df_source_data, "t_ladedatum")
        self.df_source_data = set_type_as_datetime_if_needed(self.df_source_data, "aenderungsdatum")

        self.df_obligo = self.db.get_dataframe_by_query(
            f"""
            Select 
                obligo.sma_psl,
                obligo.obligo
            FROM (Select * from sm_auftrag_psl_allgemein as sma LIMIT {self.offset},{self.limit}) as sma
            LEFT JOIN psl_koa_obligo as obligo on sma.sm_auftragsnummer = obligo.sma_psl
            """
        )

    def worker(self):
        # Folgende Regel gilt für [check_2_sma].[sm_auftragsnummer]:
        # 1. Für jede SMA in der Tabelle [SM_AUFTRAG_PSL_ALLGEMEIN].[sm_auftragsnummer] gibt es genau einen Datensatz
        # mit dem gleichen Eintrag im Feld [check_2_sma].[sm_auftragsnummer]. Dieses Feld dient zur Verknüpfung mit der
        # SMA in der [basis_1_wms_psl]

        self.df_result = self.df_source_data["sm_auftragsnummer"].drop_duplicates()

        # Folgende Regel gilt für [check_2_sma].[abschluss_jahr]: Jahr in dem der Auftrag im Status TABG oder
        # ABGS zuletzt geändert wurde

        # 1. Ist das Feld [SM_AUFTRAG_PSL_ALLGEMEIN].[systemstatus] gleich "TABG" oder "ABGS", so wird das Feld
        # [check_2_sma].[abschluss_jahr] mit der Jahresangabe aus dem Datum [SM_AUFTRAG_PSL_ALLGEMEIN].[aenderungsdatum]
        # gefüllt. Ist kein auswertbares Datum im Feld Datum [SM_AUFTRAG_PSL_ALLGEMEIN].[aenderungsdatum] vorhanden,
        # so wird das Feld [check_2_sma].[abschluss_jahr] mit der Jahresangabe aus dem Datum
        # [SM_AUFTRAG_PSL_ALLGEMEIN].[t_ladedatum] gefüllt. (Das Ladedatum entspricht der Übertragung von Daten in das
        # System BI4PSL, erfolgt eine Initialladung, haben alle Datensätze das gleiche Ladedatum, weshalb dieses nur im
        # Notfall Verwendung finden sollte).
        #
        # 2. Ist das Feld [SM_AUFTRAG_PSL_ALLGEMEIN].[systemstatus] nicht gleich "TABG" und nicht gleich "ABGS",
        # so wird das Feld [check_2_sma].[abschluss_jahr] mit dem Wert "" gefüllt.
        df_aenderungsdatum = self.df_source_data[
            ~(self.df_source_data["aenderungsdatum"].isnull())
            & ~(self.df_source_data["aenderungsdatum"] == "")
            & (self.df_source_data["systemstatus"].isin(["TABG", "ABGS"]))
        ]
        df_aenderungsdatum["abschluss_jahr"] = df_aenderungsdatum["aenderungsdatum"].transform(
            lambda _datetime: str(_datetime.year)
        )
        df_aenderungsdatum = df_aenderungsdatum[["sm_auftragsnummer", "abschluss_jahr"]]

        df_t_ladedatum = self.df_source_data[
            ((self.df_source_data["aenderungsdatum"].isnull()) | (self.df_source_data["aenderungsdatum"] == ""))
            & (self.df_source_data["systemstatus"].isin(["TABG", "ABGS"]))
        ]
        df_t_ladedatum["abschluss_jahr"] = df_t_ladedatum["t_ladedatum"].transform(
            lambda _datetime: str(_datetime.year)
        )
        df_t_ladedatum = df_t_ladedatum[["sm_auftragsnummer", "abschluss_jahr"]]

        df_abschluss_jahr = pd.concat([df_aenderungsdatum, df_t_ladedatum])

        self.df_result = pd.merge(
            left=self.df_result,
            right=df_abschluss_jahr,
            left_on=["sm_auftragsnummer"],
            right_on=["sm_auftragsnummer"],
            how="left",
        ).fillna({"abschluss_jahr": ""})

        # Folgende Regel gilt für [check_2_sma].[in_basis_1_wms_psl]: Prüfung ob der Auftrag in der
        # Verarbeitungstabelle [basis_1_wms_PSL] enthalten ist und somit in einem Basisbericht verwendet werden kann.

        # 1. Suche alle SM-Aufträge [SM_AUFTRAG_PSL_ALLGEMEIN].[sm_auftragsnummer], die auch in der Verarbeitungstabelle
        # [basis_1_wms_psl], Feld [basis_1_wms_psl].[sma_verbunden] enthalten sind und trage in das Feld
        # [check_2_sma].[in_basis_1_wms_psl] eine 1 für Ja ein.
        #
        # 2. Suche alle SM-Aufträge [SM_AUFTRAG_PSL_ALLGEMEIN].[sm_auftragsnummer], die in der Verarbeitungstabelle
        # [basis_1_wms_psl], [basis_1_wms_psl].[sma_verbunden] nicht enthalten sind und trage in das Feld
        # [check_2_sma].[in_basis_1_wms_psl] eine 0 für Nein ein.

        # Regel 1
        df_enthalten = self.df_source_data[
            ~(self.df_source_data["sma_verbunden"].isnull()) & ~(self.df_source_data["sma_verbunden"] == "")
        ][["sm_auftragsnummer"]]
        df_enthalten["in_basis_1_wms_psl"] = 1

        self.df_result = pd.merge(
            left=self.df_result,
            right=df_enthalten,
            left_on=["sm_auftragsnummer"],
            right_on=["sm_auftragsnummer"],
            how="left",
        ).fillna({"in_basis_1_wms_psl": 0})

        # Folgende Regel gilt für [check_2_sma].[systemstatus] : Systemstatus SMA
        #
        # 1. Der Wert aus [SM_AUFTRAG_PSL_ALLGEMEIN].[systemstatus] wird für jeden SMA in das Feld
        # [check_2_sma].[systemstatus] übertragen.

        self.df_result = pd.merge(
            left=self.df_result,
            right=self.df_source_data[["sm_auftragsnummer", "systemstatus"]],
            left_on=["sm_auftragsnummer"],
            right_on=["sm_auftragsnummer"],
            how="left",
        )

        # Folgende Regel gilt für [check_2_sma].[obligo]: Obligo Gesamt des SMA
        #
        # 1. Aus der aktuell vorliegenden Eingangstabelle [psl_koa_obligo] wird für jeden SMA
        # [psl_koa_plan_obligo].[auftrags_nr] die Summe aus dem Feld [psl_koa_plan_obligo].[obligo] gebildet und
        # in das Feld [check_2_sma].[obligo] übertragen. Dabei handelt es sich um Währungsbeträge mit 2
        # Nachkommastellen. Ist zu einem SMA [check_2_sma]].[sm_auftragsnummer] aus der Tabelle [check_2_sma] kein
        # zugehöriger SMA [psl_koa_plan_obligo].[auftrags_nr] in der Tabelle [psl_koa_plan_obligo] vorhanden,
        # so wird in das Feld der Wert 0,00 eingetragen. Zum Zeitpunkt der Auswertung muss sichergestellt sein,
        # das die Eingangstabelle [psl_koa_plan_obligo] vollständig geladen ist und nicht gerade eine Datenladung
        # erfolgt.

        df_obligo = (
            self.df_obligo.groupby(["sma_psl"])
            .sum("obligo")
            .reset_index()[["sma_psl", "obligo"]]
            .rename(columns={"sma_psl": "sm_auftragsnummer"})
        )

        self.df_result = pd.merge(
            left=self.df_result,
            right=df_obligo,
            left_on=["sm_auftragsnummer"],
            right_on=["sm_auftragsnummer"],
            how="left",
        ).fillna({"obligo": 0.00})

        # Folgende Regel gilt für [check_2_sma].[abschluss_jahr_komplett]: TABG oder ABGS und Obligo = 0
        #
        # 1. ist das Feld [check_2_sma].[obligo] = 0,00 und ist im Feld [check_2_sma].[abschluss_jahr]
        # ein Eintrag vorhanden, so wird der Eintrag aus [check_2_sma].[abschluss_jahr] in das Feld
        # [check_2_sma].[abschluss_jahr_komplett] übertragen.
        #
        # 2. Ist das Feld [check_2_sma].[obligo] <> 0,00 und im Feld [check_2_sma].[in_basis_1_wms_psl] ist der
        # Wert "1" für Ja eingetragen, so wird in das Feld [check_2_sma].[abschluss_jahr_komplett]
        # der Wert NULL eingetragen, bzw. bleibt leer.
        #
        #  Ist das Feld [check_2_sma].[obligo] <> 0,00 und im Feld [check_2_sma].[in_basis_1_wms_psl] ist der Wert
        #  "0" für Nein eingetragen, so wird der Eintrag aus [check_2_sma].[abschluss_jahr] in das
        #  Feld [check_2_sma].[abschluss_jahr_komplett] übertragen

        df_obligo_null = self.df_result[
            # Regel 1
            (
                (self.df_result["obligo"] == 0.00)
                & (~(self.df_result["abschluss_jahr"].isnull()) & ~(self.df_result["abschluss_jahr"] == ""))
            )
            # Regel 3
            | (~(self.df_result["obligo"] == 0.00) & (self.df_result["in_basis_1_wms_psl"] == 0))
        ][["sm_auftragsnummer", "abschluss_jahr"]].rename(columns={"abschluss_jahr": "abschluss_jahr_komplett"})

        self.df_result = pd.merge(
            left=self.df_result,
            right=df_obligo_null,
            left_on=["sm_auftragsnummer"],
            right_on=["sm_auftragsnummer"],
            how="left",
        )

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("check_2_sma")


@catchall
def run(stop_event=None, processbar=True):
    # truncate target table
    db = DatabaseManager()
    db.truncate(["check_2_sma"])

    mp = Multiprocess(stop_event=stop_event, processbar=processbar, max_package_size=100)
    mp.start_multiplexer(table_name="sm_auftrag_psl_allgemein", processing_class=Check2SMA)
    mp.start_progressbar(table_name="sm_auftrag_psl_allgemein")


if __name__ == "__main__":
    sys._mp_off = True

    run()
