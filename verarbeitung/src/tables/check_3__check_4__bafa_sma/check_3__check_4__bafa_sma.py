#!/usr/bin/python
# coding=utf-8
import gc
import timeit
from datetime import datetime
from os import makedirs, path

import numpy as np
import pandas as pd
from sqlalchemy import text

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.helper.helper import catchall, debugprint
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess

TARGET_BAFA_TABLE = "check_3_bafa_sma"
PROCESSING_GRUPPE = "processing_abschluss_gruppe"
BASIS_1_GRUPPE_SMA = "basis_1_gruppe_sma"
TARGET_SMA_TABLE = "check_4_sma_bafa"

EXTRA_DEBUG_FOLDER = "extra_debug_data"


def prepare_base_folder(dump_dir: str) -> None:
    # base folder
    makedirs(path.join(dump_dir), exist_ok=True)
    makedirs(path.join(dump_dir, EXTRA_DEBUG_FOLDER), exist_ok=True)


class Check34BafaSma(Task):
    def __init__(
        self, job: Job, stop_event=None, debug=False, database_manager=DatabaseManager(), year_offset: int = 3
    ):
        super().__init__(job, stop_event, database_manager=database_manager)
        self.year_offset = year_offset
        self.max_year = datetime.now().year - self.year_offset
        self.debug = debug

    def load_data(self):
        debugprint("Offset")
        debugprint(self.offset)
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""        
                SELECT
                gruppe.abschluss_gruppe as  grp_zu_loeschen,
                basis.rekursion as rekursion,
                check1.requidbafa as requidbafa,
                check1.abschluss_jahr as abschluss_jahr,
                check1.in_basis_1_wms_psl as grp_in_basis_1_wms_psl,
                check1.abschluss_gruppe as abschluss_gruppe,
                check1.gruppe as gruppe,
                check2.sm_auftragsnummer as sma,
                check2.abschluss_jahr_komplett as abschluss_jahr_komplett,
                check2.systemstatus as systemstatus,
                check2.in_basis_1_wms_psl as in_basis_1_wms_psl
                FROM (SELECT abschluss_gruppe FROM {PROCESSING_GRUPPE} LIMIT {self.offset},{self.limit}) as gruppe
                LEFT JOIN {BASIS_1_GRUPPE_SMA} as basis ON basis.abschluss_gruppe = gruppe.abschluss_gruppe
                LEFT JOIN check_1_wfmt as check1 ON check1.gruppe = basis.gruppe
                LEFT JOIN check_2_sma as check2 on check2.sm_auftragsnummer=basis.sma_verbunden
            """
        )

    def worker(self):
        self.df_source_data["abschluss_gruppe"] = pd.to_numeric(self.df_source_data["abschluss_gruppe"]).fillna(0)
        self.df_source_data["abschluss_jahr_komplett"] = pd.to_numeric(
            self.df_source_data["abschluss_jahr_komplett"]
        ).fillna(0)
        self.df_source_data["abschluss_gruppe"] = self.df_source_data["abschluss_gruppe"].astype(int)
        self.df_source_data["abschluss_jahr_komplett"] = self.df_source_data["abschluss_jahr_komplett"].astype(int)
        self.df_source_data.loc[
            (self.df_source_data["abschluss_jahr_komplett"] > self.max_year)
            & (self.df_source_data["systemstatus"] != "ABGS"),
            ["abschluss_jahr_komplett"],
        ] = 0

        self.df_source_data.loc[(self.df_source_data["abschluss_gruppe"] > self.max_year), ["abschluss_gruppe"]] = 0

        df_test = (
            self.df_source_data[
                (self.df_source_data["abschluss_jahr_komplett"] == 0) | (self.df_source_data["abschluss_gruppe"] == 0)
            ]
            .groupby("grp_zu_loeschen")
            .first()
            .reset_index()
        )

        df_test["loeschen_ohne_sma"] = 0
        self.df_source_data = pd.merge(
            left=self.df_source_data,
            right=df_test[["loeschen_ohne_sma", "grp_zu_loeschen"]],
            left_on="grp_zu_loeschen",
            right_on="grp_zu_loeschen",
            how="left",
        )

        del df_test
        gc.collect()

        self.df_source_data["loeschen_ohne_sma"] = self.df_source_data["loeschen_ohne_sma"].fillna(1)

    def write_result(self):
        df_check3_result = self.df_source_data[
            [
                "requidbafa",
                "gruppe",
                "abschluss_gruppe",
                "grp_in_basis_1_wms_psl",
                "grp_zu_loeschen",
                "loeschen_ohne_sma",
                "rekursion",
            ]
        ]
        df_check3_result = df_check3_result[(df_check3_result["requidbafa"] != "")]
        df_check4_result = self.df_source_data[
            [
                "sma",
                "in_basis_1_wms_psl",
                "systemstatus",
                "abschluss_jahr_komplett",
                "grp_zu_loeschen",
                "loeschen_ohne_sma",
                "rekursion",
            ]
        ]
        df_check4_result = df_check4_result[(df_check4_result["sma"] > 0)]
        df_check3_result.to_sql_replace(TARGET_BAFA_TABLE, database_manager=self.db)
        del df_check3_result
        df_check4_result.to_sql_replace(TARGET_SMA_TABLE, database_manager=self.db)
        del df_check4_result

        gc.collect()


class GetAllGruppeZuLöschen(Task):
    """'build' all groups"""

    def __init__(
        self,
        job: Job,
        stop_event=None,
        df_check_1=None,
        df_source_data=None,
        debug=False,
        dump_dir="",
        database_manager=DatabaseManager(),
    ):
        super().__init__(job, stop_event, database_manager=database_manager)
        self.df_source_data = df_source_data[["gruppe", "sma_verbunden"]]
        self.df_check_1 = df_check_1
        self.df_result = pd.DataFrame()
        self.debug = debug
        self.dump_dir = dump_dir
        self.process()

    def load_data(self):
        # not used here
        pass

    def get_check_1_bafas_by_group(self, sma) -> pd.DataFrame:
        df_data = self.df_check_1[self.df_check_1["sma_verbunden"] == sma]
        return df_data

    def recursion(self, df_bafas: pd.DataFrame, sma: int, recursion_counter: int = 0) -> (pd.DataFrame, int):

        df_data = self.df_check_1[
            (self.df_check_1["gruppe"].isin(df_bafas["gruppe"]))
            | (self.df_check_1["sma_verbunden"].isin(df_bafas["sma_verbunden"]))
        ]
        if len(df_data.index) == len(df_bafas.index):
            # end of the line, return data
            return df_data, recursion_counter
        else:
            # increment recursion counter for each step
            recursion_counter += 1
            # dump dataframe if in debug mode
            if self.debug:
                self.write_debug_data(df_data, sma, recursion_counter)
            # enter next recursion step
            return self.recursion(df_data, sma, recursion_counter)

    def write_debug_data(self, df_data: pd.DataFrame, sma, recursion_counter: int) -> None:
        # dump data for debug purposes
        makedirs(path.join(self.dump_dir, str(sma)), exist_ok=True)
        df_data.to_csv(
            path.join(path.join(self.dump_dir, str(sma)), f"sma_{sma}_Rekursion_Stufe_{recursion_counter}.csv"),
            index=False,
            sep=";",
        )

    def process(self) -> None:
        """

        Returns:
            None
        """
        debugprint("start Process")
        self.used_groups = set(list())
        self.df_result = pd.DataFrame()
        smas = self.df_check_1["sma_verbunden"].drop_duplicates().tolist()
        _start = timeit.default_timer()
        debugprint("start")
        debugprint(len(smas))
        firstSmaLength = len(smas)
        i = 0
        count_continue = 0
        concat_frames = []

        # go over all smas
        while len(smas) > 0:
            sma = smas[0]
            i = i + 1
            if i % 100 == 0:
                debugprint(f"Verarbeitete Sma {firstSmaLength - len(smas)} von {firstSmaLength}")
                debugprint(f"100 took {(timeit.default_timer() - _start)} seconds")
                _start = timeit.default_timer()
            # get initial data batch
            _df_bafas = self.get_check_1_bafas_by_group(sma)
            if self.debug:
                self.write_debug_data(_df_bafas, sma, 0)
            _df_bafas, recursion_counter = self.recursion(_df_bafas, sma)

            # set recursion depth
            _df_bafas["rekursion"] = recursion_counter
            # set abschluss gruppe
            _df_bafas["abschluss_gruppe"] = sma
            deleteSmas = _df_bafas["sma_verbunden"].to_list()
            smas = list(set(smas) - set(deleteSmas))
            concat_frames.append(_df_bafas)
        debugprint(f"Recursion took {(timeit.default_timer() - _start)} seconds")
        if len(concat_frames) > 0:
            self.df_result = pd.concat(concat_frames, axis=0, ignore_index=True)
            self.df_result = self.df_result.drop_duplicates()
            self.df_result = pd.merge(
                left=self.df_result.groupby("sma_verbunden")
                .first()
                .reset_index()[["sma_verbunden", "abschluss_gruppe", "rekursion"]],
                right=self.df_source_data,
                on=["sma_verbunden"],
                how="left",
            ).drop_duplicates()
            self.df_result = pd.merge(
                left=self.df_result.groupby("gruppe")
                .first()
                .reset_index()[["gruppe", "abschluss_gruppe", "rekursion"]],
                right=self.df_source_data,
                on=["gruppe"],
                how="left",
            ).drop_duplicates()
            self.df_result = pd.merge(
                left=self.df_result.groupby("sma_verbunden")
                .first()
                .reset_index()[["sma_verbunden", "abschluss_gruppe", "rekursion"]],
                right=self.df_source_data,
                on=["sma_verbunden"],
                how="left",
            ).drop_duplicates()
            self.df_result = pd.merge(
                left=self.df_result.groupby("gruppe")
                .first()
                .reset_index()[["gruppe", "abschluss_gruppe", "rekursion"]],
                right=self.df_source_data,
                on=["gruppe"],
                how="left",
            ).drop_duplicates()
            self.df_result.to_sql_replace(BASIS_1_GRUPPE_SMA, database_manager=self.db)
        debugprint(f"count {count_continue}")
        debugprint("fertig")

    def worker(self):
        self.process()

    def write_result(self):
        pass


def load_shared_data(
    debug: bool = False, dump_dir: str = "", database_manager: DatabaseManager = DatabaseManager()
) -> (pd.DataFrame, pd.DataFrame):
    """Load needed data for processing and ideally sort out all bafas/groups which don't need to go into recursion

    Returns:

    """

    debugprint("Loading shared data")

    # load check_1 data and connect it to sma_verbunden
    df_check_1 = database_manager.get_dataframe_by_query(
        f"""Select check_1.gruppe, basis.sma_verb_ohne_zr as sma_verbunden
            FROM check_1_wfmt as check_1 
            LEFT JOIN check_7_basis_1_wfm_psl as basis ON basis.requidbafa = check_1.requidbafa
            WHERE basis.sma_verb_ohne_zr > 0
        """
    ).astype({"sma_verbunden": "Int64"}, errors="ignore")

    # group by sma
    df_check_1 = df_check_1.groupby(["gruppe", "sma_verbunden"]).first().reset_index()
    if df_check_1.empty:
        return pd.DataFrame(columns=["gruppe", "sma_verbunden"]), pd.DataFrame(
            columns=["gruppe", "sma_verbunden", "abschluss_gruppe"]
        )

    df_check_1 = df_check_1[["gruppe", "sma_verbunden"]]

    # count 'connected' groups and smas
    df_count_gruppe = (
        df_check_1.groupby(["gruppe"]).count().rename(columns={"sma_verbunden": "sma_verbunden_count"}).reset_index()
    )
    df_count_sma = (
        df_check_1.groupby(["sma_verbunden"]).count().rename(columns={"gruppe": "gruppe_count"}).reset_index()
    )
    df_check_1 = pd.merge(left=df_check_1, right=df_count_gruppe, left_on="gruppe", right_on="gruppe")
    df_check_1 = pd.merge(left=df_check_1, right=df_count_sma, left_on="sma_verbunden", right_on="sma_verbunden")

    # get all smas which are the only smas of a bafa
    df_sma = df_check_1[df_check_1["sma_verbunden_count"] == 1]
    df_sma["sma_verbunden_key"] = df_sma["sma_verbunden"].astype("Int64", errors="ignore")
    df_sma = (
        df_sma[["sma_verbunden_key", "sma_verbunden"]]
        .groupby(["sma_verbunden_key", "sma_verbunden"])
        .first()
        .reset_index()
    )
    df_gruppe = df_check_1[df_check_1["gruppe_count"] == 1]
    df_gruppe["gruppe_key"] = df_gruppe["gruppe"]
    if not df_gruppe.empty:
        df_gruppe = df_gruppe[["gruppe_key", "gruppe"]].groupby(["gruppe", "gruppe_key"]).first().reset_index()

    df_check_1 = pd.merge(
        left=df_check_1, right=df_sma, left_on="sma_verbunden", right_on="sma_verbunden", how="left"
    ).astype({"sma_verbunden_key": "Int64"}, errors="ignore")

    if not df_gruppe.empty:
        df_check_1 = pd.merge(left=df_check_1, right=df_gruppe, left_on="gruppe", right_on="gruppe", how="left").astype(
            {"sma_verbunden_key": "Int64"}, errors="ignore"
        )
    else:
        df_check_1["gruppe_key"] = None

    df_recursion1 = df_check_1[(df_check_1["sma_verbunden_count"] != 1) & (df_check_1["gruppe_count"] != 1)][
        ["gruppe", "sma_verbunden", "sma_verbunden_key", "gruppe_key"]
    ].astype(
        {
            "sma_verbunden": "Int64",
            "sma_verbunden_key": "Int64",
        },
        errors="ignore",
    )
    df_count_gruppe = (
        df_recursion1[["gruppe", "sma_verbunden"]]
        .groupby(["gruppe"])
        .count()
        .rename(columns={"sma_verbunden": "sma_verbunden_count1"})
        .reset_index()
    )
    df_count_sma = (
        df_recursion1[["gruppe", "sma_verbunden"]]
        .groupby(["sma_verbunden"])
        .count()
        .rename(columns={"gruppe": "gruppe_count1"})
        .reset_index()
    )
    df_recursion1 = pd.merge(left=df_recursion1, right=df_count_gruppe, left_on="gruppe", right_on="gruppe")
    df_recursion1 = pd.merge(left=df_recursion1, right=df_count_sma, left_on="sma_verbunden", right_on="sma_verbunden")
    df_recursion1 = df_recursion1[
        ["gruppe", "sma_verbunden", "sma_verbunden_key", "gruppe_key", "sma_verbunden_count1", "gruppe_count1"]
    ]
    df_recursion1["abschluss_gruppe"] = ""
    df_recursion1.loc[(df_recursion1["gruppe_count1"] == 1), ["abschluss_gruppe"]] = df_recursion1["gruppe_key"]
    df_recursion1.loc[(df_recursion1["sma_verbunden_count1"] == 1), ["abschluss_gruppe"]] = df_recursion1[
        "sma_verbunden_key"
    ]
    df_sma = df_recursion1[df_recursion1["sma_verbunden_count1"] == 1]
    df_sma["sma_verbunden_key1"] = df_sma["abschluss_gruppe"].astype("Int64")
    df_sma = (
        df_sma[["sma_verbunden_key1", "sma_verbunden_count1", "gruppe"]]
        .groupby(["sma_verbunden_key1", "gruppe"])
        .first()
        .reset_index()
    )
    df_gruppe = df_recursion1[df_recursion1["gruppe_count1"] == 1]
    df_gruppe["gruppe_key1"] = df_gruppe["abschluss_gruppe"]
    df_gruppe = (
        df_gruppe[["gruppe_key1", "sma_verbunden", "gruppe_count1"]]
        .groupby(["sma_verbunden", "gruppe_key1"])
        .first()
        .reset_index()
    )

    df_check_1 = pd.merge(left=df_check_1, right=df_sma, left_on="gruppe", right_on="gruppe", how="left").astype(
        {"sma_verbunden_key1": "Int64"}
    )

    df_check_1 = pd.merge(
        left=df_check_1, right=df_gruppe, left_on="sma_verbunden", right_on="sma_verbunden", how="left"
    )
    df_check_1["abschluss_gruppe"] = np.nan
    df_check_1.loc[(df_check_1["gruppe_count1"] == 1), ["abschluss_gruppe"]] = df_check_1["gruppe_key1"]

    df_check_1.loc[(df_check_1["sma_verbunden_count1"] == 1), ["abschluss_gruppe"]] = df_check_1["sma_verbunden_key1"]

    df_check_1.loc[
        ((df_check_1["abschluss_gruppe"].isnull()) & (df_check_1["sma_verbunden_count"] == 1)), ["abschluss_gruppe"]
    ] = df_check_1["sma_verbunden_key"]

    df_check_1.loc[
        ((df_check_1["abschluss_gruppe"].isnull()) & (df_check_1["gruppe_count"] == 1)), ["abschluss_gruppe"]
    ] = df_check_1["gruppe_key"]

    df_ready_recursion = df_check_1[df_check_1["abschluss_gruppe"].isnull()][["gruppe", "sma_verbunden"]]
    df_result = df_check_1[["gruppe", "sma_verbunden", "abschluss_gruppe"]]

    df_result["rekursion"] = 1

    df_result.to_sql_replace(BASIS_1_GRUPPE_SMA, database_manager=database_manager)

    if debug:
        df_ready_recursion.to_csv(
            path.join(dump_dir, EXTRA_DEBUG_FOLDER, "erweiterte_rekursion_basis.csv"), index=False, sep=";"
        )
        df_result.to_csv(path.join(dump_dir, EXTRA_DEBUG_FOLDER, "grp_zu_loeschen.csv"), index=False, sep=";")

    debugprint("Länge Recursion")
    debugprint(len(df_ready_recursion))
    return df_ready_recursion, df_result


@catchall
def run_pre(stop_event=None, processbar=True, debug=False, dump_dir="", database_manager=DatabaseManager()):
    """the idea is basically find all groups here .....

    Args:
        stop_event:
        processbar:
    """

    if debug:
        prepare_base_folder(dump_dir)

    database_manager
    database_manager.truncate([BASIS_1_GRUPPE_SMA])

    df_check_1, df_result = load_shared_data(debug, dump_dir=dump_dir, database_manager=database_manager)

    GetAllGruppeZuLöschen(
        job=None,
        stop_event=stop_event,
        df_check_1=df_check_1,
        df_source_data=df_result,
        debug=debug,
        dump_dir=dump_dir,
        database_manager=database_manager,
    )


@catchall
def run(
    stop_event=None, processbar=True, debug=False, dump_dir="", database_manager=None, additional_data: dict = None
):
    """... and fill teh missing data in here afterwards

    Args:
        stop_event:
        processbar:

    Returns:

    """
    year_offset = additional_data.get("year_offset", 3) if additional_data else None
    if not year_offset:
        year_offset = 3

    external_database_manager = True
    if database_manager is None:
        database_manager = DatabaseManager()
        external_database_manager = False

    # Truncate processing tables
    database_manager.truncate([PROCESSING_GRUPPE])
    database_manager.execute(
        f"INSERT INTO {PROCESSING_GRUPPE} SELECT DISTINCT abschluss_gruppe FROM {BASIS_1_GRUPPE_SMA}"
    )

    # truncate target table
    database_manager.truncate([TARGET_BAFA_TABLE, TARGET_SMA_TABLE])

    max_year = datetime.now().year - year_offset

    # get not connectable smas
    df_sma = database_manager.get_dataframe_by_query(
        f"SELECT check2.* FROM check_2_sma as check2"
        f" LEFT JOIN {BASIS_1_GRUPPE_SMA} as basis ON check2.sm_auftragsnummer=basis.sma_verbunden"
        f" WHERE basis.sma_verbunden is null or check2.in_basis_1_wms_psl=0"
    )
    df_sma = df_sma.rename(columns={"sm_auftragsnummer": "sma"})
    df_sma["grp_zu_loeschen"] = df_sma["sma"]
    df_sma["abschluss_jahr_komplett"] = pd.to_numeric(df_sma["abschluss_jahr_komplett"]).fillna(0)
    df_sma["abschluss_jahr_komplett"] = df_sma["abschluss_jahr_komplett"].astype(int)
    df_sma.loc[
        (df_sma["abschluss_jahr_komplett"] > max_year) & (df_sma["systemstatus"] != "ABGS"), ["abschluss_jahr_komplett"]
    ] = 0
    df_sma["loeschen_ohne_sma"] = 1

    df_sma.loc[
        df_sma["abschluss_jahr_komplett"] == 0,
        ["loeschen_ohne_sma"],
    ] = 0

    df_sma["rekursion"] = 0
    df_sma = df_sma[
        [
            "sma",
            "in_basis_1_wms_psl",
            "systemstatus",
            "abschluss_jahr_komplett",
            "grp_zu_loeschen",
            "loeschen_ohne_sma",
            "rekursion",
        ]
    ]

    df_sma.to_sql_replace(TARGET_SMA_TABLE, database_manager=database_manager)
    if debug:
        df_sma.to_csv(path.join(dump_dir, EXTRA_DEBUG_FOLDER, "smas_nicht_in_basis_1.csv"), index=False, sep=";")

    del df_sma

    # process unconnectable bafas
    df_bafa = database_manager.get_dataframe_by_query(
        f"SELECT check1.* FROM check_1_wfmt as check1 "
        f"LEFT JOIN {BASIS_1_GRUPPE_SMA} as basis ON check1.gruppe=basis.gruppe WHERE basis.gruppe is null"
    )

    df_bafa["abschluss_gruppe"] = pd.to_numeric(df_bafa["abschluss_gruppe"]).fillna(0)
    df_bafa["abschluss_gruppe"] = df_bafa["abschluss_gruppe"].astype(int)
    df_bafa.loc[(df_bafa["abschluss_gruppe"] > max_year), ["abschluss_gruppe"]] = 0
    df_bafa["loeschen_ohne_sma"] = 1

    df_bafa.loc[
        df_bafa["abschluss_gruppe"] == 0,
        ["loeschen_ohne_sma"],
    ] = 0

    df_bafa["grp_zu_loeschen"] = df_bafa["gruppe"]
    df_bafa["rekursion"] = 0
    df_bafa = df_bafa[
        [
            "requidbafa",
            "gruppe",
            "abschluss_gruppe",
            "grp_in_basis_1_wms_psl",
            "grp_zu_loeschen",
            "loeschen_ohne_sma",
            "rekursion",
        ]
    ]

    df_bafa.to_sql_replace(TARGET_BAFA_TABLE, database_manager=database_manager)

    del df_bafa

    gc.collect()

    # process the remaining data
    _kwargs = {"stop_event": stop_event, "processbar": processbar, "max_package_size": 5000, "min_package_size": 5000}
    if external_database_manager:
        _kwargs["database_manager"] = database_manager
    if year_offset:
        _kwargs["year_offset"] = year_offset

    mp = Multiprocess(**_kwargs)
    mp.start_multiplexer(table_name=PROCESSING_GRUPPE, processing_class=Check34BafaSma)
    mp.start_progressbar(table_name=PROCESSING_GRUPPE)


def dump_table_data(table_name: str, step: str, database_manager: DatabaseManager, dump_dir: str) -> None:
    # dump data for debug purposes

    _data = database_manager.get_dataframe_by_table(table_name)

    _data.to_csv(
        path.join(path.join(dump_dir, f"{table_name}_{step}.csv")),
        index=False,
        sep=";",
    )


@catchall
def run_post(stop_event=None, processbar=True, debug=False, dump_dir="", database_manager=None):

    # Folgende Regel gilt für [check_4_sma_bafa].[loeschen_ohne_zr]:
    # 1. Alle Einträge aus dem Feld [check_4_sma_bafa].[loeschen_ohne_sma] werden 1:1 in das Feld
    # [check_4_sma_bafa].[loeschen_ohne_zr] übertragen.
    #
    # 2. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_sum] der im Feld
    # [check_4_sma_bafa].[loeschen_ohne_sma] den Eintrag 1= Ja hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ohne_sma] ebenfalls den Eintrag 1= Ja haben. Ist dies der Fall, bleibt der Eintrag
    # für diesen SMA [check_4_sma_bafa].[sma] im Feld [check_4_sma_bafa].[loeschen_ohne_zr] unverändert.
    #
    # 3. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_sum] der im Feld
    # [check_4_sma_bafa].[loeschen_ohne_sma] den Eintrag 1= Ja hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ohne_sma] ebenfalls den Eintrag 1= Ja haben. Ist dies nicht der Fall, so wird der
    # Eintrag für diesen SMA [check_4_sma_bafa].[sma] im Feld [check_4_sma_bafa].[loeschen_ohne_zr]
    # auf 0 = Nein geändert.
    #
    if database_manager is None:
        database_manager = DatabaseManager()

    query_check_4_loeschen_ohne_zr_base = """
        UPDATE check_4_sma_bafa as check_4
        SET check_4.loeschen_ohne_zr = check_4.loeschen_ohne_sma
        """
    database_manager.deadlock_save_execute(query_check_4_loeschen_ohne_zr_base)

    if debug:
        # dump check_4
        dump_table_data("check_4_sma_bafa", "loeschen_ohne_zr_regel_1", database_manager, dump_dir)

    # [check_4_sma_bafa].[loeschen_ohne_zr]
    query_check_4_loeschen_ohne_zr = """UPDATE check_4_sma_bafa as check_4
                INNER JOIN (
                    Select DISTINCT(check7.sma_verbunden) as sma from check_7_basis_1_wfm_psl as check7
                    INNER JOIN check_4_sma_bafa as check4 on check7.sma_verb_nur_sum = check4.sma
                    INNER JOIN check_3_bafa_sma as check3 on check7.requidbafa = check3.requidbafa
                    Where check7.sma_verb_nur_sum > 0 
                    AND check3.loeschen_ohne_sma = 0 
                    AND check4.loeschen_ohne_sma = 1
                ) as base
                ON base.sma = check_4.sma
                SET check_4.loeschen_ohne_zr = 0, check_4.loeschen = 0"""
    database_manager.deadlock_save_execute(query_check_4_loeschen_ohne_zr)

    if debug:
        # dump check_4
        dump_table_data("check_4_sma_bafa", "loeschen_ohne_zr_regel_2_3", database_manager, dump_dir)

    # Folgende Regel gilt für [check_3_bafa_sma].[loeschen_ohne_zr]:
    #
    # 1. Alle Einträge aus dem Feld [check_3_bafa_sma].[loeschen_ohne_sma] werden 1:1 in das Feld
    # [check_3_bafa_sma].[loeschen_ohne_zr] übertragen.
    #
    # 2. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_sum] der im Feld
    # [check_4_sma_bafa].[loeschen_ohne_zr] den Eintrag 0= Nein hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ ohne_sma] ebenfalls den Eintrag 0 = Nein haben. Ist dies der Fall, bleibt der
    # Eintrag für diese WFMT Aufträge [check_3_bafa_sma].[requidbafa] im Feld [check_3_bafa_sma].[loeschen_ohne_zr]
    # unverändert.
    #
    # 3. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_sum] der im Feld
    # [check_4_sma_bafa].[loeschen_ohne_zr] den Eintrag 0= Nein hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ ohne_sma] ebenfalls den Eintrag 0= Nein haben. Ist dies nicht der Fall, so wird
    # der Eintrag im Feld [check_3_bafa_sma].[loeschen_ohne_zr] für diese WFMT Aufträge
    # [check_3_bafa_sma].[requidbafa] auf 0 = Nein geändert.

    query_check_3_loeschen_ohne_zr_base = """
        UPDATE check_3_bafa_sma as check_3
        SET check_3.loeschen_ohne_zr = check_3.loeschen_ohne_sma
        """
    database_manager.deadlock_save_execute(query_check_3_loeschen_ohne_zr_base)

    if debug:
        dump_table_data("check_3_bafa_sma", "loeschen_ohne_zr_regel_1", database_manager, dump_dir)

    query_check_3_loeschen_ohne_zr = """UPDATE check_3_bafa_sma as check_3
            INNER JOIN(
                Select DISTINCT(check7.requidbafa) as requidbafa from check_7_basis_1_wfm_psl as check7
                INNER JOIN check_4_sma_bafa as check4 on check7.sma_verb_nur_sum = check4.sma
                INNER JOIN check_3_bafa_sma as check3 on check7.requidbafa = check3.requidbafa
                Where check7.sma_verb_nur_sum > 0 
                AND check4.loeschen_ohne_zr = 0
                AND check3.loeschen_ohne_sma = 1
            ) as base
            ON base.requidbafa = check_3.requidbafa
            SET check_3.loeschen_ohne_zr = 0, check_3.loeschen = 0
            """
    database_manager.deadlock_save_execute(query_check_3_loeschen_ohne_zr)

    if debug:
        dump_table_data("check_3_bafa_sma", "loeschen_ohne_zr_regel_2_3", database_manager, dump_dir)

    # Folgende Regel gilt für [check_4_sma_bafa].[loeschen]:
    #
    # 1. Alle Einträge aus dem Feld [check_4_sma_bafa].[loeschen_ohne_zr] werden 1:1 in das Feld
    # [check_4_sma_bafa].[loeschen] übertragen.
    #
    # 2. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_zr] der im Feld
    # [check_4_sma_bafa].[loeschen] den Eintrag 1= Ja hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ohne_zr] ebenfalls den Eintrag 1= Ja haben. Ist dies der Fall, bleibt der Eintrag
    # für diesen SMA [check_4_sma_bafa].[sma] im Feld [check_4_sma_bafa].[loeschen] unverändert.
    #
    # 3. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_zr] der im Feld
    # [check_4_sma_bafa].[loeschen] den Eintrag 1= Ja hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ohne_zr] ebenfalls den Eintrag 1= Ja haben. Ist dies nicht der Fall, so wird der
    # Eintrag für diesen SMA [check_4_sma_bafa].[sma] im Feld [check_4_sma_bafa].[loeschen] auf 0 = Nein geändert.

    query_check_4_loeschen_basis = """
        UPDATE check_4_sma_bafa as check_4
        SET check_4.loeschen = check_4.loeschen_ohne_zr
        """
    database_manager.deadlock_save_execute(query_check_4_loeschen_basis)

    if debug:
        dump_table_data("check_4_sma_bafa", "loeschen_regel_1", database_manager, dump_dir)

    query_check_4_loeschen = """UPDATE check_4_sma_bafa as check_4
                INNER JOIN (
                    Select DISTINCT(check7.sma_verbunden) as sma from check_7_basis_1_wfm_psl as check7
                    INNER JOIN check_4_sma_bafa as check4 on check7.sma_verb_nur_zr = check4.sma
                    INNER JOIN check_3_bafa_sma as check3 on check7.requidbafa = check3.requidbafa
                    Where check7.sma_verb_nur_zr > 0 
                    AND check4.loeschen = 1
                    AND check3.loeschen_ohne_zr = 0
                ) as base
                ON base.sma = check_4.sma
                SET check_4.loeschen = 0"""
    database_manager.deadlock_save_execute(query_check_4_loeschen)

    if debug:
        dump_table_data("check_4_sma_bafa", "loeschen_regel_2_3", database_manager, dump_dir)

    # Folgende Regel gilt für [check_3_bafa_sma].[loeschen]:
    #
    # 1. Alle Einträge aus dem Feld [check_3_bafa_sma].[loeschen_ohne_zr] werden 1:1 in das Feld
    # [check_3_bafa_sma].[loeschen] übertragen.
    #
    # 2. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_zr] der im Feld
    # [check_4_sma_bafa].[loeschen_ohne_zr] den Eintrag 0= Nein hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ohne_zr] ebenfalls den Eintrag 0 = Nein haben. Ist dies der Fall, bleibt der Eintrag
    # für diese WFMT Aufträge [check_3_bafa_sma].[requidbafa] im Feld [check_3_bafa_sma].[loeschen] unverändert.
    #
    # 3. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_zr] der im Feld
    # [check_4_sma_bafa].[loeschen_ohne_zr] den Eintrag 0= Nein hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl]
    # geprüft ob alle damit Verbundenen WFMT Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld
    # [check_3_bafa_sma].[loeschen_ohne_zr] ebenfalls den Eintrag 0= Nein haben. Ist dies nicht der Fall, so wird der
    # Eintrag im Feld [check_3_bafa_sma].[loeschen] für diese WFMT Aufträge [check_3_bafa_sma].[requidbafa] auf
    # 0 = Nein geändert. Zusätzlich werden bei dem zu diesem WFMT Auftrag gehörenden BA/FA Kaskade
    # [check_1_wfmt].[gruppe], für jeden Auftrag dieser Gruppe im Feld [check_3_bafa_sma].[loeschen] der Wert auf
    # 0 = Nein geändert.

    query_check_3_loeschen_basis = """
        UPDATE check_3_bafa_sma as check_3
        SET check_3.loeschen = check_3.loeschen_ohne_zr
        """
    database_manager.deadlock_save_execute(query_check_3_loeschen_basis)

    if debug:
        dump_table_data("check_3_bafa_sma", "loeschen_regel_1", database_manager, dump_dir)

    query_check_3_loeschen_2 = """UPDATE check_3_bafa_sma as check_3
    INNER JOIN(
        Select DISTINCT(check3.gruppe) as gruppe FROM check_3_bafa_sma as check3
        INNER JOIN check_7_basis_1_wfm_psl as check7 on check7.requidbafa = check3.requidbafa
        INNER JOIN check_4_sma_bafa as check4 on check7.sma_verb_nur_zr = check4.sma
        Where check7.sma_verb_nur_zr > 0 
        AND check4.loeschen_ohne_zr = 0
        AND check3.loeschen_ohne_zr = 1
    ) as base on base.gruppe = check_3.gruppe
    SET check_3.loeschen = 0
    """
    database_manager.deadlock_save_execute(query_check_3_loeschen_2)

    if debug:
        dump_table_data("check_3_bafa_sma", "loeschen_regel_2_3", database_manager, dump_dir)

    # Folgende Regel gilt für [check_4_sma_bafa].[loeschen]:
    # 1. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_zr] der im Feld [check_4_sma_bafa].[loeschen]
    # den Eintrag 1= Ja hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl] geprüft ob alle damit Verbundenen WFMT
    # Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld [check_3_bafa_sma].[loeschen] ebenfalls den Eintrag
    # 1= Ja haben. Ist dies der Fall, bleibt der Eintrag für diesen SMA [check_4_sma_bafa].[sma] im Feld
    # [check_4_sma_bafa].[loeschen] unverändert.
    #
    # 2. Für jeden SMA im Feld [check_7_basis_1_wfm_psl].[sma_verb_nur_zr] der im Feld [check_4_sma_bafa].[loeschen]
    # den Eintrag 1= Ja hat, wird anhand der Tabelle [check_7_basis_1_wfm_psl] geprüft ob alle damit Verbundenen WFMT
    # Aufträge [check_7_basis_1_wfm_psl].[requidbafa] im Feld [check_3_bafa_sma].[loeschen] ebenfalls den
    # Eintrag 1= Ja haben. Ist dies nicht der Fall, so wird der Eintrag für diesen SMA [check_4_sma_bafa].[sma] im Feld
    # [check_4_sma_bafa].[loeschen] auf 0 = Nein geändert.

    query_check_4_loeschen_rekursion = """UPDATE check_4_sma_bafa as check4
    INNER JOIN (
        SELECT DISTINCT(sma_verb_nur_zr) as sma FROM check_7_basis_1_wfm_psl as check_7 
        INNER JOIN check_4_sma_bafa as check_4 ON check_7.sma_verb_nur_zr = check_4.sma
        INNER JOIN check_3_bafa_sma as check_3 ON check_3.requidbafa = check_7.requidbafa
        WHERE check_3.loeschen = 0 AND check_4.loeschen = 1
    ) as base on base.sma = check4.sma
    SET check4.loeschen = 0
    """
    database_manager.deadlock_save_execute(query_check_4_loeschen_rekursion)
    if debug:
        dump_table_data("check_4_sma_bafa", "loeschen_regel_4", database_manager, dump_dir)


if __name__ == "__main__":
    #    sys._mp_off = True
    run_pre()
    run()
    run_post()
