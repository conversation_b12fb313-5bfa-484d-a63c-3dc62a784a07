#!/usr/bin/python
# coding=utf-8

from multiprocessing import Event

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job
from verarbeitung.src.tables.helper.helper import catchall
from typing import Optional
import pandas as pd


SOURCE_TABLE = "dbbgl_2_temp"
TARGET_TABLE = "dbbgl_2"
PROCESSING_TABLE = "processing_dbbgl_2_temp"


class DBBGL_2(Task):

    def __init__(self, job: Optional[Job], stop_event: Event = None, processbar: bool = True):  # type: ignore
        super().__init__(job, stop_event)
        self.t_ladedatum = None
        self.df_result = pd.DataFrame()

        self.query_fields = [
            "sma",
            "sma_status",
            "geometry_reference",
            "place",
            "token",
            "created",
            "type",
            "initial_hash",
            "edited",
            "comment",
            "material",
            "typeId",
            "objectId",
            "erfasst_am",
            "grabenbreite",
            "verlegetiefe",
            "georef_statuscolor",
            "tiefbauverfahren",
            "anzahl_rohrverband",
            "postprocessingpending",
            "geography",
            "geog",
            "length",
            "source",
            "provider_name",
            "status",
        ]

    def load_data(self):
        # self.processing_sma = self.db.get_dataframe_by_query(f"""Select * from {PROCESSING_TABLE}""")
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT 
                    geometryId,
                    {", ".join(self.query_fields)},
                    t_ladedatum
                FROM {SOURCE_TABLE}
            """
        )
        self.df_source_data["sma"] = self.df_source_data["sma"].astype("Int64")

        if not self.df_source_data.empty:
            self.t_ladedatum = self.df_source_data["t_ladedatum"].iloc[0]

            query_fields = [f"target.{base_field} as {base_field}_base" for base_field in self.query_fields]

            self.df_origin_data = self.db.get_dataframe_by_query(
                f"""
                    SELECT 
                        target.geometryId,
                        {", ".join(query_fields)},
                        target.vorhanden_ep2,
                        target.t_ladedatum as t_ladedatum_base
                    FROM {TARGET_TABLE} as target
                    INNER JOIN {PROCESSING_TABLE} as processing on processing.sma = target.sma
                """
            )
            self.df_origin_data["sma_base"] = self.df_origin_data["sma_base"].astype("Int64")

    def worker(self):
        if self.df_source_data.empty:
            return
        # df_not_in_new_data = self.processing_sma[~(self.processing_sma["sma"].isin(self.df_source_data["sma"]))]
        if not self.df_origin_data.empty:
            df_diff = pd.merge(
                left=self.df_source_data,
                right=self.df_origin_data,
                on=["geometryId"],
                how="outer",
                indicator=True,
            )

            # new data
            df_left_only = df_diff[df_diff["_merge"] == "left_only"][["geometryId", "t_ladedatum"] + self.query_fields]
            df_left_only["vorhanden_ep2"] = 1

            # gone data
            df_right_only = df_diff[df_diff["_merge"] == "right_only"][
                ["geometryId", "vorhanden_ep2", "t_ladedatum", "t_ladedatum_base"]
                + [f"{_field}_base" for _field in self.query_fields]
            ].rename(columns={f"{_field}_base": _field for _field in self.query_fields})
            # 'new' gone date
            df_right_only_new = df_right_only[(df_right_only["vorhanden_ep2"] == 1)]
            df_right_only_new["t_ladedatum"] = self.t_ladedatum
            # 'old' gone data
            df_right_only_old = df_right_only[(df_right_only["vorhanden_ep2"] == 0)][
                ["geometryId", "vorhanden_ep2", "t_ladedatum_base"] + [_field for _field in self.query_fields]
            ].rename(columns={"t_ladedatum_base": "t_ladedatum"})
            # re-concat
            df_right_only = pd.concat([df_right_only_old, df_right_only_new])
            del df_right_only_old
            del df_right_only_new

            df_right_only["vorhanden_ep2"] = 0

            # data in both
            query_part = " | ".join(
                [
                    (
                        f"(({field} != {field}_base) & (({field} == {field}) | ({field}_base == {field}_base)))"
                        if not field == f"{field}_base"
                        else f"(({field} != {field}_base) & (({field} == {field}) | ({field}_base == {field}_base)))"
                    )
                    for field in self.query_fields
                ]
            )
            query = f"(_merge == 'both') & (({query_part}) | vorhanden_ep2 == 0) & (t_ladedatum_base < t_ladedatum)"
            df_both = df_diff.query(query)[["geometryId", "t_ladedatum"] + self.query_fields]
            df_both["vorhanden_ep2"] = 1

            self.df_result = pd.concat([df_left_only, df_both, df_right_only])
            self.df_result["t_ladedatum"] = self.df_result["t_ladedatum"].fillna(self.t_ladedatum)

        else:
            self.df_result = self.df_source_data
            self.df_result["vorhanden_ep2"] = 1

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace(TARGET_TABLE)


@catchall
def run(**kwargs):
    dbbgl = DBBGL_2(job=None)
    dbbgl.run()

    # truncate processing table after processing
    db = DatabaseManager()
    db.truncate([PROCESSING_TABLE])


if __name__ == "__main__":
    # sys._mp_off = True
    run(stop_event=Event())
    exit(1)
