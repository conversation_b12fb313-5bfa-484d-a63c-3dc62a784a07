#!/usr/bin/python
# coding=utf-8

from multiprocessing import Event

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job
from verarbeitung.src.tables.helper.helper import catchall
from typing import Optional
import pandas as pd


SOURCE_TABLE = "dbbgl_2"
TARGET_TABLE = "dbbgl_2_1_detailmenge"


class DBBGL_2_1_Detailmenge(Task):

    def __init__(self, job: Optional[Job], stop_event: Event = None, processbar: bool = True):  # type: ignore
        super().__init__(job, stop_event)
        self.t_ladedatum = None
        self.df_result = pd.DataFrame()

        self.query_fields = [
            "sma_status",
            "geometry_reference",
            "created",
            "type",
            "edited",
            "comment",
            "material",
            "objectId",
            "erfasst_am",
            "grabenbreite",
            "verlegetiefe",
            "georef_statuscolor",
            "tiefbauverfahren",
            "anzahl_rohrverband",
            "postprocessingpending",
            "length",
            "source",
            "provider_name",
            "status",
            "vorhanden_ep2",
            "t_ladedatum",
        ]

    def load_data(self):
        # self.processing_sma = self.db.get_dataframe_by_query(f"""Select * from {PROCESSING_TABLE}""")
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT 
                    sma,
                    geometryId,
                    {", ".join(self.query_fields)}
                FROM {SOURCE_TABLE}
            """
        )
        self.df_source_data["sma"] = self.df_source_data["sma"].astype("Int64")

        self.t_ladedatum = self.df_source_data["t_ladedatum"].iloc[0]

        query_fields = [f"{base_field} as {base_field}_base" for base_field in self.query_fields]

        self.df_origin_data = self.db.get_dataframe_by_query(
            f"""
                SELECT 
                    sma, 
                    geometryId,
                    {", ".join(query_fields)}
                FROM {TARGET_TABLE}
            """
        )
        self.df_origin_data["sma"] = self.df_origin_data["sma"].astype("Int64")

    def worker(self):

        # df_not_in_new_data = self.processing_sma[~(self.processing_sma["sma"].isin(self.df_source_data["sma"]))]
        if not self.df_origin_data.empty:
            df_diff = pd.merge(
                left=self.df_source_data,
                right=self.df_origin_data,
                on=["sma", "geometryId"],
                how="outer",
                indicator=True,
            )

            # new data
            df_left_only = df_diff[df_diff["_merge"] == "left_only"][["sma", "geometryId"] + self.query_fields]

            # data in both
            query_part = " | ".join(
                [
                    (
                        f"(({field} != {field}_base) & (({field} == {field}) | ({field}_base == {field}_base)))"
                        if not field == f"{field}_base"
                        else f"(({field} != {field}_base) & (({field} == {field}) | ({field}_base == {field}_base)))"
                    )
                    for field in self.query_fields
                ]
            )
            query = f"(_merge == 'both') & ({query_part})"
            df_both = df_diff.query(query)[["sma", "geometryId"] + self.query_fields]

            self.df_result = pd.concat([df_left_only, df_both])
            self.df_result["t_ladedatum"] = self.df_result["t_ladedatum"].fillna(self.t_ladedatum)
            self.df_result["vorhanden_ep2"] = self.df_result["vorhanden_ep2"].astype("Int64")

        else:
            self.df_result = self.df_source_data

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace(TARGET_TABLE)


@catchall
def run(**kwargs):
    dbbgl = DBBGL_2_1_Detailmenge(job=None)
    dbbgl.run()


if __name__ == "__main__":
    # sys._mp_off = True
    run(stop_event=Event())
    exit(1)
