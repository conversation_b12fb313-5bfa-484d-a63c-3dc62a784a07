#!/usr/bin/python
# coding=utf-8

from multiprocessing import Event
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job
from verarbeitung.src.tables.helper.helper import catchall
from typing import Optional
import pandas as pd


SOURCE_TABLE = "dbbgl_2"
TARGET_TABLE = "dbbgl_2_2_laenge"

REFERENCE = "ref_dbbgl_bw"

SMA_STATUS_ORDER = [
    "NO_DATA",
    "APPROVAL_REQUIRED",
    "APPROVED",
    "COMPLETED",
    "FINALIZED",
]


def get_sma_status_value_dataframe() -> pd.DataFrame:
    return pd.DataFrame(
        [[name, SMA_STATUS_ORDER.index(name)] for name in SMA_STATUS_ORDER], columns=["sma_status", "sma_status_value"]
    )


class DBBGL_2_2_LAENGE(Task):

    def __init__(self, job: Optional[Job], stop_event: Event = None, processbar: bool = True):  # type: ignore
        super().__init__(job, stop_event)
        # self.t_ladedatum = None
        self.df_result = pd.DataFrame()

        self.db.truncate([TARGET_TABLE])

    def load_data(self):
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT * 
                FROM {SOURCE_TABLE}
                WHERE type = 'LineString' and vorhanden_ep2 = 1
            """
        )
        # get newest t_ladedatum
        # self.t_ladedatum = self.df_source_data.sort_values(["t_ladedatum"], ascending=False)["t_ladedatum"].to_list()[0]

        self.df_origin_data = self.db.get_dataframe_by_query(
            f"""
                SELECT *
                FROM {TARGET_TABLE}
            """
        )

        self.df_reference = self.db.get_dataframe_by_query(
            f"""
                Select * from {REFERENCE}
            """
        )
        # pad bauweise int fields to match "bw_0xx" column schema
        self.df_reference["laenge"] = "laenge_" + self.df_reference["bauweise"].astype("str").str.pad(
            3, side="left", fillchar="0"
        )
        self.df_reference = self.df_reference.sort_values("laenge")

        self.sr_laenge = self.df_reference["laenge"].drop_duplicates()

    def worker(self):
        """
         Nachfolgend werden nur Einträge aus "type" [DBBGL_2].[type] gleich "LineString" UND den Wert
          [DBBGL_2].[vorhanden_ep2] gleich 1 betrachtet.
        Folgende Regeln gelten für SMA [dbbgl2_2_laenge].[sma]:*

        1. Für jeden SMA [DBBGL_2].[sma] in der Tabelle [DBBGL_2] gibt es einen Datensatz in der Tabelle
         [dbbgl2_2_laenge]
        Folgende Regeln gelten für sma_status [dbbgl2_2_laenge].[sma_status]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_2_laenge].[sma_status] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[sma_status] mit dem Wert aus [DBBGL_2].[sma_status] gefüllt. Sind zu einem
          SMA [DBBGL_2].[sma] unterschiedliche [DBBGL_2].[sma_status] vorhanden, so wird der mit der niedrigsten
           Rangfolge eingetragen.

        2. Ist für den SMA [DBBGL_2].[sma] bereits ein Eintrag in der Tabelle [dbbgl2_2_laenge].[sma_status] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[sma_status] mit dem Wert aus [DBBGL_2].[sma_status] überschrieben,
          sofern das Datum in [DBBGL_2].[t-ladedatum] aktueller ist als das in [dbbgl2_2_laenge].[t-ladedatum].
           Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche [DBBGL_2].[sma_status] vorhanden, so wird der mit der
           niedrigsten Rangfolge eingetragen.

        Rangfolge für [DBBGL_2].[sma_status]
        1. NO_DATA,
        2. APPROVAL_REQUIRED,
        3. APPROVED,
        4. COMPLETED,
        5. FINALIZED
        Folgende Regeln gelten für "typeId" [dbbgl2_2_laenge].[type]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_2_laenge].[type] vorhanden, so wird
         das Feld [dbbgl2_2_laenge].[type] mit dem Wert aus [DBBGL_2].[type] gefüllt, dabei werden nur Datensätze
         berücksichtig, bei denen mit [DBBGL_2].[vorhanden_ep2] = 1 UND "type" [DBBGL_2].[type] gleich "LineString"
         sind . Sind keine Datensätze mit [DBBGL_2].[vorhanden_ep2] = 1 und "type" [DBBGL_2].[type] gleich "LineString"
         vorhanden, bleibt das Feld leer.
        Folgende Regeln gelten für "Erfasst am" [dbbgl2_2_laenge].[erfasst_am]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_2_laenge].erfasst_am] vorhanden, so
        wird das Feld [dbbgl2_2_laenge].[erfasst_am] mit dem Wert aus [DBBGL_2].[erfasst_am] gefüllt. Sind zu einem SMA
         [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.

        2. Ist für den SMA [DBBGL_2].[sma] bereits ein Eintrag in der Tabelle [dbbgl2_2_laenge].[erfasst_am] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[erfasst_am] mit dem Wert aus [DBBGL_2].[erfasst_am] überschrieben, sofern
         das Datum in [DBBGL_2].[erfasst_am] aktueller ist als das in [dbbgl2_2_laenge].[erfasst_am]. Sind zu einem SMA
         [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.
        Folgende Regeln gelten für "technisches Ladedatum [dbbgl2_2_laenge].[t-ladedatum]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_2_laenge].[t-ladedatum] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[t-ladedatum] mit dem Wert aus [DBBGL_2].[t-ladedatum] gefüllt. Sind zu
          einem SMA [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.

        2. Ist für den SMA [DBBGL_2].[sma] bereits ein Eintrag in der Tabelle [dbbgl2_2_laenge].[t-ladedatum] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[t-ladedatum] mit dem Wert aus [DBBGL_2].[t-ladedatum] überschrieben,
          sofern das Datum in [DBBGL_2].[t-ladedatum] aktueller ist als das in [dbbgl2_2_laenge].[t-ladedatum].
           Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.
        Folgende Regeln gelten für Länge 001 [dbbgl2_2_laenge].[laenge_001] bis Länge 025
         [dbbgl2_2_laenge].[laenge_025]:

        Die Felder Länge 001 [dbbgl2_2_laenge].[laenge_001] bis Länge 025 [dbbgl2_2_laenge].[laenge_025] haben alle die
         gleiche Berechnungsformel, daher wird nur das Feld Länge 001 [dbbgl2_2_laenge].[laenge_001] nachfolgend
          beschrieben.

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_2_laenge].[laenge_001] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[laenge_001] mit der Summe der Werte aus [DBBGL_2].[length] gefüllt,
          bei denen der Wert [DBBGL_2].[tiefbauverfahren] der Referenz [ref_dbbgl_bw].[distance_typ] im Feld
          [ref_dbbgl_bw].[bauweise] den Wert 1 hat. Berücksichtigt werden dabei nur Werte bei denen
           [DBBGL_2].[vorhanden_ep1] = 1 UND "type" [DBBGL_2].[type] gleich "LineString" ist.

        2. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_2_laenge].[laenge_001] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[laenge_001] mit der Summe der Werte aus [DBBGL_2].[length] überschrieben,
          bei denen der Wert [DBBGL_2].[tiefbauverfahren] der Referenz [ref_dbbgl_bw].[distance_typ] im Feld
           [ref_dbbgl_bw].[bauweise] den Wert 1 hat. Berücksichtigt werden dabei nur Werte bei denen
            [DBBGL_2].[vorhanden_ep1] = 1 UND "type" [DBBGL_2].[type] gleich "LineString" ist.

        3. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_2_laenge].[laenge_001] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[laenge_001] mit dem Wert 0 überschrieben, sofern es zu dem SMA keine
         zugehörigen [ref_dbbgl_bw].[bauweise] den Wert 1 gibt, wenn das Feld [DBBGL_2].[tiefbauverfahren] mit der
          der Referenz [ref_dbbgl_bw].[distance_typ] verknüpft wird. Berücksichtigt werden dabei nur Werte bei denen
           [DBBGL_2].[vorhanden_ep1] = 1 UND "type" [DBBGL_2].[type] gleich "LineString" ist.

        4. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_2_laenge].[laenge_001] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[laenge_001] mit dem Wert 0 überschrieben, sofern alle zu dem SMA
          zugehörigen [ref_dbbgl_bw].[bauweise] mit den Wert 1, bei Verknüpfung von Feld [DBBGL_2].[tiefbauverfahren]
          mit der der Referenz [ref_dbbgl_bw].[distance_typ], und alle [DBBGL_2].[vorhanden_ep1] = 0 UND
           "type" [DBBGL_2].[type] gleich "LineString" sind.

        5. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_2_laenge].[laenge_001] vorhanden,
         so wird das Feld [dbbgl2_2_laenge].[laenge_001] mit dem Wert 0 überschrieben, sofern alle zu dem SMA
          zugehörigen [ref_dbbgl_bw].[bauweise] mit den Wert 1, bei Verknüpfung von Feld [DBBGL_2].[tiefbauverfahren]
           mit der der Referenz [ref_dbbgl_bw].[distance_typ], und alle [DBBGL_2].[vorhanden_ep1] = 1 UND "type"
            [DBBGL_2].[type] nicht gleich "LineString" sind.
        """

        # get all sma's
        # self.df_result = pd.concat([self.df_origin_data[["sma"]], self.df_source_data[["sma"]]]).drop_duplicates()

        # get "base" fields
        # self.df_result = pd.merge(
        #    left=self.df_result,
        #    # right=self.df_origin_data[["sma", "sma_status", "type", "erfasst_am", "t_ladedatum"]],
        #    right=self.df_origin_data[["sma", "sma_status", "erfasst_am", "t_ladedatum"]],
        #    how="left",
        #    on=["sma"],
        # )

        # self.df_result = pd.merge(
        #    left=self.df_result,
        #    right=self.df_source_data[["sma", "sma_status", "type", "erfasst_am", "t_ladedatum", "vorhanden_ep2"]],
        #    how="left",
        #    on=["sma"],
        #    suffixes=["_old", "_new"],
        # ).drop_duplicates()

        self.df_result = self.df_source_data[
            ["sma", "sma_status", "type", "erfasst_am", "t_ladedatum", "vorhanden_ep2"]
        ].drop_duplicates()

        # sma_status logik

        # df_sma_status = self.df_result[
        #    ["sma", "sma_status_old", "sma_status_new", "t_ladedatum_old", "t_ladedatum_new"]
        # ]
        df_sma_status = self.df_result[["sma", "sma_status", "t_ladedatum"]]
        df_sma_status_value = get_sma_status_value_dataframe()

        # df_sma_status_new = df_sma_status[
        #    (
        #        (df_sma_status["t_ladedatum_new"].notna())
        #        & (
        #            (df_sma_status["t_ladedatum_new"] > df_sma_status["t_ladedatum_old"])
        #            | (df_sma_status["t_ladedatum_old"].isna())
        #        )
        #    )
        # ].rename(columns={"t_ladedatum_new": "t_ladedatum", "sma_status_new": "sma_status"})
        # df_sma_status_old = df_sma_status[
        #    (
        #        (df_sma_status["t_ladedatum_old"].notna())
        #        & (
        #            (df_sma_status["t_ladedatum_old"] >= df_sma_status["t_ladedatum_new"])
        #            | (df_sma_status["t_ladedatum_new"].isna())
        #        )
        #    )
        # ].rename(columns={"t_ladedatum_old": "t_ladedatum", "sma_status_old": "sma_status"})

        # df_sma_status = pd.concat([df_sma_status_old, df_sma_status_new])
        # del df_sma_status_old
        # del df_sma_status_new

        df_sma_status = pd.merge(left=df_sma_status, right=df_sma_status_value, how="left", on=["sma_status"])

        df_sma_status_newest_date = (
            df_sma_status.sort_values(by=["t_ladedatum"], ascending=False)
            .groupby(["sma"], as_index=False)
            .first()[["sma", "t_ladedatum"]]
            .rename(columns={"t_ladedatum": "newest_t_ladedatum"})
        )
        df_sma_status = pd.merge(left=df_sma_status, right=df_sma_status_newest_date, how="left", on=["sma"])
        del df_sma_status_newest_date

        df_sma_status = (
            df_sma_status[df_sma_status["t_ladedatum"] == df_sma_status["newest_t_ladedatum"]]
            .sort_values(by=["sma_status_value"], ascending=True)
            .groupby(["sma"], as_index=False)
            .first()
        )[["sma", "sma_status", "t_ladedatum"]]

        # self.df_result = pd.merge(
        #    left=self.df_result[
        #        [col for col in self.df_result.columns.to_list() if not col in ["sma_status_new", "sma_status_old"]]
        #    ],
        #    right=df_sma_status,
        #    how="left",
        #    on=["sma"],
        # ).drop_duplicates()
        self.df_result = pd.merge(
            left=self.df_result[
                [col for col in self.df_result.columns.to_list() if not col in ["sma_status", "t_ladedatum"]]
            ],
            right=df_sma_status,
            how="left",
            on=["sma"],
        ).drop_duplicates()

        # df_type rules
        df_type = self.df_result[["sma", "type"]]
        df_type = df_type.groupby(["sma"], as_index=False).first()[["sma", "type"]]
        self.df_result = pd.merge(
            left=self.df_result[[col for col in self.df_result.columns.to_list() if not col == "type"]],
            right=df_type,
            how="left",
            on=["sma"],
        ).drop_duplicates()
        del df_type

        # erfasst_am
        # df_erfasst_am_base = self.df_result[["sma", "erfasst_am_old", "erfasst_am_new"]]
        # df_erfasst_am_new = df_erfasst_am_base[
        #    (
        #        (df_erfasst_am_base["erfasst_am_new"].notna())
        #        & (
        #            (df_erfasst_am_base["erfasst_am_new"] > df_erfasst_am_base["erfasst_am_old"])
        #            | (df_erfasst_am_base["erfasst_am_old"].isna())
        #        )
        #    )
        # ].rename(columns={"erfasst_am_new": "erfasst_am"})
        # df_erfasst_am_old = df_erfasst_am_base[
        #    (
        #        df_erfasst_am_base["erfasst_am_old"].notna()
        #        & (
        #            (df_erfasst_am_base["erfasst_am_old"] >= df_erfasst_am_base["erfasst_am_new"])
        #            | (df_erfasst_am_base["erfasst_am_new"].isna())
        #        )
        #    )
        # ].rename(columns={"erfasst_am_old": "erfasst_am"})

        # df_erfasst_am = (
        #    pd.concat([df_erfasst_am_new, df_erfasst_am_old])
        #    .sort_values(by=["erfasst_am"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )[["sma", "erfasst_am"]]

        # del df_erfasst_am_base
        # del df_erfasst_am_new
        # del df_erfasst_am_old

        # self.df_result = pd.merge(
        #    left=self.df_result[
        #        [col for col in self.df_result.columns.to_list() if not col in ["erfasst_am_new", "erfasst_am_old"]]
        #    ],
        #    right=df_erfasst_am,
        #    how="left",
        #    on=["sma"],
        # ).drop_duplicates()

        # del df_erfasst_am

        # df_old_ladedatum = (
        #    self.df_result[
        #        (self.df_result["t_ladedatum_old"].notna())
        #        & (
        #            (self.df_result["t_ladedatum_old"] >= self.df_result["t_ladedatum_new"])
        #            | (self.df_result["t_ladedatum_new"].isna())
        #        )
        #    ]
        #    .sort_values(["t_ladedatum_old"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )[["sma", "t_ladedatum_old"]].rename(columns={"t_ladedatum_old": "t_ladedatum"})

        # df_new_ladedatum = (
        #    self.df_result[
        #        (self.df_result["t_ladedatum_new"].notna())
        #        & (
        #            (self.df_result["t_ladedatum_new"] > self.df_result["t_ladedatum_old"])
        #            | (self.df_result["t_ladedatum_old"].isna())
        #        )
        #    ]
        #    .sort_values(["t_ladedatum_old"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )[["sma", "t_ladedatum_new"]].rename(columns={"t_ladedatum_new": "t_ladedatum"})

        ## sort once more to really get the newest t_ladedatum
        # df_ladedatum = (
        #    pd.concat([df_old_ladedatum, df_new_ladedatum])
        #    .sort_values(["t_ladedatum"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )
        # del df_old_ladedatum
        # del df_new_ladedatum

        # self.df_result = pd.merge(left=self.df_result, right=df_ladedatum, how="left", on=["sma"])
        # del df_ladedatum

        df_erfasst_am = (
            self.df_result[["sma", "erfasst_am"]]
            .sort_values(by=["erfasst_am"], ascending=False)
            .groupby(["sma"], as_index=False)
            .first()[["sma", "erfasst_am"]]
        )
        self.df_result = pd.merge(
            left=self.df_result[[col for col in self.df_result.columns.to_list() if not col in ["erfasst_am"]]],
            right=df_erfasst_am,
            how="left",
            on=["sma"],
        ).drop_duplicates()
        del df_erfasst_am

        self.df_result = self.df_result[["sma", "sma_status", "type", "erfasst_am", "t_ladedatum"]].drop_duplicates()

        # aggregate data for each distance_typ
        for _laenge in self.sr_laenge.to_list():

            _mapping = self.df_reference[self.df_reference["laenge"] == _laenge]["distance_typ"].to_list()

            # get all fields for current mapping
            query_parts = [f"tiefbauverfahren == '{_field}'" for _field in _mapping]
            query = f"{'|'.join(query_parts)}"

            df_query = self.df_source_data.query(query)[["sma", "tiefbauverfahren", "length"]]

            df_new = df_query[["sma"]].drop_duplicates()

            # split different distany_typ's into different dataframe columns,
            # ignore laenge values for fields with "vorhanden_ep1" == 0
            for _field in _mapping:
                # df_field = df_query[(df_query["vorhanden_ep1"] == 1) & (df_query["distance_typ"] == _field)]
                df_field = df_query[(df_query["tiefbauverfahren"] == _field)]
                df_field[_field] = df_field["length"]
                df_field = df_field[["sma", _field]].groupby(["sma"], as_index=False).sum()

                df_new = pd.merge(
                    left=df_new,
                    right=df_field,
                    on=["sma"],
                    how="left",
                )
                df_new = df_new.fillna(value=0)

            # add up values
            df_new[_laenge] = df_new[_mapping].sum(axis="columns")
            # df_new = df_new[["sma", _laenge]].rename(columns={_laenge: f"{_laenge}_new"})
            df_new = df_new[["sma", _laenge]]

            # df_merge = pd.merge(
            #    left=self.df_result,
            #    right=self.df_origin_data[["sma", _laenge]].rename(columns={_laenge: f"{_laenge}_old"}),
            #    how="left",
            #    on=["sma"],
            # )
            # df_merge = pd.merge(left=df_merge, right=df_new, how="outer", indicator=True)
            # for _col in [f"{_laenge}_old", f"{_laenge}_new"]:
            #    df_merge[_col] = df_merge[_col].fillna(value=0)
            # df_merge = df_merge.drop_duplicates()

            # gone "without a trace"
            # df_gone = df_merge[df_merge["_merge"] == "left_only"]
            # df_gone = df_gone[df_gone[f"{_laenge}_old"] != 0]
            # df_gone = df_gone[["sma"]]
            # df_gone[f"{_laenge}"] = 0

            # df_both = df_merge[df_merge["_merge"] == "both"]
            # del df_merge

            # df_new = df_both[["sma", f"{_laenge}_new"]].rename(columns={f"{_laenge}_new": _laenge})
            # concat results
            # df_result_col = pd.concat([df_gone, df_new])[["sma", _laenge]]

            # merge onto result
            # self.df_result = pd.merge(left=self.df_result, right=df_result_col, how="left", on=["sma"])
            self.df_result = pd.merge(left=self.df_result, right=df_new, how="left", on=["sma"])
            self.df_result[_laenge] = self.df_result[_laenge].fillna(0)

        # build final result dataframe
        self.df_result = self.df_result[
            ["sma", "sma_status", "type", "erfasst_am", "t_ladedatum"] + self.sr_laenge.to_list()
        ]

        # fill all not (yet) used cols with zeroes
        bw_cols = [col for col in self.df_origin_data.columns if col.startswith("laenge_")]

        for col in bw_cols:
            if not col in self.df_result.columns:
                self.df_result[col] = 0
            else:
                self.df_result[col] = self.df_result[col].fillna(value=0)

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace(TARGET_TABLE)


@catchall
def run(**kwargs):
    dbbgl = DBBGL_2_2_LAENGE(job=None)
    dbbgl.run()


if __name__ == "__main__":
    # sys._mp_off = True
    run(stop_event=Event())
    exit(1)
