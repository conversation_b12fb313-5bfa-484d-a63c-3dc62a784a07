#!/usr/bin/python
# coding=utf-8

from multiprocessing import Event
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job
from verarbeitung.src.tables.helper.helper import catchall
from typing import Optional
import pandas as pd


SOURCE_TABLE = "dbbgl_2"
TARGET_TABLE = "dbbgl_2_3_stueck"

REFERENCE = "ref_dbbgl_st"

SMA_STATUS_ORDER = [
    "NO_DATA",
    "APPROVAL_REQUIRED",
    "APPROVED",
    "COMPLETED",
    "FINALIZED",
]


def get_sma_status_value_dataframe() -> pd.DataFrame:
    return pd.DataFrame(
        [[name, SMA_STATUS_ORDER.index(name)] for name in SMA_STATUS_ORDER], columns=["sma_status", "sma_status_value"]
    )


class DBBGL_2_2_LAENGE(Task):

    def __init__(self, job: Optional[Job], stop_event: Event = None, processbar: bool = True):  # type: ignore
        super().__init__(job, stop_event)
        self.df_result = pd.DataFrame()
        self.db.truncate([TARGET_TABLE])

    def load_data(self):
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT * 
                FROM {SOURCE_TABLE}
                WHERE vorhanden_ep2 = 1
            """
        )
        self.df_origin_data = self.db.get_dataframe_by_query(
            f"""
               SELECT *
               FROM {TARGET_TABLE}
           """
        )

        self.df_reference = self.db.get_dataframe_by_query(
            f"""
                Select * from {REFERENCE}
            """
        )
        # pad stueck int fields to match "stueck_0xx" column schema
        self.df_reference["stueck"] = "stueck_" + self.df_reference["stueck"].astype("str").str.pad(
            3, side="left", fillchar="0"
        )
        self.df_reference = self.df_reference.sort_values("stueck")

        self.sr_stueck = self.df_reference["stueck"].drop_duplicates()

    def worker(self):
        """
        Nachfolgend werden nur Einträge mit den Wert [DBBGL_2].[vorhanden_ep2] gleich 1 betrachtet.
        Folgende Regeln gelten für SMA [dbbgl2_3_stueck].[sma]:*

        1. Für jeden SMA [DBBGL_2].[sma] in der Tabelle [DBBGL_2] gibt es einen Datensatz in der Tabelle [dbbgl2_3_stueck]
        Folgende Regeln gelten für sma_status [dbbgl2_3_stueck].[sma_status]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_3_stueck].[sma_status] vorhanden, so wird das Feld [dbbgl2_3_stueck].[sma_status] mit dem Wert aus [DBBGL_2].[sma_status] gefüllt. Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche [DBBGL_2].[sma_status] vorhanden, so wird der mit der niedrigsten Rangfolge eingetragen.

        2. Ist für den SMA [DBBGL_2].[sma] bereits ein Eintrag in der Tabelle [dbbgl2_3_stueck].[sma_status] vorhanden, so wird das Feld [dbbgl2_3_stueck].[sma_status] mit dem Wert aus [DBBGL_2].[sma_status] überschrieben, sofern das Datum in [DBBGL_2].[t-ladedatum] aktueller ist als das in [dbbgl2_3_stueck].[t-ladedatum]. Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche [DBBGL_2].[sma_status] vorhanden, so wird der mit der niedrigsten Rangfolge eingetragen.

        Rangfolge für [DBBGL_2].[sma_status]
        1. NO_DATA,
        2. APPROVAL_REQUIRED,
        3. APPROVED,
        4. COMPLETED,
        5. FINALIZED
        Folgende Regeln gelten für "typeId" [dbbgl2_3_stueck].[type]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_3_stueck].[type] vorhanden, so wird das Feld [dbbgl2_3_stueck].[type] mit dem Wert aus [DBBGL_2].[type] gefüllt, dabei werden nur Datensätze berücksichtig, bei denen mit [DBBGL_2].[vorhanden_ep2] = ist . Sind keine Datensätze mit [DBBGL_2].[vorhanden_ep2] = 1 vorhanden, bleibt das Feld leer. Sind zu dem SMA sowohl [DBBGL_2].[type] = Point als auch LineString vorhanden so werden diese als "Point, LineString" dargestellt
        Folgende Regeln gelten für "Erfasst am" [dbbgl2_3_stueck].[erfasst_am]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_3_stueck].erfasst_am] vorhanden, so wird das Feld [dbbgl2_3_stueck].[erfasst_am] mit dem Wert aus [DBBGL_2].[erfasst_am] gefüllt. Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.

        2. Ist für den SMA [DBBGL_2].[sma] bereits ein Eintrag in der Tabelle [dbbgl2_3_stueck].[erfasst_am] vorhanden, so wird das Feld [dbbgl2_3_stueck].[erfasst_am] mit dem Wert aus [DBBGL_2].[erfasst_am] überschrieben, sofern das Datum in [DBBGL_2].[erfasst_am] aktueller ist als das in [dbbgl2_3_stueck].[erfasst_am]. Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.
        Folgende Regeln gelten für "technisches Ladedatum [dbbgl2_3_stueck].[t-ladedatum]:

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_3_stueck].[t-ladedatum] vorhanden, so wird das Feld [dbbgl2_3_stueck].[t-ladedatum] mit dem Wert aus [DBBGL_2].[t-ladedatum] gefüllt. Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.

        2. Ist für den SMA [DBBGL_2].[sma] bereits ein Eintrag in der Tabelle [dbbgl2_3_stueck].[t-ladedatum] vorhanden, so wird das Feld [dbbgl2_3_stueck].[t-ladedatum] mit dem Wert aus [DBBGL_2].[t-ladedatum] überschrieben, sofern das Datum in [DBBGL_2].[t-ladedatum] aktueller ist als das in [dbbgl2_3_stueck].[t-ladedatum]. Sind zu einem SMA [DBBGL_2].[sma] unterschiedliche Datumswerte vorhanden, so wird der neueste verwendet.
        Folgende Regeln gelten für Stück 001 [dbbgl2_3_stueck].[stueck_001] bis 'Stück 025 [dbbgl2_3_stueck].[stueck_025]:

        Die Felder Stück 001 [dbbgl2_3_stueck].[stueck_001] bis Stück 025 [dbbgl2_3_stueck].[stüeck_025] haben alle die gleiche Berechnungsformel, daher wird nur das Feld Stück 001 [dbbgl2_3_stueck].[stück_001] nachfolgend beschrieben.

        1. Ist für den SMA [DBBGL_2].[sma] noch kein Eintrag in der Tabelle [dbbgl2_3_stueck].[stueck_001] vorhanden, so wird das Feld [dbbgl2_3_stueck].[stueck_001] mit der Anzahl der Datensätze zu diesem SMA gefüllt, bei denen der Wert [DBBGL_2].[objektid] der Referenz [ref_dbbgl_st].[objektid] im Feld [ref_dbbgl_st].[stueck] den Wert 1 hat. Berücksichtigt werden dabei nur Werte bei denen [DBBGL_2].[vorhanden_ep2] = 1 ist.

        2. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_3_stueck].[stueck_001] vorhanden, so wird das Feld [dbbgl2_3_stueck].[stueck_001] mit der Anzahl der Datensätze zu diesem SMA überschrieben, bei denen der Wert [DBBGL_2].[objektid] der Referenz [ref_dbbgl_st].[objektid] im Feld [ref_dbbgl_st].[stueck] den Wert 1 hat. Berücksichtigt werden dabei nur Werte bei denen [DBBGL_2].[vorhanden_ep2] = 1 ist.

        3. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_3_stueck].[stueck_001] vorhanden, so wird das Feld [dbbgl2_3_stueck].[stueck_001] mit dem Wert 0 überschrieben, sofern es zu dem SMA keine zugehörigen Datensatz [ref_dbbgl_st].[stueck] mit den Wert 1 gibt, wenn das Feld [DBBGL_2].[objektid] mit der der Referenz [ref_dbbgl_st].[objektid] verknüpft wird. Berücksichtigt werden dabei nur Werte bei denen [DBBGL_2].[vorhanden_ep1] = 1 ist.

        4. Ist für den SMA [DBBGL_2].[sma] bereits Eintrag in der Tabelle [dbbgl2_3_stueck].[stueck_001] vorhanden, so wird das Feld [dbbgl2_3_stueck].[stueck_001] mit dem Wert 0 überschrieben, sofern alle zu dem SMA zugehörigen [ref_dbbgl_st].[stueck] mit den Wert 1, bei Verknüpfung von Feld [DBBGL_2].[objektid] mit der der Referenz [ref_dbbgl_st].[objektid], und alle [DBBGL_2].[vorhanden_ep1] = 0 sind.

        """

        # get all sma's
        # self.df_result = pd.concat([self.df_origin_data[["sma"]], self.df_source_data[["sma"]]]).drop_duplicates()

        # get "base" fields
        # self.df_result = pd.merge(
        #    left=self.df_result,
        #    right=self.df_origin_data[["sma", "sma_status", "erfasst_am", "t_ladedatum"]],
        #    how="left",
        #    on=["sma"],
        # )

        # self.df_result = pd.merge(
        #    left=self.df_result,
        #    right=self.df_source_data[["sma", "sma_status", "type", "erfasst_am", "t_ladedatum", "vorhanden_ep2"]],
        #    how="left",
        #    on=["sma"],
        #    suffixes=["_old", "_new"],
        # ).drop_duplicates()

        self.df_result = self.df_source_data[
            ["sma", "sma_status", "type", "erfasst_am", "t_ladedatum", "vorhanden_ep2"]
        ]

        # sma_status logik

        # df_sma_status = self.df_result[
        #    ["sma", "sma_status_old", "sma_status_new", "t_ladedatum_old", "t_ladedatum_new"]
        # ]
        df_sma_status = self.df_result[["sma", "sma_status", "t_ladedatum"]]
        df_sma_status_value = get_sma_status_value_dataframe()

        # df_sma_status_new = df_sma_status[
        #    (
        #        (df_sma_status["t_ladedatum_new"].notna())
        #        & (
        #            (df_sma_status["t_ladedatum_new"] > df_sma_status["t_ladedatum_old"])
        #            | (df_sma_status["t_ladedatum_old"].isna())
        #        )
        #    )
        # ].rename(columns={"t_ladedatum_new": "t_ladedatum", "sma_status_new": "sma_status"})
        # df_sma_status_old = df_sma_status[
        #    (
        #        (df_sma_status["t_ladedatum_old"].notna())
        #        & (
        #            (df_sma_status["t_ladedatum_old"] >= df_sma_status["t_ladedatum_new"])
        #            | (df_sma_status["t_ladedatum_new"].isna())
        #        )
        #    )
        # ].rename(columns={"t_ladedatum_old": "t_ladedatum", "sma_status_old": "sma_status"})

        # df_sma_status = pd.concat([df_sma_status_old, df_sma_status_new])
        # del df_sma_status_old
        # del df_sma_status_new

        df_sma_status = pd.merge(left=df_sma_status, right=df_sma_status_value, how="left", on=["sma_status"])
        df_sma_status_newest_date = (
            df_sma_status.sort_values(by=["t_ladedatum"], ascending=False)
            .groupby(["sma"], as_index=False)
            .first()[["sma", "t_ladedatum"]]
            .rename(columns={"t_ladedatum": "newest_t_ladedatum"})
        )
        df_sma_status = pd.merge(left=df_sma_status, right=df_sma_status_newest_date, how="left", on=["sma"])
        del df_sma_status_newest_date

        df_sma_status = (
            df_sma_status[df_sma_status["t_ladedatum"] == df_sma_status["newest_t_ladedatum"]]
            .sort_values(by=["sma_status_value"], ascending=True)
            .groupby(["sma"], as_index=False)
            .first()
            # )[["sma", "sma_status"]]
        )[["sma", "sma_status", "t_ladedatum"]]

        self.df_result = pd.merge(
            left=self.df_result[
                # [col for col in self.df_result.columns.to_list() if not col in ["sma_status_new", "sma_status_old"]]
                [col for col in self.df_result.columns.to_list() if not col in ["sma_status", "t_ladedatum"]]
            ],
            right=df_sma_status,
            how="left",
            on=["sma"],
        ).drop_duplicates()

        # df_type rules
        df_type = self.df_result[["sma", "type"]].drop_duplicates().sort_values(["type"], ascending=True)
        df_type = (
            df_type[(df_type["type"].notna())].groupby(["sma"], as_index=False).agg({"type": ",".join})[["sma", "type"]]
        )

        self.df_result = pd.merge(
            left=self.df_result[[col for col in self.df_result.columns.to_list() if not col == "type"]],
            right=df_type,
            how="left",
            on=["sma"],
        ).drop_duplicates()
        del df_type

        # erfasst_am
        # df_erfasst_am_base = self.df_result[["sma", "erfasst_am_old", "erfasst_am_new"]]
        df_erfasst_am_base = self.df_result[["sma", "erfasst_am"]]
        # df_erfasst_am_new = df_erfasst_am_base[
        #    (
        #        (df_erfasst_am_base["erfasst_am_new"].notna())
        #        & (
        #            (df_erfasst_am_base["erfasst_am_new"] > df_erfasst_am_base["erfasst_am_old"])
        #            | (df_erfasst_am_base["erfasst_am_old"].isna())
        #        )
        #    )
        # ].rename(columns={"erfasst_am_new": "erfasst_am"})
        # df_erfasst_am_old = df_erfasst_am_base[
        #    (
        #        df_erfasst_am_base["erfasst_am_old"].notna()
        #        & (
        #            (df_erfasst_am_base["erfasst_am_old"] >= df_erfasst_am_base["erfasst_am_new"])
        #            | (df_erfasst_am_base["erfasst_am_new"].isna())
        #        )
        #    )
        # ].rename(columns={"erfasst_am_old": "erfasst_am"})

        df_erfasst_am = (
            # pd.concat([df_erfasst_am_new, df_erfasst_am_old])
            df_erfasst_am_base.sort_values(by=["erfasst_am"], ascending=False)
            .groupby(["sma"], as_index=False)
            .first()
        )[["sma", "erfasst_am"]]

        del df_erfasst_am_base
        # del df_erfasst_am_new
        # del df_erfasst_am_old

        self.df_result = pd.merge(
            left=self.df_result[
                # [col for col in self.df_result.columns.to_list() if not col in ["erfasst_am_new", "erfasst_am_old"]]
                [col for col in self.df_result.columns.to_list() if not col in ["erfasst_am"]]
            ],
            right=df_erfasst_am,
            how="left",
            on=["sma"],
        ).drop_duplicates()

        del df_erfasst_am

        # df_old_ladedatum = (
        #    self.df_result[
        #        (self.df_result["t_ladedatum_old"].notna())
        #        & (
        #            (self.df_result["t_ladedatum_old"] >= self.df_result["t_ladedatum_new"])
        #            | (self.df_result["t_ladedatum_new"].isna())
        #        )
        #    ]
        #    .sort_values(["t_ladedatum_old"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )[["sma", "t_ladedatum_old"]].rename(columns={"t_ladedatum_old": "t_ladedatum"})

        # df_new_ladedatum = (
        #    self.df_result[
        #        (self.df_result["t_ladedatum_new"].notna())
        #        & (
        #            (self.df_result["t_ladedatum_new"] > self.df_result["t_ladedatum_old"])
        #            | (self.df_result["t_ladedatum_old"].isna())
        #        )
        #    ]
        #    .sort_values(["t_ladedatum_old"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )[["sma", "t_ladedatum_new"]].rename(columns={"t_ladedatum_new": "t_ladedatum"})

        # sort once more to really get the newest t_ladedatum
        # df_ladedatum = (
        #    pd.concat([df_old_ladedatum, df_new_ladedatum])
        #    .sort_values(["t_ladedatum"], ascending=False)
        #    .groupby(["sma"], as_index=False)
        #    .first()
        # )
        # del df_old_ladedatum
        # del df_new_ladedatum

        # self.df_result = pd.merge(left=self.df_result, right=df_ladedatum, how="left", on=["sma"])
        # del df_ladedatum

        self.df_result = self.df_result[["sma", "sma_status", "type", "erfasst_am", "t_ladedatum"]].drop_duplicates()

        # aggregate data for each distance_typ
        for _stueck in self.sr_stueck.to_list():

            _mapping = self.df_reference[self.df_reference["stueck"] == _stueck]["objectId"].to_list()

            # get all fields for current mapping
            query_parts = [f"objectId == '{_field}'" for _field in _mapping]
            query = f"{'|'.join(query_parts)}"

            df_query = self.df_source_data.query(query)[["sma", "objectId"]]

            df_new = df_query[["sma"]].drop_duplicates()

            # split different objectId's into different dataframe columns,
            # ignore values for fields with "vorhanden_ep1" == 0
            for _field in _mapping:
                df_field = df_query[(df_query["objectId"] == _field)]
                df_field = df_field.groupby(["sma"], as_index=False).count().rename(columns={"objectId": _field})

                df_new = pd.merge(
                    left=df_new,
                    right=df_field,
                    on=["sma"],
                    how="left",
                )
                df_new = df_new.fillna(value=0)

            # add up values
            df_new[_stueck] = df_new[_mapping].sum(axis="columns")
            _df_new = df_new[["sma", _stueck]].rename(columns={_stueck: f"{_stueck}_new"})

            df_merge = pd.merge(left=self.df_result, right=_df_new, how="outer", indicator=True)
            df_merge = df_merge.drop_duplicates()

            # gone "without a trace"
            df_gone = df_merge[df_merge["_merge"] == "left_only"]
            df_gone = df_gone[["sma"]]
            df_gone[f"{_stueck}"] = 0

            del df_merge

            # concat results
            df_result_col = pd.concat([df_gone, df_new])[["sma", _stueck]]

            # merge onto result
            self.df_result = pd.merge(left=self.df_result, right=df_result_col, how="left", on=["sma"])
            self.df_result[_stueck] = self.df_result[_stueck].fillna(0)

        # build final result dataframe
        self.df_result = self.df_result[
            ["sma", "sma_status", "type", "erfasst_am", "t_ladedatum"] + self.sr_stueck.to_list()
        ]

        # fill all not (yet) used cols with zeroes
        bw_cols = [col for col in self.df_origin_data.columns if col.startswith("stueck_")]

        for col in bw_cols:
            if not col in self.df_result.columns:
                self.df_result[col] = 0
            else:
                self.df_result[col] = self.df_result[col].fillna(value=0)

            # aaand we want int's
            self.df_result[col] = self.df_result[col].astype("Int64")

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace(TARGET_TABLE)


@catchall
def run(**kwargs):
    dbbgl = DBBGL_2_2_LAENGE(job=None)
    dbbgl.run()


if __name__ == "__main__":
    # sys._mp_off = True
    run(stop_event=Event())
    exit(1)
