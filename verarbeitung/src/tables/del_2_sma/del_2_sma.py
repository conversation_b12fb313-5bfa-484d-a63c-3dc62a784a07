#!/usr/bin/python
# coding=utf-8
import sys

import pandas as pd
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.helper.helper import catchall
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess


class Del2SMA(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.df_result = pd.DataFrame()

    def load_data(self):
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT
                    check4.sma
                FROM (Select * FROM check_4_sma_bafa LIMIT {self.offset},{self.limit}) AS check4
                LEFT JOIN del_2_sma AS del2 ON check4.sma = del2.sma 
                WHERE check4.loeschen = 1 AND del2.geloescht IS NULL
            """
        )

    def worker(self):
        #
        # 1. Ist die SMA-Nummer [check_4_sma_bafa].[sma] mit dem Eintrag [check_4_sma_bafa].[loeschen] = 1 in der
        # Tabelle [del_2_sma] noch nicht vorhanden, so füge die SMA-Nummer in das Feld [del_2_sma].[sma] als neuen
        # Datensatz an. Das Feld [del_2_sma].[gelöscht] bleibt leer.
        #
        # 2. Ist die RequestID [check_4_sma_bafa].[sma] mit dem Eintrag [check_4_sma_bafa].[loeschen] = 1 in der
        # Tabelle [del_2_sma] bereits vorhanden und ist das Feld [del_2_sma].[gelöscht] leer, so wird die Request ID
        # ignoriert. Das Feld [del_2_sma].[gelöscht] bleibt unverändert leer.
        #
        # 3. Ist die RequestID  [check_4_sma_bafa].[sma] mit dem Eintrag [check_4_sma_bafa].[loeschen] = 1 in der
        # Tabelle [del_2_sma] bereits vorhanden und ist das Feld [del_2_sma].[gelöscht] mit einem Datum gefüllt,
        # so füge die Request ID in das Feld [del_2_sma_error].[sma] als neuen Datensatz an. Das Feld
        # [del_2_sma_error].[ausgewertet] wird mit dem Datum / Uhrzeit an dem die Auswertung lief gefüllt.
        #
        # Für Regel 3 siehe del_2_sma_error
        self.df_result = self.df_source_data

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("del_2_sma")


@catchall
def run(stop_event=None, processbar=True):
    mp = Multiprocess(stop_event=stop_event, processbar=processbar)
    mp.start_multiplexer(table_name="check_4_sma_bafa", processing_class=Del2SMA)
    mp.start_progressbar(table_name="check_4_sma_bafa")


if __name__ == "__main__":
    sys._mp_off = True

    run()
