#!/usr/bin/python
# coding=utf-8
import datetime
import sys

import pandas as pd
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.helper.helper import catchall
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess


class Del2SMAError(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.df_result = pd.DataFrame()

    def load_data(self):
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT
                    check4.sma,
                    check4.loeschen,
                    del2.geloescht,
                    del2Err.ausgewertet
                FROM (Select * FROM check_4_sma_bafa LIMIT {self.offset},{self.limit}) AS check4
                LEFT JOIN del_2_sma AS del2 ON check4.sma = del2.sma
                LEFT JOIN del_2_sma_error as del2Err ON del2.sma = del2Err.sma
            """
        )

    def worker(self):
        #
        # 1. Ist die SMA-Nummer [check_4_sma_bafa].[sma] mit dem Eintrag [check_4_sma_bafa].[loeschen] = 1 in der
        # Tabelle [del_2_sma] noch nicht vorhanden, so füge die SMA-Nummer in das Feld [del_2_sma].[sma] als neuen
        # Datensatz an. Das Feld [del_2_sma].[gelöscht] bleibt leer.
        #
        # 2. Ist die RequestID [check_4_sma_bafa].[sma] mit dem Eintrag [check_4_sma_bafa].[loeschen] = 1 in der
        # Tabelle [del_2_sma] bereits vorhanden und ist das Feld [del_2_sma].[gelöscht] leer, so wird die Request ID
        # ignoriert. Das Feld [del_2_sma].[gelöscht] bleibt unverändert leer.
        #
        # 3. Ist die RequestID  [check_4_sma_bafa].[sma] mit dem Eintrag [check_4_sma_bafa].[loeschen] = 1 in der
        # Tabelle [del_2_sma] bereits vorhanden und ist das Feld [del_2_sma].[gelöscht] mit einem Datum gefüllt,
        # so füge die Request ID in das Feld [del_2_sma_error].[sma] als neuen Datensatz an. Das Feld
        # [del_2_sma_error].[ausgewertet] wird mit dem Datum / Uhrzeit an dem die Auswertung lief gefüllt.
        #
        # Für Regel 1+2 siehe del_2_sma

        df_data = self.df_source_data[
            ((self.df_source_data["loeschen"] == 1) & ~(self.df_source_data["geloescht"].isnull()))
        ]

        df_data = df_data[["sma", "ausgewertet"]]
        df_data["ausgewertet"] = df_data["ausgewertet"].fillna(datetime.datetime.now())

        self.df_result = df_data.reset_index(drop=True)

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("del_2_sma_error")


@catchall
def run(stop_event=None, processbar=True):
    db = DatabaseManager()
    db.truncate(["del_2_sma_error"])

    mp = Multiprocess(stop_event=stop_event, processbar=processbar)
    mp.start_multiplexer(table_name="check_4_sma_bafa", processing_class=Del2SMAError)
    mp.start_progressbar(table_name="check_4_sma_bafa")


if __name__ == "__main__":
    sys._mp_off = True

    run()
