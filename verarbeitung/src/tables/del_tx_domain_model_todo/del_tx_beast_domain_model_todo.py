#!/usr/bin/python
# coding=utf-8
import sys
from typing import List
from datetime import datetime

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import Typo3DatabaseManager
from verarbeitung.src.tables.helper.helper import catchall
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job

MAX_BAFA_LIST_SIZE = 10000

TARGET_TABLE = "tx_beast_domain_model_todo"


class DelTODO(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.frontend_db = Typo3DatabaseManager()
        self.timestamp = datetime.now()

    def load_data(self):
        #########################################################
        # Soll- bzw. Wiedervorlagetermine sind immer mit genau einem Bezugsfeld verknüpft.
        # Somit können die alle Informationen zu einem Soll- bzw. Wiedervorlagetermin gelöscht werden,
        # wenn der im Bezugsfeld enthaltene Wert gelöscht wird.
        #
        # Folgende Regel gilt somit für das Löschen von Soll- und Wiedervorlageterminen mit dem Bezugsfeld
        # SM-Auftrag [tx_beast_domain_model_todo].[sma]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_todo].[sma] in der Tabelle [del_2_sma].[sma]
        # #1469 enthalten so werden die zu diesen SMA zugehörigen Informationen des Soll- bzw. Wiedervorlagetermin
        # gelöscht. Alle Anderen Datensätze werden dadurch nicht verändert.
        #
        # Folgende Regel gilt somit für das Löschen von Soll- und Wiedervorlageterminen mit dem Bezugsfeld
        # Request-ID BA/FA [tx_beast_domain_model_todo].[requidbafa]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_todo].[requidbafa] in der Tabelle
        # [del_1_wfmt].[requidbafa] #1470 enthalten so werden die zu diesen BA/FA Request-ID zugehörigen Informationen
        # des Soll- bzw. Wiedervorlagetermin gelöscht. Alle Anderen Datensätze werden dadurch nicht verändert.
        #
        # Folgende Regel gilt somit für das Löschen von Soll- und Wiedervorlageterminen mit dem Kommentarindex
        # [tx_beast_domain_model_todo].[kommentar_index]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_todo].[kommentar_index] in der Tabelle
        # [del_3_kom_index].[kom_index] #1485 enthalten so werden die zu diesen Kommentar Index zugehörigen
        # Informationen des Soll- bzw. Wiedervorlagetermin gelöscht. Alle Anderen Datensätze werden dadurch
        # nicht verändert.
        #
        # Für alle anderen Bezugsfelder gibt es noch keine Löschroutine, weswegen derzeit auch keine Regeln
        # dafür erstellt werden.

        # remove bafa from comment
        sr_del1_bafas = self.db.get_dataframe_by_query(
            f"""
                SELECT requidbafa FROM del_1_wfmt
            """
        )["requidbafa"]

        self.frontend_db.delete_where_in(TARGET_TABLE, "requidbafa", sr_del1_bafas)

        sr_del2_smas = self.db.get_dataframe_by_query(
            f"""
                SELECT sma FROM del_2_sma
            """
        )["sma"]

        self.frontend_db.delete_where_in(TARGET_TABLE, "sma", sr_del2_smas)

        sr_del3_kommentar_index = self.db.get_dataframe_by_query(
            f"""
                SELECT kom_index FROM del_3_kom_index
            """
        )["kom_index"]

        self.frontend_db.delete_where_in(TARGET_TABLE, "kommentar_index", sr_del3_kommentar_index)

    def worker(self):
        return True

    def write_result(self):
        return True


@catchall
def run(**kwargs):
    loeschen = DelTODO(job=None)
    loeschen.load_data()


if __name__ == "__main__":
    sys._mp_off = True

    run()
