#!/usr/bin/python
# coding=utf-8

import pandas as pd
import sys

from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Multiprocess, Job
from verarbeitung.src.tables.helper.helper import catchall


def truncate_result_table():
    db = DatabaseManager()
    query = """TRUNCATE TABLE kollo_basis;"""
    db.deadlock_save_execute(query)


class KolloBasis(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.df_result = pd.DataFrame()

    def load_data(self):

        self.df_source_data = self.db.get_dataframe_by_query(
            """
                SELECT
                wmsti_nr_angebot,
                wmsti_nr_auftrag,
                kollo_auftragsnr
                FROM kollo_offene_auftraege as kollo           
            """.format(
                offset=self.offset, limit=self.limit
            )
        )
        self.df_source_data["wmsti_nr_angebot"].fillna(inplace=True, value="")
        self.df_source_data["wmsti_nr_auftrag"].fillna(inplace=True, value="")

    def worker(self):
        # Regel 1
        #  Ist weder im Feld [kollo_offene_auftraege].[wmsti_nr_angebot]
        #  noch in [kollo_offene_auftraege].[wmsti_nr_auftrag] eine RequestID eingetragen,
        #  erfolgt auch kein Eintrag in der [kollo_basis]
        self.df_source_data = self.df_source_data[
            ~(self.df_source_data["wmsti_nr_angebot"] == "") | ~(self.df_source_data["wmsti_nr_auftrag"] == "")
        ]

        self.df_source_data["kollo_auftragsart"] = 0

        # Regel 2
        # Ist nur im Feld [kollo_offene_auftraege].[wmsti_nr_angebot] eine Request ID eingetragen und das
        # Feld [kollo_offene_auftraege].[wmsti_nr_auftrag] leer, so wird in der Tabelle [kollo_basis]
        # folgende Daten eingefügt.
        # [kollo_offene_auftraege].[kollo_auftragsnr] wird in die [kollo_basis].[kollo_auftragsnr] übertragen
        # [kollo_offene_auftraege].[wmsti_nr_angebot] wird in die [kollo_basis].[kollo_reqid] übertragen
        # [kollo_basis].[kollo_auftragsart] wird mit dem Wert "1" für Angebot gefüllt
        # && Regel 5 a
        # Enthält sowohl Feld [kollo_offene_auftraege].[wmsti_nr_angebot]
        # als auch das Feld [kollo_offene_auftraege].[wmsti_nr_auftrag] eine
        # RequestId und sind diese unterschiedlich, so wird in der Tabelle [kollo_basis] folgende Daten eingefügt.
        #
        # Es wird ein Datensatz für Angebot erzeugt:
        # [kollo_offene_auftraege].[kollo_auftragsnr] wird in die [kollo_basis].[kollo_auftragsnr] übertragen
        # [kollo_offene_auftraege].[wmsti_nr_angebot] wird in die [kollo_basis].[kollo_reqid] übertragen
        # [kollo_basis].[kollo_auftragsart] wird mit dem Wert "1" für Angebot gefüllt

        df_rule_2 = self.df_source_data[
            (
                (~(self.df_source_data["wmsti_nr_angebot"] == "") & (self.df_source_data["wmsti_nr_auftrag"] == ""))
                | (
                    ~(self.df_source_data["wmsti_nr_angebot"] == self.df_source_data["wmsti_nr_auftrag"])
                    & ~(self.df_source_data["wmsti_nr_angebot"] == "")
                    & ~(self.df_source_data["wmsti_nr_auftrag"] == "")
                )
            )
        ]
        df_rule_2["kollo_auftragsart"] = 1
        df_rule_2 = df_rule_2.rename(columns={"wmsti_nr_angebot": "kollo_reqid"})[
            ["kollo_auftragsnr", "kollo_reqid", "kollo_auftragsart"]
        ]

        # Regel 3
        # Ist nur im Feld [kollo_offene_auftraege].[wmsti_nr_auftrag] eine Request ID eingetragen und
        # das Feld [kollo_offene_auftraege].[wmsti_nr_angebot] leer, so wird in der Tabelle
        # [kollo_basis] folgende Daten eingefügt.
        # [kollo_offene_auftraege].[kollo_auftragsnr] wird in die [kollo_basis].[kollo_auftragsnr] übertragen
        # [kollo_offene_auftraege].[wmsti_nr_auftrag] wird in die [kollo_basis].[kollo_reqid] übertragen
        # [kollo_basis].[kollo_auftragsart] wird mit dem Wert "2" für Realisierung gefüllt
        # && Regel 5 b
        # Enthält sowohl Feld [kollo_offene_auftraege].[wmsti_nr_angebot]
        # als auch das Feld [kollo_offene_auftraege].[wmsti_nr_auftrag] eine
        # RequestId und sind diese unterschiedlich, so wird in der Tabelle [kollo_basis] folgende Daten eingefügt.
        # Und mit der gleichen [kollo_offene_auftraege].[kollo_auftragsnr] ein zweiter Datensatz mit Realisierung.
        # [kollo_offene_auftraege].[kollo_auftragsnr] wird in die [kollo_basis].[kollo_auftragsnr] übertragen
        # [kollo_offene_auftraege].[wmsti_nr_auftrag] wird in die [kollo_basis].[kollo_reqid] übertragen
        # [kollo_basis].[kollo_auftragsart] wird mit dem Wert "2" für Realisierung gefüllt
        df_rule_3 = self.df_source_data[
            (
                ((self.df_source_data["wmsti_nr_angebot"] == "") & ~(self.df_source_data["wmsti_nr_auftrag"] == ""))
                | (
                    ~(self.df_source_data["wmsti_nr_angebot"] == self.df_source_data["wmsti_nr_auftrag"])
                    & ~(self.df_source_data["wmsti_nr_angebot"] == "")
                    & ~(self.df_source_data["wmsti_nr_auftrag"] == "")
                )
            )
        ]
        df_rule_3["kollo_auftragsart"] = 2
        df_rule_3 = df_rule_3.rename(columns={"wmsti_nr_auftrag": "kollo_reqid"})[
            ["kollo_auftragsnr", "kollo_reqid", "kollo_auftragsart"]
        ]

        # Regel 4
        # Enthält das Feld [kollo_offene_auftraege].[wmsti_nr_angebot] und das
        # Feld [kollo_offene_auftraege].[wmsti_nr_auftrag] die gleichen RequestId,
        # so wird in der Tabelle [kollo_basis] folgende Daten eingefügt.
        # [kollo_offene_auftraege].[kollo_auftragsnr] wird in die [kollo_basis].[kollo_auftragsnr] übertragen
        # [kollo_offene_auftraege].[wmsti_nr_angebot] wird in die [kollo_basis].[kollo_reqid] übertragen
        # [kollo_basis].[kollo_auftragsart] wird mit dem Wert "3" für Angebot und Realisierung gefüllt
        df_rule_4 = self.df_source_data[
            (self.df_source_data["wmsti_nr_angebot"] == self.df_source_data["wmsti_nr_auftrag"])
        ]
        df_rule_4["kollo_auftragsart"] = 3
        df_rule_4 = df_rule_4.rename(columns={"wmsti_nr_angebot": "kollo_reqid"})[
            ["kollo_auftragsnr", "kollo_reqid", "kollo_auftragsart"]
        ]

        # concat dataframes
        self.df_result = pd.concat([df_rule_2, df_rule_3, df_rule_4]).reset_index()[
            ["kollo_auftragsnr", "kollo_reqid", "kollo_auftragsart"]
        ]

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("kollo_basis")


@catchall
def run(**kwargs):

    truncate_result_table()

    mp = Multiprocess(**kwargs)
    mp.start_multiplexer(table_name="kollo_offene_auftraege", processing_class=KolloBasis)
    mp.start_progressbar(table_name="kollo_offene_auftraege")


if __name__ == "__main__":
    sys._mp_off = True

    run()
