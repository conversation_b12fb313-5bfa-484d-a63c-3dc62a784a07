#!/usr/bin/python
# coding=utf-8

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job
from verarbeitung.src.tables.helper.helper import catchall


class ProcessingBafaVrtTermin(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)

    def load_data(self):
        self.db.truncate(["processing_bafa_vrt"])
        self.db.execute(
            """
                REPLACE INTO  processing_bafa_vrt (
                SELECT DISTINCT wms6.kask_ba_master FROM processing_bafa as pbafa 
                LEFT JOIN wms_6_master as wms6 
                on pbafa.bafa = wms6.requidbafa
                );
            """
        )
        self.db.execute(
            """
                REPLACE INTO  processing_bafa_vrt (
                SELECT requidbafa 
                FROM wms_1_allgemein 
                WHERE statusbafa_int not in (6,7) and bafa="BA"
                );
            """
        )
        return True

    def worker(self):
        return True

    def write_result(self):
        return True


@catchall
def run(**kwargs):
    processing_bafa_vrt_termin = ProcessingBafaVrtTermin(job=None)
    processing_bafa_vrt_termin.load_data()
