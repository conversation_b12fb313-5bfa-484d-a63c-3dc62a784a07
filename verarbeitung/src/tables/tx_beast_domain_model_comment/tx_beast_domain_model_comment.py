#!/usr/bin/python
# coding=utf-8
import sys
from datetime import datetime
from typing import List

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import Typo3DatabaseManager
from verarbeitung.src.tables.helper.helper import catchall
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job

MAX_BAFA_LIST_SIZE = 100000
TARGET_TABLE = "tx_beast_domain_model_comment"


def split_list(data: List[str], split_at: int = MAX_BAFA_LIST_SIZE) -> list:
    _lists = [data[x * split_at : (x + 1) * split_at] for x in range(int((len(data) + split_at - 1) / split_at))]
    return _lists


class DelFeComment(Task):
    def __init__(self, job: Job, stop_event=None):
        super().__init__(job, stop_event)
        self.frontend_db = Typo3DatabaseManager()
        self.timestamp = datetime.now()

    def load_data(self):
        #
        # Folgende Regel gilt somit für das Löschen von Kommentarfeldern mit dem Bezugsfeld Request-ID BA/FA
        # [tx_beast_domain_model_comment].[requidbafa]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[requidbafa] in der Tabelle
        # [del_1_wfmt].[requidbafa] #1470 enthalten UND das Bezugsfeld [tx_beast_domain_model_comment].[sma] ist leer
        # UND das Bezugsfeld [tx_beast_domain_model_comment].[kommentar_index] ist leer
        # UND das Bezugsfeld [tx_beast_domain_model_comment].[auftragsnr_auftraggeber] ist leer, so wird der zu diesen
        # BA/FA Request-ID zugehörigen Kommentareintrag (kompletter Datensatz) vollständig gelöscht.
        # Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #
        # 2. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[requidbafa] in der Tabelle
        # [del_1_wfmt].[requidbafa] #1470 enthalten UND eines oder mehree Bezugsfelder
        # [tx_beast_domain_model_comment].[sma], [tx_beast_domain_model_comment].[kommentar_index],
        # [tx_beast_domain_model_comment].[auftragsnr_auftraggeber sind nicht leer,
        # so wird der zu diesen BA/FA Request-ID zugehörigen Kommentareintrag nicht gelöscht.
        # Der Eintrag im Feld [tx_beast_domain_model_activeoptionvalue].[requidbafa] wird in diesem Fall auf den
        # Wert NULL gesetzt. Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #

        # remove bafa from comment
        sr_del1_bafas = self.db.get_dataframe_by_query(
            f"""
                SELECT requidbafa FROM del_1_wfmt
            """
        )["requidbafa"]

        # self.frontend_db.update_where_in(TARGET_TABLE, "requidbafa", sr_del1_bafas, "")

        # revert this to old variant to prevent db overload
        for _bafas in split_list(sr_del1_bafas.to_list()):
            self.frontend_db.deadlock_save_execute(
                f"""
                    UPDATE
                        tx_beast_domain_model_comment
                    SET requidbafa = ''
                    WHERE
                        requidbafa IN ({",".join([f"'{bafa}'" for bafa in _bafas])}) 
                """
            )

        #
        # Folgende Regel gilt somit für das Löschen von Optionswerten mit dem Bezugsfeld SM-Auftrag
        # [tx_beast_domain_model_comment].[sma]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[sma] in der Tabelle
        # [del_2_sma].[sma] #1469 enthalten UND das Bezugsfeld [tx_beast_domain_model_comment].[requidbafa] ist leer
        # UND das Bezugsfeld [tx_beast_domain_model_comment].[kommentar_index] ist leer, so wird der zu diesen
        # SMA zugehörigen Kommentareintrag (kompletter Datensatz) vollständig gelöscht.
        # Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #
        # 2. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[sma] in der Tabelle
        # [del_2_sma].[sma] #1469 enthalten UND eines oder beide Bezugsfelder
        # [tx_beast_domain_model_comment].[requidbafa], [tx_beast_domain_model_comment].[kommentar_index]
        # sind nicht leer, so wird der zu diesen SMA zugehörigen Kommentareintrag nicht gelöscht.
        # Der Eintrag im Feld [tx_beast_domain_model_activeoptionvalue].[sma] wird in diesem Fall auf den
        # Wert NULL gesetzt. Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #

        sr_del2_smas = self.db.get_dataframe_by_query(
            f"""
                SELECT sma FROM del_2_sma
            """
        )["sma"]

        self.frontend_db.update_where_in(TARGET_TABLE, "sma", sr_del2_smas, 0)

        #
        # Folgende Regel gilt somit für das Löschen von Optionswerten mit dem Bezugsfeld Request-ID Urprung
        # [tx_beast_domain_model_comment].[tnid]:
        #
        # 1. Das Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[tnid] wird derzeit nicht genutzt.
        # Befindet sich ein Eintrag in diesem Feld wird der Eintrag gelöscht und der Wert auf NULL gesetzt.
        #
        self.frontend_db.deadlock_save_execute(
            f"""
                UPDATE
                    tx_beast_domain_model_comment AS com SET com.tnid = 0
                WHERE 
                    com.tnid > 0
            """
        )

        #
        # Folgende Regel gilt somit für das Löschen von Optionswerten mit dem Bezugsfeld Kommentar-Index
        # [tx_beast_domain_model_comment].[kommentar_index]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[kommentar_index] in der Tabelle
        # [del_3_kom_index].[kom_index] #1485 enthalten UND das Bezugsfeld [tx_beast_domain_model_comment].[requidbafa]
        # ist leer UND das Bezugsfeld [tx_beast_domain_model_comment].[sma] ist leer, so wird der zu diesen
        # Kommentarindex zugehörigen Kommentareintrag (kompletter Datensatz) vollständig gelöscht.
        # Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #
        # 2. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[kommentar_index] in der Tabelle
        # [del_3_kom_index].[kom_index] #1485 enthalten UND eines oder beide Bezugsfelder
        # [tx_beast_domain_model_comment].[requidbafa], [tx_beast_domain_model_comment].[sma] sind nicht leer,
        # so wird der zu diesen Kommentar-Index zugehörigen Kommentareintrag nicht gelöscht. Der Eintrag im Feld
        # [tx_beast_domain_model_activeoptionvalue].[kommentar_index] wird in diesem Fall auf den Wert NULL gesetzt.
        # Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #
        sr_del3_komindex = self.db.get_dataframe_by_query(
            f"""
                 SELECT kom_index FROM del_3_kom_index
            """
        )["kom_index"]

        self.frontend_db.update_where_in(TARGET_TABLE, "kommentar_index", sr_del3_komindex, "")

        self.db.deadlock_save_execute(
            f"""
                UPDATE
                    del_3_kom_index
                SET geloescht = '{self.timestamp}'
                WHERE 
                    geloescht = '0000-00-00 00:00:00' or geloescht is NULL
            """
        )

        #
        # Folgende Regel gilt somit für das Löschen von Kommentarfeldern mit dem Bezugsfeld Auftragsnummer Auftraggeber
        # [tx_beast_domain_model_comment].[auftragsnr_auftraggeber]:
        #
        # 1. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[auftragsnr_auftraggeber] in
        # der Tabelle [del_6_auftragsnrag].[auftragsnrag] #2063 enthalten UND das Bezugsfeld
        # [tx_beast_domain_model_comment].[requidbafa] ist leer UND das Bezugsfeld
        # [tx_beast_domain_model_comment].[sma] ist leer
        # UND das Bezugsfeld [tx_beast_domain_model_comment].[kommentar_index] ist leer,
        # so wird der zu dieser Auftragsnummer Auftraggeber zugehörigen Kommentareintrag
        # (kompletter Datensatz) vollständig gelöscht.
        # Das Kommentarfeld selbst wird durch diese Löschung nicht verändert
        #
        # 2. Ist der Wert im Bezugsfeld [tx_beast_domain_model_activeoptionvalue].[auftragsnr_auftraggeber] in der
        # Tabelle [del_6_auftragsnrag].[auftragsnrag] #2063 enthalten UND eines oder mehrere der Bezugsfelder
        # [tx_beast_domain_model_comment].[requidbafa], [tx_beast_domain_model_comment].[sma],
        # [tx_beast_domain_model_comment].[kommentar_index] sind nicht leer, so wird der zu dieser Auftragsnummer
        # Auftraggeber zugehörigen Kommentareintrag nicht gelöscht.
        # Der Eintrag im Feld [tx_beast_domain_model_activeoptionvalue].[auftragsnr_auftraggeber]
        # wird in diesem Fall auf den Wert NULL gesetzt.
        # Das Kommentarfeld selbst wird durch diese Löschung nicht verändert

        sr_del_6_auftragsnrag = self.db.get_dataframe_by_query("""SELECT auftragsnrag FROM del_6_auftragsnrag""")[
            "auftragsnrag"
        ]

        self.frontend_db.update_where_in(TARGET_TABLE, "auftragsnr_auftraggeber", sr_del_6_auftragsnrag, "")

        #
        # Hinweis: Ist das Feld [tx_beast_domain_model_comment].[requidbafa]
        # UND [tx_beast_domain_model_comment].[sma]
        # UND [tx_beast_domain_model_activeoptionvalue].
        # [kommentar_index] UND [tx_beast_domain_model_comment].
        # [auftragsnr_auftraggeber] UND [tx_beast_domain_model_comment].[area_nr]
        # UND [tx_beast_domain_model_comment].[kls_id]
        # UND [tx_beast_domain_model_comment].[fol_id]
        # UND [tx_beast_domain_model_comment].[nvt_lang]
        # UND [tx_beast_domain_model_comment].[ba_nummer]
        # UND [tx_beast_domain_model_comment].[bulk_order_id]
        # UND [tx_beast_domain_model_comment].[proj_nr] leer wird der gesamte Datensatz gelöscht.
        # Das Feld [tx_beast_domain_model_comment].[tnid] wird nicht berücksichtigt,
        # da es derzeit keine Verwendung findet.
        #
        self.frontend_db.deadlock_save_execute(
            f"""
                DELETE
                FROM
                    tx_beast_domain_model_comment
                WHERE
                    requidbafa = ''
                    AND sma = 0
                    AND kommentar_index = ''
                    AND auftragsnr_auftraggeber = ''
                    AND area_nr = ''
                    AND kls_id = 0
                    AND fol_id = 0
                    AND nvt_lang = ''
                    AND ba_nummer = 0
                    AND bulk_order_id = 0
                    AND proj_nr = 0
            """
        )

    def worker(self):
        return True

    def write_result(self):
        return True


@catchall
def run(stop_event=None, processbar=True):
    loeschen = DelFeComment(job=None, stop_event=None)
    loeschen.load_data()


if __name__ == "__main__":
    sys._mp_off = True

    run()
