#!/usr/bin/python
# coding=utf-8

from multiprocessing import Event

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess
from typing import Optional
from verarbeitung.src.tables.helper.helper import catchall
import pandas as pd


class WFMGGS1Area(Task):
    def __init__(self, job: Optional[Job] = None, stop_event: Optional[Event] = None, processbar: bool = True):
        super().__init__(job, stop_event=stop_event)

    def load_data(self) -> None:
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                Select 
                    wfm_11.requidbafa,
                    area_nr_mont_a,
                    area_name_mont_a,
                    area_nr_klsid_a,
                    area_name_klsid_a
                FROM (Select requidbafa, kls_id_a, kls_id_mont_a  FROM wfm_11_montageort LIMIT {self.offset}, {self.limit}) as wfm_11
                LEFT JOIN processing_wfm_ggs_1_area_id_a as area_kls ON area_kls.kls_id_a = wfm_11.kls_id_a
                LEFT JOIN processing_wfm_ggs_1_area_id_mont_a as area_mont ON area_mont.kls_id_mont_a = wfm_11.kls_id_mont_a
            
            """
        )
        self.df_source_data["area_name_mont_a"] = self.df_source_data["area_name_mont_a"].fillna(
            value="KLS-ID Mont A leer"
        )
        self.df_source_data["area_name_klsid_a"] = self.df_source_data["area_name_klsid_a"].fillna(
            value="KLS-ID A leer"
        )

    def worker(self) -> None:
        pass

    def write_result(self) -> None:
        if not self.df_source_data.empty:
            self.df_source_data.to_sql_replace("wfm_ggs_1_area")


@catchall
def run(**kwargs) -> None:

    mp = Multiprocess(**kwargs)
    mp.start_multiplexer(table_name="wfm_11_montageort", processing_class=WFMGGS1Area)
    mp.start_progressbar(table_name="wfm_11_montageort")


@catchall
def fill_processing_table(**kwargs) -> None:

    db = DatabaseManager()
    db.truncate(["processing_wfm_ggs_1_area_id_a", "processing_wfm_ggs_1_area_id_mont_a"])

    df_id_a = db.get_dataframe_by_query(
        """
        Select distinct(kls_id_a) FROM wfm_11_montageort
    """
    )
    df_id_a["area_nr_klsid_a"] = None
    df_id_a["area_name_klsid_a"] = None
    df_id_a.to_sql_replace("processing_wfm_ggs_1_area_id_a")
    del df_id_a

    df_id_mont_a = db.get_dataframe_by_query(
        """
        Select distinct(kls_id_mont_a) FROM wfm_11_montageort
    """
    )
    df_id_mont_a["area_nr_mont_a"] = None
    df_id_mont_a["area_name_mont_a"] = None
    df_id_mont_a.to_sql_replace("processing_wfm_ggs_1_area_id_mont_a")
    del df_id_mont_a


class WFMGGS1AreaKLSIDA(Task):
    def __init__(self, job: Optional[Job] = None, stop_event: Optional[Event] = None, processbar: bool = True):
        super().__init__(job, stop_event=stop_event)

    def load_data(self) -> None:

        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
               Select
                
                base.kls_id_a as base_kls_id_a,
                area_kls.kls_id as kls_id_a,
                area_kls.area_nr,
                area_kls.area_name
               FROM (Select kls_id_a from processing_wfm_ggs_1_area_id_a LIMIT {self.offset}, {self.limit}) as base
               LEFT JOIN gbgs_basis_1_area as area_kls on area_kls.kls_id = base.kls_id_a
               """
        )

    def worker(self) -> None:

        # get non-unique area_nr_mont_a <-> area_nr joins

        df_num_unique_area_nr = (
            self.df_source_data[["kls_id_a", "area_nr"]]
            .drop_duplicates()[
                (self.df_source_data["kls_id_a"].notna())
                & (self.df_source_data["kls_id_a"] != 0)
                & (self.df_source_data["area_nr"].notna())
                & (self.df_source_data["area_nr"] != 0)
            ]
            .groupby(["kls_id_a"])
            .count()
            .reset_index()[["kls_id_a", "area_nr"]]
            .rename(columns={"area_nr": "count_area_nr"})
        )

        self.df_source_data = pd.merge(
            left=self.df_source_data, right=df_num_unique_area_nr, on=["kls_id_a"], how="left"
        )
        del df_num_unique_area_nr

        self.df_source_data["area_nr_klsid_a"] = None
        self.df_source_data["area_name_klsid_a"] = None

        # 1. Ist für die KLS-ID Montageort A [wfm_11_montageort].[kls_id_mont_a] in der Tabelle
        # [gbgs_basis_1_area_neu] im Feld [gbgs_basis_1_area_neu].[area_nr] eine Eindeutige Nummer vorhanden,
        # so wird das Feld [wfm_ggs_1_area].[area_nr_klsid_a] mit dem eindeutigen Wert aus
        # [gbgs_basis_1_area_neu].[area_nr_klsid_a] und das Feld [wfm_ggs_1_area].[area_name_klsid_ a] mit
        # dem eindeutigen Wert aus [gbgs_basis_1_area_neu].[area_name] gefüllt.
        df_unique = self.df_source_data[
            (self.df_source_data["count_area_nr"]).notna()
            & (self.df_source_data["count_area_nr"] == 1)
            & (self.df_source_data["area_nr"].notna())
            & (self.df_source_data["area_nr"] != 0)
        ]

        df_unique["area_nr_klsid_a"] = df_unique["area_nr"]
        df_unique["area_name_klsid_a"] = df_unique["area_name"]

        df_unique = df_unique[["base_kls_id_a", "area_nr_klsid_a", "area_name_klsid_a"]]

        # 2. Ist die KLS-ID Montageort A [wfm_11_montageort].[kls_id_mont_a] in der Tabelle [gbgs_basis_1_area_neu]
        # im Feld [gbgs_basis_1_area_neu].[area_nr] nicht enthalten, so wird das Feld
        # [wfm_ggs_1_area].[area_nr_klsid_a] mit dem Wert NULL und das Feld [wfm_ggs_1_area].[area_name_klsid_ a] mit
        # dem Wert "Keine Gebiets ID vorhanden" gefüllt.

        df_no_area_nr = self.df_source_data[
            ((self.df_source_data["area_nr"].isna()) | (self.df_source_data["area_nr"] == ""))
            & ((self.df_source_data["base_kls_id_a"].notna()) & (self.df_source_data["base_kls_id_a"] != 0))
        ]
        df_no_area_nr["area_nr_klsid_a"] = None
        df_no_area_nr["area_name_klsid_a"] = "Keine Gebiets ID vorhanden"
        df_no_area_nr = df_no_area_nr[["base_kls_id_a", "area_nr_klsid_a", "area_name_klsid_a"]]

        # 3. Ist für die KLS-ID Montageort A [wfm_11_montageort].[kls_id_klsid_a] in der Tabelle
        # [gbgs_basis_1_area_neu] im Feld [gbgs_basis_1_area_neu].[area_nr] keine Eindeutige Nummer vorhanden,
        # so wird das Feld [wfm_ggs_1_area].[area_nr_klsid_a] mit dem Wert NULL und das Feld
        # [wfm_ggs_1_area].[area_name_klsid_ a] mit dem Wert "Gebiets ID nicht eindeutig" gefüllt.

        df_non_unique = self.df_source_data[
            (self.df_source_data["count_area_nr"].notna()) & (self.df_source_data["count_area_nr"] > 1)
        ]
        df_non_unique["area_nr_klsid_a"] = None
        df_non_unique["area_name_klsid_a"] = "Gebiets ID nicht eindeutig"
        df_non_unique = df_non_unique[["base_kls_id_a", "area_nr_klsid_a", "area_name_klsid_a"]]

        # 4. Ist die KLS-ID Montageort A [wfm_11_montageort].[kls_id_a] gleich NULL bzw. 0, so wird das
        # Feld [wfm_ggs_1_area].[area_nr_klsid_a] mit dem Wert NULL und das Feld
        # [wfm_ggs_1_area].[area_name_klsid_ a] mit dem Wert "KLS-ID A" gefüllt.

        df_kls_id_a_null = self.df_source_data[
            (self.df_source_data["base_kls_id_a"].isna()) | (self.df_source_data["base_kls_id_a"] == 0)
        ]
        df_kls_id_a_null["area_name_klsid_a"] = "KLS-ID A leer"
        df_kls_id_a_null = df_kls_id_a_null[["base_kls_id_a", "area_nr_klsid_a", "area_name_klsid_a"]]

        # merge results

        df_result = pd.concat([df_unique, df_no_area_nr, df_non_unique, df_kls_id_a_null])

        # betters save than sorry
        self.df_result = df_result[["base_kls_id_a", "area_nr_klsid_a", "area_name_klsid_a"]].rename(
            columns={"base_kls_id_a": "kls_id_a"}
        )

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("processing_wfm_ggs_1_area_id_a")


@catchall
def run_a(**kwargs) -> None:

    mp = Multiprocess(**kwargs)
    mp.start_multiplexer(table_name="processing_wfm_ggs_1_area_id_a", processing_class=WFMGGS1AreaKLSIDA)
    mp.start_progressbar(table_name="processing_wfm_ggs_1_area_id_a")


class WFMGGS1AreaKLSIDMontA(Task):
    def __init__(self, job: Optional[Job] = None, stop_event: Optional[Event] = None, processbar: bool = True):
        super().__init__(job, stop_event=stop_event)

    def load_data(self) -> None:

        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
               Select
                
                base.kls_id_mont_a as base_kls_id_mont_a,
                area_kls.kls_id as kls_id_mont_a,
                area_kls.area_nr as area_nr_mont,
                area_kls.area_name as area_name_mont
               FROM (Select kls_id_mont_a from processing_wfm_ggs_1_area_id_mont_a LIMIT {self.offset}, {self.limit}) as base
               LEFT JOIN gbgs_basis_1_area as area_kls on area_kls.kls_id = base.kls_id_mont_a
               """
        )

    def worker(self) -> None:

        # get non-unique area_nr_mont_a <-> area_nr joins
        df_num_unique_area_nr_mont = (
            self.df_source_data[["kls_id_mont_a", "area_nr_mont"]]
            .drop_duplicates()[
                (self.df_source_data["kls_id_mont_a"].notna())
                & (self.df_source_data["kls_id_mont_a"] != 0)
                & (self.df_source_data["area_nr_mont"].notna())
                & (self.df_source_data["area_nr_mont"] != 0)
            ]
            .groupby(["kls_id_mont_a"])
            .count()
            .reset_index()[["kls_id_mont_a", "area_nr_mont"]]
            .rename(columns={"area_nr_mont": "count_area_nr_mont"})
        )

        self.df_source_data = pd.merge(
            left=self.df_source_data, right=df_num_unique_area_nr_mont, on=["kls_id_mont_a"], how="left"
        )
        del df_num_unique_area_nr_mont

        self.df_source_data["area_nr_mont_a"] = None
        self.df_source_data["area_name_mont_a"] = None

        # 1. Ist für die KLS-ID Montageort A [wfm_11_montageort].[kls_id_mont_a] in der Tabelle
        # [gbgs_basis_1_area_neu] im Feld [gbgs_basis_1_area_neu].[area_nr] eine Eindeutige Nummer vorhanden,
        # so wird das Feld [wfm_ggs_1_area].[area_nr_mont_a] mit dem eindeutigen Wert aus
        # [gbgs_basis_1_area_neu].[area_nr] und das Feld [wfm_ggs_1_area].[area_name_mont_a] mit
        # dem eindeutigen Wert aus [gbgs_basis_1_area_neu].[area_name] gefüllt.

        df_unique_mont = self.df_source_data[
            (self.df_source_data["count_area_nr_mont"]).notna()
            & (self.df_source_data["count_area_nr_mont"] == 1)
            & (self.df_source_data["area_nr_mont"].notna())
            & (self.df_source_data["area_nr_mont"] != 0)
        ]
        df_unique_mont["area_nr_mont_a"] = df_unique_mont["area_nr_mont"]
        df_unique_mont["area_name_mont_a"] = df_unique_mont["area_name_mont"]

        df_unique_mont = df_unique_mont[["base_kls_id_mont_a", "area_nr_mont_a", "area_name_mont_a"]]

        # 2. Ist die KLS-ID Montageort A [wfm_11_montageort].[kls_id_mont_a] in der Tabelle [gbgs_basis_1_area_neu]
        # im Feld [gbgs_basis_1_area_neu].[area_nr] nicht enthalten, so wird das Feld [wfm_ggs_1_area].[area_nr_mont_a]
        # mit dem Wert NULL und das Feld [wfm_ggs_1_area].[area_name_mont_a] mit dem Wert
        # "Keine Gebiets ID vorhanden" gefüllt.

        df_no_area_nr_mont = self.df_source_data[
            ((self.df_source_data["area_nr_mont"].isna()) | (self.df_source_data["area_nr_mont"] == ""))
            & ((self.df_source_data["base_kls_id_mont_a"].notna()) & (self.df_source_data["base_kls_id_mont_a"] != 0))
        ]
        df_no_area_nr_mont["area_nr_mont_a"] = None
        df_no_area_nr_mont["area_name_mont_a"] = "Keine Gebiets ID vorhanden"
        df_no_area_nr_mont = df_no_area_nr_mont[["base_kls_id_mont_a", "area_nr_mont_a", "area_name_mont_a"]]

        # 3. Ist für die KLS-ID Montageort A [wfm_11_montageort].[kls_id_mont_a] in der Tabelle
        # [gbgs_basis_1_area_neu] im Feld [gbgs_basis_1_area_neu].[area_nr] keine Eindeutige Nummer vorhanden,
        # so wird das Feld [wfm_ggs_1_area].[area_nr_mont_a] mit dem Wert NULL und das Feld
        # [wfm_ggs_1_area].[area_name_mont_a] mit dem Wert "Gebiets ID nicht eindeutig" gefüllt.

        df_non_unique_mont = self.df_source_data[
            (self.df_source_data["count_area_nr_mont"].notna()) & (self.df_source_data["count_area_nr_mont"] > 1)
        ]
        df_non_unique_mont["area_nr_mont_a"] = None
        df_non_unique_mont["area_name_mont_a"] = "Gebiets ID nicht eindeutig"
        df_non_unique_mont = df_non_unique_mont[["base_kls_id_mont_a", "area_nr_mont_a", "area_name_mont_a"]]

        # 4. Ist die KLS-ID Montageort A [wfm_11_montageort].[kls_id_mont_a] gleich NULL bzw. 0, so wird das Feld
        # [wfm_ggs_1_area].[area_nr_mont_a] mit dem Wert NULL und das Feld [wfm_ggs_1_area].[area_name_mont_a]
        # mit dem Wert "KLS-ID Mont A leer" gefüllt.
        df_kls_id_mont_a_null = self.df_source_data[
            (self.df_source_data["base_kls_id_mont_a"].isna()) | (self.df_source_data["base_kls_id_mont_a"] == 0)
        ]
        df_kls_id_mont_a_null["area_name_mont_a"] = "KLS-ID Mont A leer"
        df_kls_id_mont_a_null = df_kls_id_mont_a_null[["base_kls_id_mont_a", "area_nr_mont_a", "area_name_mont_a"]]

        # merge results

        df_result_mont = pd.concat([df_unique_mont, df_no_area_nr_mont, df_non_unique_mont, df_kls_id_mont_a_null])

        # betters save than sorry
        self.df_result = df_result_mont[["base_kls_id_mont_a", "area_nr_mont_a", "area_name_mont_a"]].rename(
            columns={"base_kls_id_mont_a": "kls_id_mont_a"}
        )

    def write_result(self):
        if not self.df_result.empty:
            self.df_result.to_sql_replace("processing_wfm_ggs_1_area_id_mont_a")


@catchall
def run_mont_a(**kwargs) -> None:

    mp = Multiprocess(**kwargs)
    mp.start_multiplexer(table_name="processing_wfm_ggs_1_area_id_mont_a", processing_class=WFMGGS1AreaKLSIDMontA)
    mp.start_progressbar(table_name="processing_wfm_ggs_1_area_id_mont_a")


class WFMGGS1AreaKaskade(Task):
    def __init__(self, job: Optional[Job] = None, stop_event: Optional[Event] = None, processbar: bool = True):
        super().__init__(job, stop_event=stop_event)

    def load_data(self) -> None:

        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
            SELECT
            area.requidbafa,
            area.area_nr_klsid_a,
            area.area_name_klsid_a,
            area.area_nr_mont_a,
            area.area_name_mont_a,
            ref.gf_struktur,
            master.kask_ba_master,
            area_master.area_nr_klsid_a as master_area_nr_klsid_a,
            area_master.area_name_klsid_a as master_area_name_klsid_a,
            area_master.area_nr_mont_a as master_area_nr_mont_a,
            area_master.area_name_mont_a as master_area_name_mont_a
            FROM (
                Select requidbafa FROM processing_wfm_ggs_1_area_kaskade LIMIT {self.offset}, {self.limit}
            ) as base
            LEFT JOIN wms_6_master as master on base.requidbafa = master.kask_ba_master
            LEFT JOIN wfm_ggs_1_area as area on master.requidbafa = area.requidbafa
            LEFT JOIN wfm_ggs_1_area as area_master on area_master.requidbafa = master.kask_ba_master
            LEFT JOIN wmsti_psl_001 as psl_001 on master.requidbafa = psl_001.wmsti_requidbafa
            LEFT JOIN ref_strukturplan as ref on psl_001.wmsti_prozessausloeser = ref.gf_struktur
            """
        )

        self.df_source_data = self.df_source_data.dropna(subset=["requidbafa"])

    def worker(self) -> None:
        # Folgende Regel gilt für die Felder [wfm_ggs_1_area].[area_nr_strukt], [wfm_ggs_1_area].[area_name_strukt]:
        #
        # 1. Ist im Feld [wmsti_psl_001].[wmsti_prozessausloeser] ein Eintrag aus der Referenz Strukturplanung
        # [ref_strukturplan].[gf_stuktur] vorhanden und die zur Request ID [wfm_ggs_1_area].[requidbafa] gehörendendie
        # Area Nr. KLS ID A [wfm_ggs_1_area].[area_nr_klsid_a] nicht gleich NULL bzw. 0 so wird das Feld
        # Area Nr. Struktur [wfm_ggs_1_area].[area_nr_strukt] mit dem Wert aus Area Nr. KLS ID A
        # [wfm_ggs_1_area].[area_nr_klsid_a] und das Feld Area Name Strukt [wfm_ggs_1_area].[area_name_strukt]
        # mit dem Wert aus Area Name KLS ID [wfm_ggs_1_area].[area_name_klsid_ a] gefüllt.
        #
        # 2. Trift Regel 1 nicht zu, so wird das Feld Area Nr. Struktur [wfm_ggs_1_area].[area_nr_strukt] mit
        # dem Wert NULL und das Feld Area Name Strukt [wfm_ggs_1_area].[area_name_strukt] mit dem Wert NULL gefüllt.

        df_struktur = self.df_source_data[
            (
                (self.df_source_data["gf_struktur"].notna())
                & (self.df_source_data["gf_struktur"] != 0)
                & (self.df_source_data["gf_struktur"] != "")
            )
            & (
                (self.df_source_data["area_nr_klsid_a"].notna())
                & (self.df_source_data["area_nr_klsid_a"] != 0)
                & (self.df_source_data["area_nr_klsid_a"] != "")
            )
        ]
        df_struktur["area_nr_strukt"] = df_struktur["area_nr_klsid_a"]
        df_struktur["area_name_strukt"] = df_struktur["area_name_klsid_a"]

        df_struktur = df_struktur[["requidbafa", "area_nr_strukt", "area_name_strukt"]]

        self.df_result = pd.merge(left=self.df_source_data, right=df_struktur, how="left", on=["requidbafa"])
        del df_struktur

        # Folgende Regel gilt für die Felder [wfm_ggs_1_area].[area_nr_kask], [wfm_ggs_1_area].[area_nr_kask]:
        #
        # 1. Ist für die Area Nr. KLS ID A [wfm_ggs_1_area].[area_nr_klsid_a] des zur Request ID
        # [wfm_ggs_1_area].[requidbafa] gehörenden Kaskaden Masters [wms_6_master].[kask_ba_master] nicht gleich NULL
        # bzw. 0 so wird das Feld Area Nr. Kaskade [wfm_ggs_1_area].[area_nr_kask] mit dem Wert aus Area Nr. KLS ID A
        # [wfm_ggs_1_area].[area_nr_klsid_a] und das Feld Area Name Kaskade [wfm_ggs_1_area].[area_name_kask] mit dem
        # Wert aus Area Name KLS ID [wfm_ggs_1_area].[area_name_klsid_ a] gefüllt.
        #
        # 2. Ist für die Area Nr. KLS ID A [wfm_ggs_1_area].[area_nr_klsid_a] des zur Request ID
        # [wfm_ggs_1_area].[requidbafa] gehörenden Kaskaden Masters [wms_6_master].[kask_ba_master] gleich NULL bzw. 0
        # UND das Feld [wfm_ggs_1_area].[area_nr_mont_a] ist nicht gleich NULL bzw. 0 so wird das Feld Area Nr. Kaskade
        # [wfm_ggs_1_area].[area_nr_kask] mit dem Wert aus Area Nr. Mont A [wfm_ggs_1_area].[area_nr_mont_a] und das
        # Feld Area Name Kaskade [wfm_ggs_1_area].[area_name_kask] mit dem Wert aus Area Name Mont A
        # [wfm_ggs_1_area].[area_name_mont_a] gefüllt.
        #
        # 3. Ist für die Area Nr. KLS ID A [wfm_ggs_1_area].[area_nr_klsid_a] des zur Request ID
        # [wfm_ggs_1_area].[requidbafa] gehörenden Kaskaden Masters [wms_6_master].[kask_ba_master] gleich NULL bzw. 0
        # UND das Feld [wfm_ggs_1_area].[area_nr_mont_a] ist gleich NULL bzw. 0 so werden alle zum Kaskaden Master
        # [wms_6_master].[kask_ba_master] gehörenden Aufträge geprüft, ob ein Eintrag in der Spalte
        # [wfm_ggs_1_area].[area_nr_strukt] ungleich Null bzw. 0 vorhanden ist. Ist dies bei einer oder mehreren
        # Aufträgen der Fall, so wird der Eintrag von der Höchsten Request ID übertragen. Das Feld Area Nr. Kaskade
        # [wfm_ggs_1_area].[area_nr_kask] wird mit dem Wert aus Area Nr. Strukturplanung
        # [wfm_ggs_1_area].[area_nr_stukt] und das Feld Area Name Kaskade [wfm_ggs_1_area].[area_name_kask] mit dem
        # Wert aus Area Name Strukturplanung [wfm_ggs_1_area].[area_name_srukt] gefüllt.
        #
        # 4. Trifft Regel 1-4 nicht, zu so wird das Feld Area Nr. Kaskade [wfm_ggs_1_area].[area_nr_kask] mit dem Wert
        # NULL und das Feld Area Name Kaskade [wfm_ggs_1_area].[area_name_kask] mit dem Wert
        # "Keine Gebiets ID vorhanden" gefüllt.

        df_kaskade_master = self.df_result[
            (
                (self.df_result["kask_ba_master"].notna())
                & (self.df_result["kask_ba_master"] != 0)
                & (self.df_result["kask_ba_master"] != "")
            )
            & (
                (self.df_result["master_area_nr_klsid_a"].notna())
                & (self.df_result["master_area_nr_klsid_a"] != 0)
                & (self.df_result["master_area_nr_klsid_a"] != "")
            )
        ]
        df_kaskade_master["area_nr_kask"] = df_kaskade_master["master_area_nr_klsid_a"]
        df_kaskade_master["area_name_kask"] = df_kaskade_master["master_area_name_klsid_a"]

        df_kaskade_master_area_mont = self.df_result[
            (
                (self.df_result["kask_ba_master"].notna())
                & (self.df_result["kask_ba_master"] != 0)
                & (self.df_result["kask_ba_master"] != "")
            )
            & (
                (self.df_result["master_area_nr_klsid_a"].isna())
                | (self.df_result["master_area_nr_klsid_a"] == 0)
                | (self.df_result["master_area_nr_klsid_a"] == "")
            )
            & (
                (self.df_result["master_area_nr_mont_a"].notna())
                & (self.df_result["master_area_nr_mont_a"] != 0)
                & (self.df_result["master_area_nr_mont_a"] != "")
            )
        ]
        df_kaskade_master_area_mont["area_nr_kask"] = df_kaskade_master_area_mont["master_area_nr_mont_a"]
        df_kaskade_master_area_mont["area_name_kask"] = df_kaskade_master_area_mont["master_area_name_mont_a"]

        df_kaskade_master_area_nr_strukt = self.df_result[
            (
                (self.df_result["kask_ba_master"].notna())
                & (self.df_result["kask_ba_master"] != 0)
                & (self.df_result["kask_ba_master"] != "")
            )
            & (
                (self.df_result["master_area_nr_klsid_a"].isna())
                | (self.df_result["master_area_nr_klsid_a"] == 0)
                | (self.df_result["master_area_nr_klsid_a"] == "")
            )
            & (
                (self.df_result["master_area_nr_mont_a"].isna())
                | (self.df_result["master_area_nr_mont_a"] == 0)
                | (self.df_result["master_area_nr_mont_a"] == "")
            )
            & (
                (self.df_result["area_nr_strukt"].notna())
                & (self.df_result["area_nr_strukt"] != 0)
                & (self.df_result["area_nr_strukt"] != "")
            )
        ]
        df_group = (
            df_kaskade_master_area_nr_strukt.sort_values(
                "requidbafa", key=lambda value: value.str[1:].astype("int"), ascending=False
            )
            .groupby(["kask_ba_master"])
            .first()
            .reset_index()
        )
        df_group = df_group[["kask_ba_master", "area_nr_strukt", "area_name_strukt"]]
        df_group["area_nr_kask"] = df_group["area_nr_strukt"]
        df_group["area_name_kask"] = df_group["area_name_strukt"]
        df_group = df_group[["kask_ba_master", "area_nr_kask", "area_name_kask"]]
        df_kaskade_master_area_nr_strukt = pd.merge(
            left=self.df_result[["requidbafa", "kask_ba_master"]],
            right=df_group,
            on=["kask_ba_master"],
            how="left",
        )[["requidbafa", "area_nr_kask", "area_name_kask"]]
        df_kaskade_master_area_nr_strukt = df_kaskade_master_area_nr_strukt[
            (df_kaskade_master_area_nr_strukt["area_nr_kask"] != "")
            & (df_kaskade_master_area_nr_strukt["area_nr_kask"].notna())
        ]

        df_kaskade_result = pd.concat(
            [df_kaskade_master, df_kaskade_master_area_mont, df_kaskade_master_area_nr_strukt]
        )[["requidbafa", "area_nr_kask", "area_name_kask"]]

        self.df_result = pd.merge(left=self.df_result, right=df_kaskade_result, on=["requidbafa"], how="left")
        del df_kaskade_master_area_nr_strukt

        df_result_rule4 = self.df_result[
            (self.df_result["area_nr_kask"].isna()) | (self.df_result["area_nr_kask"] == "")
        ]
        df_result_rule4["area_name_kask"] = "Keine Gebiets ID vorhanden"

        self.df_result = self.df_result[
            (self.df_result["area_nr_kask"].notna()) & (self.df_result["area_nr_kask"] != "")
        ]

        self.df_result = pd.concat([self.df_result, df_result_rule4])
        del df_result_rule4

        self.df_result = self.df_result[
            [
                "requidbafa",
                "area_nr_mont_a",
                "area_name_mont_a",
                "area_nr_klsid_a",
                "area_name_klsid_a",
                "area_nr_strukt",
                "area_name_strukt",
                "area_nr_kask",
                "area_name_kask",
            ]
        ]

        self.df_result

    def write_result(self) -> None:
        if not self.df_result.empty:
            self.df_result.to_sql_replace("wfm_ggs_1_area")


def fill_kaskade_processing_table() -> None:
    db = DatabaseManager()
    db.truncate(["processing_wfm_ggs_1_area_kaskade"])
    db.deadlock_save_execute(
        """
            INSERT INTO processing_wfm_ggs_1_area_kaskade (
                Select distinct(kask_ba_master) FROM wms_6_master
            )
        """
    )


@catchall
def run_kaskade(**kwargs) -> None:

    fill_kaskade_processing_table()

    mp = Multiprocess(**kwargs)
    mp.start_multiplexer(table_name="processing_wfm_ggs_1_area_kaskade", processing_class=WFMGGS1AreaKaskade)
    mp.start_progressbar(table_name="processing_wfm_ggs_1_area_kaskade")


if __name__ == "__main__":
    fill_processing_table(stop_event=Event())

    run_a(stop_event=Event())

    run_mont_a(stop_event=Event())

    run(stop_event=Event())

    run_kaskade(stop_event=Event())
    exit(1)
