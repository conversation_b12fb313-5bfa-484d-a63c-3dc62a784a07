# WFMT Betr Korr Del - Erweiterte Duplikat-Bereinigung

## Übersicht

Diese Task identifiziert und entfernt Duplikate aus der Tabelle `wfmt_betr_korr` basierend auf zwei verschiedenen Regeln für optimale Performance bei mehreren Millionen Datensätzen.

## Funktionalität

### Duplikat-Erkennungsregeln

#### Regel 1: Aufeinanderfolgende Duplikate
1. **Gruppierung**: Alle Datensätze werden nach `requidbafa` gruppiert
2. **Sortierung**: Pro Gruppe werden die Datensätze nach `aenderungsdatum` aufsteigend sortiert
3. **Vergleich**: Jeder Datensatz wird mit dem direkt vorherigen verglichen
4. **Markierung**: Datensätze mit gleichem `hist_betriebsber_korr` Wert wie der vorherige werden als Duplikate markiert

#### Regel 2: Zeitbasierte Duplikate (0-2 Uhr)
1. **Gruppierung**: Alle Datensätze werden nach `requidbafa` und `hist_betriebsber_korr` gruppiert
2. **Zeitfilter**: Nur Datensätze mit `aenderungsdatum` zwischen 0:00 und 2:00 Uhr werden betrachtet
3. **Erhaltung**: Der älteste Datensatz pro Gruppe bleibt erhalten
4. **Markierung**: Alle anderen Datensätze in der Zeitgruppe werden als Duplikate markiert

### Datenverschiebung
- **Ziel-Tabelle**: Duplikate werden in `wfmt_betr_korr_del` verschoben
- **Löschung**: Duplikate werden aus `wfmt_betr_korr` gelöscht
- **Zeitstempel**: Ein `geloescht_am` Zeitstempel wird hinzugefügt

## Tabellen

### Eingangstabelle: `wfmt_betr_korr`
```sql
CREATE TABLE wfmt_betr_korr (
  requidbafa varchar(15) NOT NULL,
  hist_betriebsber_korr DATETIME NULL,
  aenderungsdatum DATETIME NOT NULL,
  PRIMARY KEY (requidbafa, aenderungsdatum)
);
```

### Ziel-Tabelle: `wfmt_betr_korr_del`
```sql
CREATE TABLE wfmt_betr_korr_del (
  requidbafa varchar(15) NOT NULL,
  hist_betriebsber_korr DATETIME NULL,
  aenderungsdatum DATETIME NOT NULL,
  geloescht_am DATETIME NOT NULL,
  regel varchar(50) NOT NULL,
  PRIMARY KEY (requidbafa, aenderungsdatum)
);
```

### Processing-Tabelle: `processing_wfmt_betr_korr_del`
```sql
CREATE TABLE processing_wfmt_betr_korr_del (
    requidbafa varchar(15) NOT NULL,
    PRIMARY KEY (requidbafa)
);
```

## Beispiele

### Regel 1: Aufeinanderfolgende Duplikate

**Eingangsdaten:**
```
requidbafa | hist_betriebsber_korr | aenderungsdatum
TEST001    | 2023-01-01 00:00:00   | 2023-01-01 10:00:00
TEST001    | 2023-01-01 00:00:00   | 2023-01-02 10:00:00  <- Duplikat (Regel 1)
TEST001    | 2023-01-05 00:00:00   | 2023-01-03 10:00:00
TEST001    | 2023-01-05 00:00:00   | 2023-01-04 10:00:00  <- Duplikat (Regel 1)
```

### Regel 2: Zeitbasierte Duplikate

**Eingangsdaten:**
```
requidbafa | hist_betriebsber_korr | aenderungsdatum
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 01:15:00  <- Behalten (ältester)
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 01:30:00  <- Duplikat (Regel 2)
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 10:00:00  <- Behalten (außerhalb 0-2h)
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 01:45:00  <- Duplikat (Regel 2)
```

### Nach der Verarbeitung

**wfmt_betr_korr** (verbleibende Datensätze):
```
requidbafa | hist_betriebsber_korr | aenderungsdatum
TEST001    | 2023-01-01 00:00:00   | 2023-01-01 10:00:00
TEST001    | 2023-01-05 00:00:00   | 2023-01-03 10:00:00
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 01:15:00
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 10:00:00
```

**wfmt_betr_korr_del** (verschobene Duplikate):
```
requidbafa | hist_betriebsber_korr | aenderungsdatum     | geloescht_am        | regel
TEST001    | 2023-01-01 00:00:00   | 2023-01-02 10:00:00 | 2024-01-15 14:30:00 | aufeinanderfolgend
TEST001    | 2023-01-05 00:00:00   | 2023-01-04 10:00:00 | 2024-01-15 14:30:00 | aufeinanderfolgend
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 01:30:00 | 2024-01-15 14:30:00 | zeitbasiert_0-2h
TEST002    | 2023-02-01 00:00:00   | 2023-02-01 01:45:00 | 2024-01-15 14:30:00 | zeitbasiert_0-2h
```

## Ausführung

### Manuell
```python
from verarbeitung.src.tables.wfmt_betr_korr_del.wfmt_betr_korr_del import run
from multiprocessing import Event

run(stop_event=Event())
```

### Als Teil der DAG
Die Task kann in die DAG-Konfiguration eingebunden werden:
```python
wfmt_betr_korr_del = self.add_task(
    TaskConfig(
        name="wfmt_betr_korr_del",
        maincolumn=[IndexColumn(index_type=IndexType.bafa, column="requidbafa")],
        dependend_tasks=[],
        run_function_path="verarbeitung.src.tables.wfmt_betr_korr_del.wfmt_betr_korr_del",
        run_function_name="run",
        tables=["wfmt_betr_korr"],
        task_type=[TaskType.WFMT],
    )
)
```

## Performance-Optimierungen

### Für mehrere Millionen Datensätze optimiert:

- **Minimale Datenbankbelastung**: ORDER BY aus SQL entfernt, Sortierung in Pandas
- **Chunkweise Verarbeitung**: Daten werden in Chunks verarbeitet für optimale Speichernutzung
- **Zweiphasiger Ansatz**:
  1. Phase: Nur Duplikate sammeln (parallel)
  2. Phase: Batch-Delete mit einem einzigen JOIN (sequential)
- **Keine temporären Tabellen**: Vermeidet Verbindungsprobleme
- **Effiziente Pandas-Operationen**: Optimierte Gruppierung und Filterung
- **Batch-Delete**: Ein einziger DELETE mit JOIN statt tausende einzelne Queries

## Logging

Die Task protokolliert:
- Anzahl verarbeiteter `requidbafa` pro Chunk
- Anzahl gefundener Duplikate pro Regel und Chunk
- Gesamtstatistiken nach Regeln
- Performance-Metriken (Datensätze pro requidbafa)
- Anzahl verschobener und gelöschter Datensätze

### Beispiel-Output:
```
=== WFMT Betr Korr Del - Erweiterte Duplikat-Bereinigung ===
Regel 1: Aufeinanderfolgende Duplikate
Regel 2: Zeitbasierte Duplikate (0-2 Uhr, ältester bleibt)

Processing-Tabelle gefüllt: 125,432 eindeutige requidbafa
Gesamtdatensätze in wfmt_betr_korr: 2,847,391
Durchschnittlich 22.7 Datensätze pro requidbafa

Chunk 0-1000: 1,247 Duplikate gefunden
  - Regel 1 (aufeinanderfolgend): 892
  - Regel 2 (zeitbasiert 0-2h): 355

Insgesamt 45,231 Duplikate in wfmt_betr_korr_del gefunden.

Statistiken nach Regeln:
  - aufeinanderfolgend: 32,145 Duplikate
  - zeitbasiert_0-2h: 13,086 Duplikate

Erfolgreich 45,231 Duplikate aus wfmt_betr_korr gelöscht

=== Endergebnis ===
Duplikate verschoben: 45,231
Duplikate aus wfmt_betr_korr gelöscht: 45,231
  - aufeinanderfolgend: 32,145
  - zeitbasiert_0-2h: 13,086
Duplikat-Bereinigung erfolgreich abgeschlossen!
```

## Sicherheit

- **Transaktional**: Alle Operationen sind transaktional
- **Backup**: Duplikate werden vor der Löschung gesichert
- **Rollback**: Bei Fehlern können die Daten aus `wfmt_betr_korr_del` wiederhergestellt werden
