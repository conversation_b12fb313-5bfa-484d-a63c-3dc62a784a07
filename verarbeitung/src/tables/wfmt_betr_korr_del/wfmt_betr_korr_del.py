#!/usr/bin/python
# coding=utf-8

from multiprocessing import Event
from datetime import datetime

from verarbeitung.src.tables.Common.task import Task
from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, Multiprocess
from typing import Optional
from verarbeitung.src.tables.helper.helper import catchall
import pandas as pd


PROCESSING_TABLE = "processing_wfmt_betr_korr_del"
TARGET_TABLE = "wfmt_betr_korr_del"


class WFMTBetrKorrDel(Task):
    """
    Identifiziert und verschiebt Duplikate aus wfmt_betr_korr:

    Regel 1 (Aufeinanderfolgende Duplikate):
    - Pro requidbafa werden alle Datensätze nach aenderungsdatum aufsteigend sortiert
    - Datensätze mit gleichem hist_betriebsber_korr Wert wie der vorherige werden als Duplikate markiert

    Regel 2 (Zeitbasierte Duplikate):
    - Pro requidbafa werden alle Datensätze mit gleichem hist_betriebsber_korr gesucht
    - Wenn sowohl Tag- (2-23h) als auch Nacht-Datensätze (0-2h) existieren
    - ALLE Nacht-Datensätze werden als Duplikate markiert (Tag-Datensätze bleiben)

    Alle Duplikate werden in wfmt_betr_korr_del verschoben und aus wfmt_betr_korr gelöscht
    """

    def __init__(self, job: Optional[Job] = None, stop_event: Optional[Event] = None, processbar: bool = True):
        super().__init__(job, stop_event=stop_event)
        self.df_duplicates = pd.DataFrame()

    def load_data(self) -> None:
        self.df_source_data = self.db.get_dataframe_by_query(
            f"""
                SELECT
                wfmt.requidbafa,
                wfmt.hist_betriebsber_korr,
                wfmt.aenderungsdatum
                FROM (SELECT requidbafa FROM {PROCESSING_TABLE} LIMIT {self.offset},{self.limit}) as p_bafa
                LEFT JOIN wfmt_betr_korr as wfmt on p_bafa.requidbafa = wfmt.requidbafa
                WHERE wfmt.requidbafa IS NOT NULL
        """)

    def worker(self) -> None:
        if self.df_source_data.empty:
            print(f"Chunk {self.offset}-{self.offset + self.limit}: Keine Daten gefunden")
            return

        duplicates_list = []
        total_requidbafa = len(self.df_source_data['requidbafa'].unique())
        print(f"Chunk {self.offset}-{self.offset + self.limit}: Verarbeite {total_requidbafa} requidbafa mit {len(self.df_source_data)} Datensätzen")

        # Gruppiere nach requidbafa und verarbeite jede Gruppe einzeln
        for requidbafa, group in self.df_source_data.groupby('requidbafa'):
            # Sortiere nach aenderungsdatum aufsteigend für beide Regeln
            group_sorted = group.sort_values('aenderungsdatum').reset_index(drop=True)

            # === REGEL 1: Aufeinanderfolgende Duplikate ===
            regel1_duplicates = self._find_consecutive_duplicates(group_sorted)

            # === REGEL 2: Zeitbasierte Duplikate (0-2 Uhr) ===
            regel2_duplicates = self._find_time_based_duplicates(group_sorted)

            # Kombiniere beide Regelsets (ohne Überschneidungen)
            all_duplicates = self._combine_duplicate_sets(regel1_duplicates, regel2_duplicates)

            if not all_duplicates.empty:
                duplicates_list.append(all_duplicates)

        # Kombiniere alle Duplikate
        if duplicates_list:
            self.df_duplicates = pd.concat(duplicates_list, ignore_index=True)
            # Füge Zeitstempel für die Löschung hinzu
            self.df_duplicates['geloescht_am'] = datetime.now()

            regel1_count = len(self.df_duplicates[self.df_duplicates['regel'] == 'aufeinanderfolgend'])
            regel2_count = len(self.df_duplicates[self.df_duplicates['regel'] == 'zeitbasiert_0-2h'])

            print(f"Chunk {self.offset}-{self.offset + self.limit}: {len(self.df_duplicates)} Duplikate gefunden")
            print(f"  - Regel 1 (aufeinanderfolgend): {regel1_count}")
            print(f"  - Regel 2 (zeitbasiert 0-2h): {regel2_count}")
        else:
            print(f"Chunk {self.offset}-{self.offset + self.limit}: Keine Duplikate gefunden")

    def _find_consecutive_duplicates(self, group_sorted):
        """Findet aufeinanderfolgende Duplikate (Regel 1)"""
        if len(group_sorted) < 2:
            return pd.DataFrame()

        # Erstelle eine Spalte mit dem vorherigen hist_betriebsber_korr Wert
        group_sorted['prev_hist_betriebsber_korr'] = group_sorted['hist_betriebsber_korr'].shift(1)

        # Identifiziere Duplikate (wo aktueller Wert = vorheriger Wert)
        duplicates = group_sorted[
            (group_sorted['hist_betriebsber_korr'] == group_sorted['prev_hist_betriebsber_korr']) &
            (group_sorted['prev_hist_betriebsber_korr'].notna())
        ]

        if not duplicates.empty:
            result = duplicates[['requidbafa', 'hist_betriebsber_korr', 'aenderungsdatum']].copy()
            result['regel'] = 'aufeinanderfolgend'
            return result

        return pd.DataFrame()

    def _find_time_based_duplicates(self, group_sorted):
        """Findet zeitbasierte Duplikate zwischen 0-2 Uhr (Regel 2)

        Regel: Wenn es für einen hist_betriebsber_korr Wert sowohl Tag- als auch Nacht-Datensätze gibt,
        dann lösche ALLE Nacht-Datensätze (0-2 Uhr) und behalte nur die Tag-Datensätze.
        """
        if len(group_sorted) < 2:
            return pd.DataFrame()

        duplicates_list = []

        # Gruppiere nach hist_betriebsber_korr Wert
        for hist_value, hist_group in group_sorted.groupby('hist_betriebsber_korr'):
            if len(hist_group) < 2:  # Keine Duplikate möglich
                continue

            # Sortiere nach aenderungsdatum
            hist_group_sorted = hist_group.sort_values('aenderungsdatum').reset_index(drop=True)

            # Teile in Tag- und Nacht-Datensätze auf
            night_records = hist_group_sorted[
                (hist_group_sorted['aenderungsdatum'].dt.hour >= 0) &
                (hist_group_sorted['aenderungsdatum'].dt.hour < 2)
            ]

            day_records = hist_group_sorted[
                (hist_group_sorted['aenderungsdatum'].dt.hour >= 2) |
                (hist_group_sorted['aenderungsdatum'].dt.hour <= 23)
            ]

            # Wenn es sowohl Tag- als auch Nacht-Datensätze gibt, lösche ALLE Nacht-Datensätze
            if not night_records.empty and not day_records.empty:
                # Alle Nacht-Datensätze sind Duplikate
                duplicates = night_records.copy()
                duplicates['regel'] = 'zeitbasiert_0-2h'
                duplicates_list.append(duplicates[['requidbafa', 'hist_betriebsber_korr', 'aenderungsdatum', 'regel']])

        if duplicates_list:
            return pd.concat(duplicates_list, ignore_index=True)

        return pd.DataFrame()

    def _combine_duplicate_sets(self, regel1_duplicates, regel2_duplicates):
        """Kombiniert beide Regelsets und entfernt Überschneidungen"""
        if regel1_duplicates.empty and regel2_duplicates.empty:
            return pd.DataFrame()

        if regel1_duplicates.empty:
            return regel2_duplicates

        if regel2_duplicates.empty:
            return regel1_duplicates

        # Kombiniere beide DataFrames
        combined = pd.concat([regel1_duplicates, regel2_duplicates], ignore_index=True)

        # Entferne Überschneidungen - Regel 1 hat Priorität
        # (Falls ein Datensatz in beiden Regeln vorkommt, behalte Regel 1)
        combined = combined.drop_duplicates(
            subset=['requidbafa', 'aenderungsdatum'],
            keep='first'  # Behält den ersten (Regel 1)
        )

        return combined

    def write_result(self):
        if not self.df_duplicates.empty:
            print(f"Chunk {self.offset}-{self.offset + self.limit}: Schreibe {len(self.df_duplicates)} Duplikate in {TARGET_TABLE}")

            # Schreibe nur Duplikate in die Lösch-Tabelle
            # Das Löschen aus wfmt_betr_korr erfolgt später in einem Batch
            self.df_duplicates.to_sql_replace(TARGET_TABLE)

            print(f"Chunk {self.offset}-{self.offset + self.limit}: {len(self.df_duplicates)} Duplikate in {TARGET_TABLE} gespeichert")



def fill_processing_table() -> None:
    """Füllt die Processing-Tabelle mit allen requidbafa aus wfmt_betr_korr"""
    db = DatabaseManager()
    print(f"Truncate Tabellen: {PROCESSING_TABLE}, {TARGET_TABLE}")
    db.truncate([PROCESSING_TABLE, TARGET_TABLE])

    print("Fülle Processing-Tabelle mit requidbafa aus wfmt_betr_korr...")
    # Optimiert: Verwende nur eine Query für bessere Performance
    db.deadlock_save_execute(
        f"""
            INSERT INTO {PROCESSING_TABLE} (requidbafa)
            SELECT DISTINCT requidbafa FROM wfmt_betr_korr
            WHERE requidbafa IS NOT NULL
        """
    )

    # Zähle eingefügte Datensätze
    count_result = db.get_dataframe_by_query(f"SELECT COUNT(*) as count FROM {PROCESSING_TABLE}")
    total_requidbafa = count_result.iloc[0]['count']
    print(f"Processing-Tabelle gefüllt: {total_requidbafa} eindeutige requidbafa")

    # Zähle Gesamtdatensätze in wfmt_betr_korr (optimiert)
    total_result = db.get_dataframe_by_query("SELECT COUNT(*) as count FROM wfmt_betr_korr")
    total_records = total_result.iloc[0]['count']
    print(f"Gesamtdatensätze in wfmt_betr_korr: {total_records}")
    print(f"Durchschnittlich {total_records/total_requidbafa:.1f} Datensätze pro requidbafa")


def delete_duplicates_from_source() -> None:
    """Löscht alle Datensätze aus wfmt_betr_korr, die in wfmt_betr_korr_del stehen"""
    db = DatabaseManager()

    print("Lösche alle Duplikate aus wfmt_betr_korr...")

    # Zähle zuerst, wie viele gelöscht werden
    count_query = f"""
        SELECT COUNT(*) as count
        FROM wfmt_betr_korr wfmt
        INNER JOIN {TARGET_TABLE} del_table
        ON wfmt.requidbafa = del_table.requidbafa
        AND wfmt.aenderungsdatum = del_table.aenderungsdatum
    """

    count_result = db.get_dataframe_by_query(count_query)
    to_delete_count = count_result.iloc[0]['count']
    print(f"Anzahl zu löschender Datensätze: {to_delete_count}")

    if to_delete_count > 0:
        # Lösche mit einem einzigen JOIN
        delete_query = f"""
            DELETE wfmt FROM wfmt_betr_korr wfmt
            INNER JOIN {TARGET_TABLE} del_table
            ON wfmt.requidbafa = del_table.requidbafa
            AND wfmt.aenderungsdatum = del_table.aenderungsdatum
        """

        try:
            db.execute(delete_query)
            print(f"Erfolgreich {to_delete_count} Duplikate aus wfmt_betr_korr gelöscht")
        except Exception as e:
            print(f"Fehler beim Löschen der Duplikate: {e}")
            raise
    else:
        print("Keine Duplikate zum Löschen gefunden")


@catchall
def run(**kwargs) -> None:
    print("=== WFMT Betr Korr Del - Erweiterte Duplikat-Bereinigung ===")
    print("Regel 1: Aufeinanderfolgende Duplikate")
    print("Regel 2: Zeitbasierte Duplikate (0-2 Uhr, ältester bleibt)")
    print()

    fill_processing_table()

    print("\nStarte Multiprocessing...")
    mp = Multiprocess(**kwargs)
    mp.start_multiplexer(table_name=PROCESSING_TABLE, processing_class=WFMTBetrKorrDel)
    mp.start_progressbar(table_name=PROCESSING_TABLE)

    print("Multiprocessing abgeschlossen.")

    # Zeige Zwischenergebnisse
    db = DatabaseManager()
    result = db.get_dataframe_by_query(f"SELECT COUNT(*) as count FROM {TARGET_TABLE}")
    total_found = result.iloc[0]['count']
    print(f"Insgesamt {total_found} Duplikate in {TARGET_TABLE} gefunden.")

    # Zeige Statistiken nach Regeln
    if total_found > 0:
        regel_stats = db.get_dataframe_by_query(f"""
            SELECT regel, COUNT(*) as count
            FROM {TARGET_TABLE}
            GROUP BY regel
        """)
        print("\nStatistiken nach Regeln:")
        for _, row in regel_stats.iterrows():
            print(f"  - {row['regel']}: {row['count']} Duplikate")

        # Lösche alle Duplikate aus der ursprünglichen Tabelle
        #delete_duplicates_from_source()

        print("\n=== Endergebnis ===")
        print(f"Duplikate verschoben: {total_found}")
        print(f"Duplikate aus wfmt_betr_korr gelöscht: {total_found}")
        for _, row in regel_stats.iterrows():
            print(f"  - {row['regel']}: {row['count']}")
        print("Duplikat-Bereinigung erfolgreich abgeschlossen!")
    else:
        print("Keine Duplikate gefunden - keine weiteren Aktionen erforderlich.")


if __name__ == "__main__":
    run(stop_event=Event())
    exit(0)
