import gc
import json
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from os import path
from threading import Event
from typing import Callable, Dict, List, Optional

import pandas as pd
from psutil import virtual_memory
from platform import system

from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager, FrontendDatabaseManager
from verarbeitung.src.tables.Multiprocessing.Multiprocessing import Job, default_number_processes
from Modules.beast_modules import TaskType

BASE_FOLDER = "/mnt/data"

TYPO3_DB_NAME = "beast_typo3"

AUTOINCREMENT_QUERY = "ALTER TABLE {name} ADD `id` int auto_increment NOT NULL, ADD PRIMARY KEY (id);"

COLUMNS_REPORT = [
    "sql_basis1_create_query",
    "sql_basis1_select_query",
    "sql_basis1_index_query",
    "basis1_ref_tables_d_b_names",
    "sql_create_query",
    "sql_index_query",
    "sql_select_query",
    "title",
    "database_name",
    "ref_tables_d_b_names",
    "uid",
]
COLUMNS_SERVERFILTER = [
    "normal_filter_json",
]

COLUMNS_AGGREGATIONFILTER = [
    "filter_json",
]

COLUMNS_BASIS = ["db_name"]


def check_ram(stop_event: Event, max_current_memory_usage_percent: int = 75, min_free_ram: int = None) -> None:
    """wait until there is allegedly enough free ram to proceed"""
    os = system()
    if not stop_event:
        stop_event = Event()
    # mac workaround
    if os == "Darwin":
        max_current_memory_usage_percent = 100.0
    while not stop_event.is_set():
        print("Getting current memory usage")
        memory = virtual_memory()

        # cap min_free_ram
        max_usable_memory = (memory.total / 1024.0**2) - 3 * 1024.0
        if min_free_ram is not None and min_free_ram > max_usable_memory:
            min_free_ram = max_usable_memory

        if (min_free_ram is not None and (memory.available / 1024.0**2) < min_free_ram) or (
            min_free_ram is None and memory.percent > max_current_memory_usage_percent
        ):
            print("Memory cap reached, trying to free up RAM")
            gc.collect()
            stop_event.wait(30)
        else:
            print("Enough free RAM available")
            break


class Task(ABC):
    @abstractmethod
    def __init__(self, job: Job, stop_event: Event = None, database_manager: DatabaseManager = None, **kwargs):
        self.ram_factor = None
        if isinstance(job, Job):
            self.offset = job.offset
            self.limit = job.limit
            self.ram_factor = job.ram_factor
        # no job ,no multiprocessing
        self.result_list_of_dicts = []
        self.df_source_data = pd.DataFrame()
        if stop_event:
            self.stop_event = stop_event
        else:
            self.stop_event = Event()
        self.counter = 0
        # can't be done within parameters of the function or else one "new" connection might
        # kill the connection pool of another connection
        if not database_manager:
            self.db = DatabaseManager()
        else:
            self.db = database_manager

    def prepare(self):
        pass

    def count_table(self, table_name, where=1):
        """Fetch the row count of a table.

        Args:
            table_name (str):

        Returns:
            table row count
        """
        row_count = 0

        _row_count = self.db.deadlock_save_execute(
            f"""
            Select count(*) FROM {table_name} WHERE {where}
            """
        )

        query_result = _row_count.first()
        if query_result[0]:
            row_count = query_result[0]
        return row_count

    def run(self):
        # tODO get ram_factor here
        check_ram(self.stop_event, min_free_ram=self.ram_factor)

        if self.load_data() is False:
            return
        if self.worker() is False:
            return
        self.write_result()
        gc.collect()

        # clean up
        del self.result_list_of_dicts
        gc.collect()
        self.result_list_of_dicts = []
        del self.df_source_data
        gc.collect()
        self.df_source_data = pd.DataFrame()

        if getattr(self, "df_result", None) is not None:
            del self.df_result
            self.df_result = pd.DataFrame()

    def __del__(self):
        self.db.close()
        gc.collect()

    @abstractmethod
    def load_data(self):
        raise NotImplementedError("Must override load_data")

    @abstractmethod
    def worker(self):
        raise NotImplementedError("Must override worker")

    @abstractmethod
    def write_result(self):
        raise NotImplementedError("Must override write_result")

    def stop_worker(self):

        if self.stop_event is None:
            return False

        self.counter += 1

        if self.counter % 100 == 0:
            return self.stop_event.is_set()

        return False


class Status(Enum):

    WAIT_FOR_RUN = 1
    IN_WORK = 2
    DONE = 3
    ERROR = 4
    DEACTIVATED = 5


def get_task_type_by_name(name: str) -> Optional[TaskType]:
    """Try to determine a task by it's name"""
    for _task in [_type for _type in TaskType.__dict__.values() if not _type.module_name.startswith("__")]:
        if _task.module_name.lower() == name.lower():
            return _task
    return None


class IndexType(Enum):

    bafa = 1
    sma = 2
    aaid = 3
    komindex = 4
    bbkid = 5
    klsid = 6
    ba_nummer = 7
    fol_id = 8
    area_nr = 9
    kreditor = 10
    bulk_order_id = 11
    projekt_nr = 12
    supplier_partyid = 13
    nvt_area = 14


@dataclass
class IndexColumn:

    index_type: IndexType = None
    column: str = None

    def to_str(self) -> str:
        return f"{self.index_type.value}|{self.column}"


# attributes to be serialized, rest will be ignored
TASK_ATTRIBUTES = [
    "name",
    "run_function_path",
    "run_function_name",
    "error",
    "start_time",
    "end_time",
    "max_package_size",
    "min_package_size",
    "num_processes",
    "ram_usage",
    "ram_factor",
]


class TaskConfig:
    def __init__(
        self,
        name: str,
        run_function_path: str,
        run_function_name: str,
        maincolumn: List[IndexColumn] = [],
        dependend_tasks: List = [],
        task_type: TaskType = None,
        status: Status = Status.WAIT_FOR_RUN,
        sql=None,
        run_function: Callable = None,
        tables: List = [],
        result_tables: List = [],
        additional_data: Dict = {},
        error: str = None,
        start_time: int = 0,
        end_time: int = 0,
        max_package_size: int = None,
        min_package_size: int = None,
        num_processes: int = None,
        ram_usage: int = None,
        ram_factor: int = None,
        needs_lock: bool = False,
    ):
        self.name = name
        self.run_function_path = run_function_path
        self.run_function_name = run_function_name
        self.maincolumn = maincolumn
        self.task_type = task_type
        self.tables = tables
        self.dependend_tasks = dependend_tasks
        self.status = status
        self.sql = sql
        self.run_function = run_function
        self.start_time = start_time
        self.end_time = end_time
        self.result_tables = result_tables
        self.additional_data = additional_data
        self.error = error
        self.max_package_size = max_package_size
        self.min_package_size = min_package_size
        self.num_processes = num_processes
        if self.num_processes is None:
            self.num_processes = default_number_processes()
        self.ram_usage = ram_usage
        self.ram_factor = ram_factor
        self.needs_lock = needs_lock

    def name(self):
        return self.name

    def get_running_time(self):
        return self.end_time - self.start_time

    def to_str(self) -> str:
        """basically just return the name"""
        return self.name

    def to_json(self):
        """dump 'some' attributes to json. Only use attributes needed for communication. Some attributes like 'maincolumn' are not serializeable and not needed on the worker and vice versa 'run_function' is unneeded on jenkisn server"""
        try:
            return json.dumps({attr: getattr(self, attr) for attr in TASK_ATTRIBUTES})
        except Exception as error:
            print(error)
            raise


class DeleteTaskConfig:
    def __init__(
        self,
        name: str,
        run_function_path: str,
        run_function_name: str,
        dependent: List = [],
        task_type: TaskType = None,
        status: Status = Status.WAIT_FOR_RUN,
        sql=None,
        run_function: Callable = None,
        tables: List = [],
        result_tables: List = [],
        additional_data: Dict = {},
        delete: bool = False,
        error: str = None,
    ):
        self.name = name
        self.task_type = task_type
        self.tables = tables
        self.dependend_tasks = dependent
        self.run_function_path = run_function_path
        self.run_function_name = run_function_name
        self.run_function = run_function
        self.sql = sql
        self.status = status
        self.start_time = 0
        self.end_time = 0
        self.result_tables = result_tables
        self.additional_data = additional_data
        self.delete = delete
        self.error = error

    def name(self):
        return self.name

    def get_running_time(self):
        return self.end_time - self.start_time


def build_pre_task(dataset):

    root_folder = path.join(BASE_FOLDER, dataset["database_name"])
    temp_folder = path.join(root_folder, "basis")
    target_csv_file = path.join(root_folder, "{}_basis.csv".format(dataset["database_name"]))
    target_table = dataset["database_name"] + "_basis"

    filter_json = None
    if dataset["filter_json"]:
        filter_json = json.loads(dataset["filter_json"])
    server_filter_json = None
    if dataset["normal_filter_json"]:
        server_filter_json = json.loads(dataset["normal_filter_json"])
    if dataset["db_name"]:
        basis_table = dataset["db_name"]

    additional_data = {
        "temp_folder": temp_folder,
        "target_csv_file": target_csv_file,
        "target_table": target_table,
        "target_basis_table": target_table,
        "sql_basis1_create_query": dataset["sql_basis1_create_query"].format(name=target_table).lower(),
        "sql_basis1_select_query": dataset["sql_basis1_select_query"].lower(),
        "sql_basis1_index_query": dataset["sql_basis1_index_query"].format(name=target_table).lower(),
        "add_autoincrement_query": AUTOINCREMENT_QUERY.format(name=target_table).lower(),
        "filter_json": filter_json,
        "report_id": dataset["uid"],
        "server_filter_json": server_filter_json,
        "basis_table": basis_table,
    }

    pre_task = TaskConfig(
        name="{}_basis1".format(dataset["database_name"]),
        task_type=[TaskType.MultithreadedAggregation],
        dependend_tasks=[],
        maincolumn=[],
        tables=dataset["basis1_ref_tables_d_b_names"].lower().split(","),
        run_function_path="verarbeitung.src.tables.aggregation.multithreaded_aggregations",
        run_function_name="run_pre",
        additional_data=additional_data,
    )

    return pre_task, additional_data


def build_agg_task(dataset, additional_pre_task_data, pre_agg_task):

    root_folder = path.join(BASE_FOLDER, dataset["database_name"])
    temp_folder = path.join(root_folder, "aggregation")
    target_csv_file = path.join(root_folder, "{}.csv".format(dataset["database_name"]))
    target_table = dataset["database_name"]

    additional_data = {
        "temp_folder": temp_folder,
        "target_csv_file": target_csv_file,
        "target_table": target_table,
        "target_basis_table": additional_pre_task_data["target_basis_table"],
        "import_table": "{}_import".format(target_table),
        "sql_create_query": dataset["sql_create_query"],
        "sql_select_query": dataset["sql_select_query"],
        "sql_index_query": dataset["sql_index_query"].lower(),
        "report_id": dataset["uid"],
        "report_title": dataset["title"],
    }

    agg_task = TaskConfig(
        name=dataset["database_name"],
        task_type=[TaskType.MultithreadedAggregation],
        dependend_tasks=[pre_agg_task],
        maincolumn=[],
        tables=dataset["ref_tables_d_b_names"].lower().split(",") + [additional_pre_task_data["target_table"]],
        run_function_path="verarbeitung.src.tables.aggregation.multithreaded_aggregations",
        run_function_name="run",
        additional_data=additional_data,
    )
    return agg_task


def build_tasks(dataset, task_dict=None, module=None):
    """Create all tasks for an aggreation from a dataset with all dependencies.

    Args:
        dataset (dict): dict containing all data for an aggregation taken fropm the db

    Returns:
        list containing tasks
    """

    pre_task, additional_pre_task_data = build_pre_task(dataset)
    agg_task = build_agg_task(dataset, additional_pre_task_data, pre_task)

    # add dependencies
    if task_dict:
        for task in task_dict.values():
            if task.name in dataset["basis1_ref_tables_d_b_names"]:
                pre_task.dependend_tasks.append(task)
                # for _type in task.task_type:
                #    if not _type in pre_task.task_type:
                #        pre_task.task_type.append(_type)

            if task.name in dataset["ref_tables_d_b_names"]:
                agg_task.dependend_tasks.append(task)
                # for _type in task.task_type:
                #    if not _type in agg_task.task_type:
                #        agg_task.task_type.append(_type)

    # override task type for now
    pre_task.task_type.append(TaskType.BI4PSL)
    agg_task.task_type.append(TaskType.BI4PSL)

    return [pre_task, agg_task]


def get_sql_definitions_from_db_for_dynamic_aggregation_tasks():
    """
    read all necessary information from typo3 db in order to build all aggregation tasks with all its dependencies
    :return: Database Repsonse Object (sql_create_query,sql_insert_query,title,database_name,ref_tables_d_b_name)
    """
    db = FrontendDatabaseManager()
    all_columns_query = (
        ["report.{col} as {col}".format(col=col) for col in COLUMNS_REPORT]
        + ["serverfilter.{col} as {col}".format(col=col) for col in COLUMNS_SERVERFILTER]
        + ["aggregationFilter.{col} as {col}".format(col=col) for col in COLUMNS_AGGREGATIONFILTER]
        + ["reftable.{col} as {col}".format(col=col) for col in COLUMNS_BASIS]
    )
    all_columns = COLUMNS_REPORT + COLUMNS_SERVERFILTER + COLUMNS_AGGREGATIONFILTER + COLUMNS_BASIS

    SELECT = """
           SELECT {columns} FROM {typo3_db}.tx_beast_domain_model_report as report
           LEFT JOIN {typo3_db}.tx_beast_domain_model_serverfilter as serverfilter
           ON report.server_filter = serverfilter.uid
           LEFT JOIN {typo3_db}.tx_beast_domain_model_aggregationfilter as aggregationFilter 
           ON report.aggregation_filter=aggregationFilter.uid
           LEFT JOIN {typo3_db}.tx_beast_domain_model_reftablejoinconfig as reftablejoinconfig 
           ON report.ref_table_join_config=reftablejoinconfig.uid
           LEFT JOIN {typo3_db}.tx_beast_domain_model_reftableconfig as reftableconfig 
           ON reftablejoinconfig.basis_reftable_config=reftableconfig.uid           
           LEFT JOIN {typo3_db}.tx_beast_domain_model_reftable as reftable 
           ON reftableconfig.ref_table=reftable.uid
           WHERE  aggregation=1 and dynamic_report=0
       """.format(
        columns=",".join(all_columns_query),
        typo3_db=TYPO3_DB_NAME,
    )
    response = db.deadlock_save_execute(SELECT).all()
    sql_data = []
    for row in response:
        data = {column: row[all_columns.index(column)] for column in all_columns}
        sql_data.append(data)
    return sql_data
