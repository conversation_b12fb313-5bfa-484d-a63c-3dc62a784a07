import re
from os import path, listdir, mkdir
from shutil import rmtree

from verarbeitung.src.tables.Database.DatabaseManager import (
    DatabaseManager,
    FrontendDatabaseManager,
    to_sql_replace,
    Typo3DatabaseManager,
)
from verarbeitung.test.utils.helper import read_csv_headline
import pytest
import sys
import pandas as pd
from sqlalchemy import text

# skip broken tests for now
# TODO fix tests
# collect_ignore = ["test_blackbox.py"]

sys._called_from_test = True
sys._mp_off = True
sys._hdf5_off = True

calc_tables = [
    "basis_1_wms_psl",
    "orge",
    "ref_ni_schema",
    "sm_auftrag_psl_allgemein",
    "ueweg_1_allgemein",
    "wms_1_allgemein",
    "wms_2_termine",
    "wmsti_psl_001",
    "sm_auftrag_psl_finanzen",
    "sm_auftrag_psl_allgemein",
    "ref_orge",
    "statusergaenzungen",
    "iwan_ap_serg_reihenfolge",
    "iwan_bezugsdauer",
    "iwan_projektgruppe",
    "ref_fr_ivh",
    "ref_fr_container",
    "psl_geschaeftsfall",
    "workflow_auftrag_zeitpunkte",
    "wms_1_allgemein",
    "wms_2_termine",
    "psl_1_allgemein",
    "psl_2_psp",
    "psl_3_termine",
    "psl_4_status",
    "psl_5_meldungen",
    "psl_6_finanz",
    "psl_geschaeftsfall",
    "psl_meldungen",
    "wms_10_steuernd",
    "wms_3_lieferzeit",
    "wms_4_arbeitsplan",
    "wms_5_prodreif",
    "wms_6_master",
    "wms_7_gefaehrdet",
    "wms_8_prio",
    "wms_9_zusatz",
    "phasen_1_daprida",
    "phasen_2_diagnose",
    "phasen_3_angebot",
    "phasen_4_projektierung",
    "phasen_5_realisierung",
    "phasen_6_inbetriebnahme",
    "phasen_7_dokumentation",
    "phasen_8_abschluss",
    "phasen_9_esel",
    # "ref_phase",
    "fr_1_wms",
    "fr_2_psl",
    "gf_1_allgemein",
    "iwan_bezugsdauer",
    "iwan_gf_pgz_detail",
    "iwan_projektgruppe",
    "linientechnische_veraenderung",
    "mega_1_techabschl",
    "nap_1_analyse",
    "nap_full",
    "workflow_auftrag_zeitpunkte",
    # REF Tables
    "ref_prod_stoer_tafel",
    "referenz_gewerke_ausnahme",
    "ref_ende_ue_wege",
    "processing_bafa",
    "processing_sma",
    "processing_bafa_by_sma",
]


@pytest.fixture(autouse=True)
def database_manager(monkeypatch):
    monkeypatch.setattr(DatabaseManager, "DATABASE", DatabaseManager._test_db)
    monkeypatch.setenv("PROCESSING_DATABASE", DatabaseManager._test_db)

    database_manager = DatabaseManager(_called_from_test=True)
    current_path = path.dirname(path.realpath(__file__))

    __engine = database_manager.create_nonspecific_connection()

    with __engine.begin() as connection:
        _result = connection.execute(text("SHOW DATABASES;")).all()
    existing_databases = [db[0] for db in _result]

    if DatabaseManager._test_db in existing_databases:
        drop_query = f"""DROP DATABASE IF EXISTS {DatabaseManager._test_db}"""
        database_manager.execute(drop_query)

    create_querys = [
        f"""CREATE DATABASE {DatabaseManager._test_db}""",
        f"""USE {DatabaseManager._test_db}""",
    ]
    for _query in create_querys:
        with __engine.begin() as connection:
            connection.execute(text(_query))
            # connection.commit()

    database_manager.create_new_connection(DatabaseManager._test_db)
    query = database_manager.load_sql_file(path.join(current_path, "data/empty_structure.sql"))
    database_manager.execute(query)
    yield database_manager
    # cleanup


@pytest.fixture
def frontend_database_manager(monkeypatch):
    monkeypatch.setattr(FrontendDatabaseManager, "DATABASE", FrontendDatabaseManager._test_db)
    monkeypatch.setenv("FRONTEND_DATABASE", FrontendDatabaseManager._test_db)

    current_path = path.dirname(path.realpath(__file__))
    frontend_database_manager = FrontendDatabaseManager(_called_from_test=True)

    __engine = frontend_database_manager.create_nonspecific_connection()

    with __engine.begin() as connection:
        _result = connection.execute(text("SHOW DATABASES;")).all()
    existing_databases = [db[0] for db in _result]

    if FrontendDatabaseManager._test_db in existing_databases:
        drop_query = f"""DROP DATABASE IF EXISTS {FrontendDatabaseManager._test_db}"""
        frontend_database_manager.execute(drop_query)

    create_querys = [
        f"""CREATE DATABASE {FrontendDatabaseManager._test_db}""",
        f"""USE {FrontendDatabaseManager._test_db}""",
    ]

    for _query in create_querys:
        with __engine.begin() as connection:
            connection.execute(text(_query))
            # connection.commit()

    frontend_database_manager.create_new_connection(FrontendDatabaseManager._test_db)
    query = frontend_database_manager.load_sql_file(path.join(current_path, "data/empty_structure_frontend.sql"))
    frontend_database_manager.execute(query)
    yield frontend_database_manager


@pytest.fixture
def typo3_database_manager():
    database_manager = Typo3DatabaseManager()
    current_path = path.dirname(path.realpath(__file__))

    __engine = database_manager.create_nonspecific_connection()
    with __engine.begin() as connection:
        existing_databases = [db[0] for db in connection.execute(text("SHOW DATABASES;")).all()]

    if Typo3DatabaseManager._test_db in existing_databases:
        drop_query = f"""DROP DATABASE IF EXISTS {Typo3DatabaseManager._test_db}"""
        database_manager.execute(drop_query)

    create_querys = [
        f"""CREATE DATABASE {Typo3DatabaseManager._test_db}""",
        f"""USE {Typo3DatabaseManager._test_db}""",
    ]
    for _query in create_querys:
        with __engine.begin() as connection:
            connection.execute(text(_query))
            # connection.commit()

    database_manager.create_new_connection(Typo3DatabaseManager._test_db)
    query = database_manager.load_sql_file(path.join(current_path, "data/empty_structure_typo3.sql"))
    database_manager.execute(query)
    yield database_manager


@pytest.fixture
def create_csv_files(monkeypatch, database_manager):
    from verarbeitung.test.utils.helper import create_csv_files as _create_csv_files

    def create_csv_files(
        test_data_folder,
        tables=None,
        sql=None,
        name=None,
        df=None,
        date_columns=None,
        int_columns=[],
        database_manager=database_manager,
    ):

        return _create_csv_files(
            test_data_folder,
            tables=tables,
            sql=sql,
            name=name,
            df=df,
            date_columns=date_columns,
            int_columns=int_columns,
            database_manager=database_manager,
        )

    yield create_csv_files


@pytest.fixture
def debug_data_folder(test_data_folder):
    current_path = path.dirname(path.realpath(__file__))
    debug_folder = path.join(current_path, test_data_folder, "debug_data")
    # TODO create folder and clear if exists
    rmtree(debug_folder, ignore_errors=True)
    mkdir(debug_folder)
    return debug_folder


@pytest.fixture
def path_to_input_data(test_data_folder):
    current_path = path.dirname(path.realpath(__file__))
    return path.join(current_path, test_data_folder, "input_data")


@pytest.fixture
def path_to_new_input_data(test_data_folder):
    current_path = path.dirname(path.realpath(__file__))
    return path.join(current_path, test_data_folder, "new_input_data")


@pytest.fixture
def result_data_folder(test_data_folder):
    current_path = path.dirname(path.realpath(__file__))
    result_data_directory = path.join(current_path, test_data_folder, "result_data")
    if not path.exists(result_data_directory):
        mkdir(result_data_directory)
    return result_data_directory


@pytest.fixture
def input_csv_files(path_to_input_data):
    return [
        _file
        for _file in [f for f in listdir(path_to_input_data) if path.isfile(path.join(path_to_input_data, f))]
        if _file.endswith(".csv")
    ]


@pytest.fixture
def input_sql_files(path_to_input_data):
    return [
        _file
        for _file in [f for f in listdir(path_to_input_data) if path.isfile(path.join(path_to_input_data, f))]
        if _file.endswith(".sql")
    ]


@pytest.fixture
def input_new_csv_files(path_to_new_input_data):
    return [f for f in listdir(path_to_new_input_data) if path.isfile(path.join(path_to_new_input_data, f))]


@pytest.fixture
def f_test_tables(input_csv_files):
    return list(map(lambda file: path.splitext(file)[0], input_csv_files))


@pytest.fixture
def load_test_folder(test_data_folder):
    """brauchen wir vermutlich nicht"""
    test_directories = [f for f in listdir(test_data_folder) if path.isdir(path.join(test_data_folder, f))]
    return test_directories


DATATYPE_MAPPING = [
    {
        "types": ["CHAR", "BLOB", "TEXT", "BINARY", "ENUM", "VARCHAR", "VARBINARY", "SET"],
        "data_type": str,
    },
    # {"types": ["DATE", "DATETIME", "TIME"], "default": "00-00-00 00:00:00", "data_type": np.datetime64},
    # {
    #    "types": ["INT", "DOUBLE", "YEAR", "TINYINT", "BIGINT", "MEDIUMINT", "NUMERIC"],
    #    "data_type": np.int64,
    # },
    # {
    #    "types": ["FLOAT", "DECIMAL"],
    #    "data_type": np.float64,
    # },
]


def map_data_type(column_type):
    column_type = column_type.upper()
    for _type in DATATYPE_MAPPING:
        # if column_type in _type["types"]:
        if any([type in column_type for type in _type["types"]]):
            return _type["data_type"]
    return None


def get_db_colums_information(database_manager, table_name):
    columns = {}
    query = f"SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table_name}'"
    with database_manager.engine.begin() as connection:
        result = connection.execute(text(query)).all()
    columns = {_name: map_data_type(_type) for _name, _type in result if map_data_type(_type) is not None}
    return columns


@pytest.fixture
def frontend_tables_names():
    current_path = path.dirname(path.realpath(__file__))
    sql_file = path.join(current_path, "data/empty_structure_frontend.sql")
    tables = []
    with open(sql_file, "r") as _file:
        _lines = _file.readlines()
        _lines = [line for line in _lines if line.lower().startswith("create")]
        expression = r".*\`(.*)\`.*"
        tables = [re.match(expression, line).group(1) for line in _lines]

    return tables


def _load_test_data_from_csv(
    database_manager,
    frontend_database_manager,
    typo3_database_manager,
    path_to_input_data,
    input_csv_files,
    input_sql_files,
    frontend_tables_names,
):

    for sql_file in input_sql_files:
        db = database_manager
        _commands = []
        _sql_file = path.join(path_to_input_data, sql_file)
        with open(_sql_file, "r") as _file:
            _commands = [_command for _command in _file.read().split(";") if _command.strip()]
        for command in _commands:
            db.deadlock_save_execute(command)

    for csv_file in input_csv_files:
        path_to_csv = path.join(path_to_input_data, csv_file)
        table = csv_file.replace("test.", "")
        table = table.replace(".csv", "")

        db = database_manager
        if table in frontend_tables_names:
            db = frontend_database_manager
        if csv_file.startswith("tx_beast_"):
            db = typo3_database_manager

        # get existing column headers from db
        column_name_query = f"SELECT column_name from information_schema.columns WHERE table_schema = '{db._test_db}' AND table_name = '{table}'"
        column_name_row_proxy_list = db.deadlock_save_execute(column_name_query).all()
        db_column_name_list = [column[0] for column in column_name_row_proxy_list]
        # column names from csv file
        column_name_list = read_csv_headline(path_to_csv)

        column_types = get_db_colums_information(db, table)

        if column_name_list:
            # if there are no headers the file is probably empty
            df_source_data = pd.read_csv(
                path_to_csv,
                delimiter=";",
                dtype={key: value for key, value in column_types.items() if key in column_name_list},
            )

            columns = [col for col in df_source_data.columns if col in db_column_name_list and col in column_name_list]
            df_source_data = df_source_data[columns]
            to_sql_replace(df_source_data, table, deduplicate=False, database_manager=db)


@pytest.fixture
def load_test_data_from_csv(
    database_manager,
    frontend_database_manager,
    typo3_database_manager,
    path_to_input_data,
    input_csv_files,
    input_sql_files,
    frontend_tables_names,
):
    return _load_test_data_from_csv(
        database_manager,
        frontend_database_manager,
        typo3_database_manager,
        path_to_input_data,
        input_csv_files,
        input_sql_files,
        frontend_tables_names,
    )


@pytest.fixture
def import_new_data(
    database_manager,
    frontend_database_manager,
    typo3_database_manager,
    path_to_new_input_data,
    input_new_csv_files,
    input_new_sql_files,
    frontend_tables_names,
):
    class Import:
        def __init__(
            self,
            database_manager,
            frontend_database_manager,
            typo3_database_manager,
            path_to_new_input_data,
            input_new_csv_files,
            input_new_sql_files,
            frontend_tables_names,
        ):
            self.database_manager = database_manager
            self.frontend_database_manager = frontend_database_manager
            self.typo3_database_managaer = typo3_database_manager
            self.path_to_new_input_data = path_to_new_input_data
            self.input_new_csv_files = input_new_csv_files
            self.input_new_sql_files = input_new_sql_files
            self.frontend_table_names = frontend_tables_names

        def load(self):
            _load_test_data_from_csv(
                self.database_manager,
                self.frontend_database_manager,
                self.typo3_database_managaer,
                self.path_to_new_input_data,
                self.input_new_csv_files,
                self.input_new_sql_files,
                self.frontend_table_names,
            )

    return Import(
        database_manager,
        frontend_database_manager,
        typo3_database_manager,
        path_to_new_input_data,
        input_new_csv_files,
        input_new_sql_files,
        frontend_tables_names,
    )
