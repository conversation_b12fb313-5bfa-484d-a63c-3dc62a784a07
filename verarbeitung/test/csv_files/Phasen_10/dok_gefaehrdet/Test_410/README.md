# Diese Testdaten wurden am 2019-04-26 um 09:23:26Uhr generiert. Als Test Ziel wurde phasen_10_gefaehrdet gewählt. 

*Achtung*

Es wird empfohlen die CSV Dateien mit dem Progamm TadViwer zu bearbeiten. 
Diese Programm gibt es für Mac, Windows und Linux.
[<PERSON><PERSON><PERSON><PERSON>](https://www.tadviewer.com/#news)

### Folgende Request ID's wurden übergeben: 
w00000074084074 w00000075074354 w00000074357338 w00000067762292 w00000061824519 w00000081272378 w00000081498979 w00000081106998 w00000083640486 w00000083514740 w00000083446865 w00000083322283 w00000082430191 w00000082198287 w00000083439966
### Das Auswertedatum ist : 
2019-04-26 um 11:16:46Uhr

#Information:
| Filter <PERSON>                                                                             | arbeitsplan_bez                                | statusbafa                          | dok_sezst             | betrbereitkorrdatum                 |
|-------------------------------------------------------------------------------------------|------------------------------------------------|-------------------------------------|-----------------------|-------------------------------------|
| Regel 1                                                                                   | "beginnt nicht mit 'AIP' "                     |                                     |                       |                                     |
| Regel 2	='AIP_S6.03B_Realisierung_Abbau_SFP-Modul_BNG'	"<> „Produktion Abgeschlossen“ und |                                                |                                     |                       |                                     |
| „Abgeschlossen“"                                                                          | "kein gültiges Datum"                          | < 'Auswertedatum' - 10 Kalendertage |                       |                                     |
| Regel 3	='AIP_S6.03B_Realisierung_Abbau_SFP-Modul_BNG'	"<> „Produktion Abgeschlossen“ und |                                                |                                     |                       |                                     |
| „Abgeschlossen“"                                                                          | "kein gültiges Datum"                          | > 'Auswertedatum' - 10 Kalendertage |                       |                                     |
| Regel 3	='AIP_S6.03B_Realisierung_Abbau_SFP-Modul_BNG'	"<> „Produktion Abgeschlossen“ und |                                                |                                     |                       |                                     |
| „Abgeschlossen“"                                                                          | "kein gültiges Datum"                          | = 'Auswertedatum' - 10 Kalendertage |                       |                                     |
| Regel 3	='AIP_S6.03B_Realisierung_Abbau_SFP-Modul_BNG'	"<> „Produktion Abgeschlossen“ und |                                                |                                     |                       |                                     |
| „Abgeschlossen“"                                                                          | "Ein gültiges Datum ist vorhanden"             | < 'Auswertedatum' - 10 Kalendertage |                       |                                     |
| Regel 3                                                                                   | ='AIP_S6.03B_Realisierung_Abbau_SFP-Modul_BNG' | =„Produktion Abgeschlossen“         | "kein gültiges Datum" | < 'Auswertedatum' - 10 Kalendertage |
| Regel 3                                                                                   | ='AIP_S6.03B_Realisierung_Abbau_SFP-Modul_BNG' | =„Abgeschlossen“                    | "kein gültiges Datum" | < 'Auswertedatum' - 10 Kalendertage |


##Regel1:
w00000074084074,w00000075074354,w00000074357338,w00000067762292,w00000061824519
##Regel2:
w00000081272378,w00000081498979,w00000081106998
##Regel3.1:
w00000083640486,w00000083514740,w00000083446865,w00000083322283,w00000082430191
##Regel3.2:
Keine Daten gefunden
##Regel3.3:
w00000082198287
##Regel3.4:
Keine Daten gefunden
##Regel3.5:
w00000083439966


#Zusammenfassung der Verarbeitung:

### Folgende SMA's wurde für diesen Testdatensatz verarbeitet: 
204439040 300195522 202054690 204756503 203140295 204772328 204603403 204823097 204561813 300196630 204790581 204372761 204401436 204738815

### Folgende Request ID's (Bafa) wurde für diesen Testdatensatz verarbeitet: 
w00000074084074 w00000075074354 w00000074357338 w00000067762292 w00000061824519 w00000081272378 w00000081498979 w00000081106998 w00000083640486 w00000083514740 w00000083446865 w00000083322283 w00000082430191 w00000082198287 w00000083439966

