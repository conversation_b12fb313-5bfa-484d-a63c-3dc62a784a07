requidba<PERSON>;anb_sezst;anv_sezst;wandlung_angebot;ane_sezst;ake_ang_sezst;akl_ang_sezst;akn_ang_sezst;proj_ltg_e_sezst;vlt_erl_sezst;vrt_erl_sezst;ser_sezst;suee_sezst;sia_sezst
e00000048266822;1999-01-01 00:00:00;1999-01-01 00:00:00;Nein;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00
e00000048266836;1999-01-01 00:00:00;1999-01-01 00:00:00;Nein;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00
e00000048267049;1999-01-01 00:00:00;1999-01-01 00:00:00;Nein;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00
e00000048267050;1999-01-01 00:00:00;1999-01-01 00:00:00;Nein;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00;1999-01-01 00:00:00
e00000070452108;2000-01-01 00:00:00;2000-01-01 00:00:00;Nein;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
e00000070452109;2000-01-01 00:00:00;2000-01-01 00:00:00;Nein;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
e00000070452159;2000-01-01 00:00:00;2000-01-01 00:00:00;Nein;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
T00000075649228;2018-01-25 16:51:12;2000-01-01 00:00:00;Nein;2018-02-13 12:07:13;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000037942633;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000040740672;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000047097967;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000047103671;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000047761553;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000048788616;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000050634306;2150-01-01 00:00:00;2150-01-01 00:00:00;Nein;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00;2150-01-01 00:00:00
w00000090015329;2020-05-19 11:05:45;2000-01-01 00:00:00;Nein;2020-05-19 11:43:33;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-05-19 11:32:05;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090052919;2020-05-21 12:00:11;2000-01-01 00:00:00;Nein;2020-05-21 12:40:33;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-05-21 12:32:03;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090067719;2020-05-23 09:37:34;2000-01-01 00:00:00;Nein;2021-04-26 08:02:55;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-10-13 19:05:39;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090068297;2020-05-23 12:37:11;2000-01-01 00:00:00;Nein;2020-05-23 12:59:09;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-05-23 12:53:10;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090069636;2020-05-23 16:46:45;2000-01-01 00:00:00;Nein;2020-05-23 16:56:01;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-05-28 17:54:03;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090070954;2020-05-24 14:28:55;2000-01-01 00:00:00;Nein;2020-05-24 14:39:35;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090071661;2020-05-24 18:19:02;2000-01-01 00:00:00;Nein;2020-05-24 18:33:41;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090209938;2020-06-02 14:44:15;2000-01-01 00:00:00;Nein;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-06-16 08:35:05;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090235583;2200-01-01 00:00:00;2000-01-01 00:00:00;Nein;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090242642;2020-06-04 10:35:03;2000-01-01 00:00:00;Nein;2020-07-24 17:59:43;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-07-21 12:04:03;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090293930;2020-06-08 13:25:06;2000-01-01 00:00:00;Nein;2020-07-02 10:43:09;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090294244;2020-06-08 13:27:30;2000-01-01 00:00:00;Nein;2020-07-02 10:42:58;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
w00000090295823;2020-06-08 12:26:03;2000-01-01 00:00:00;Nein;2020-06-10 13:17:15;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00;2020-06-09 10:51:03;2000-01-01 00:00:00;2200-01-01 00:00:00;2000-01-01 00:00:00;2000-01-01 00:00:00
