# Diese Testdaten wurden am 2019-07-19 um 06:49:47Uhr generiert. Als Test Ziel wurde phasen_4_projektierung_lz gewählt. 

*Achtung*

Es wird empfohlen die CSV Dateien mit dem Progamm TadViwer zu bearbeiten. 
Diese Programm gibt es für Mac, Windows und Linux.
[<PERSON><PERSON>ie<PERSON>](https://www.tadviewer.com/#news)

### Folgende Request ID's wurden übergeben: 
w00000082458348 w00000083291882 w00000083097747 w00000083307626 w00000082894901 w00000082458348 w00000083291882 w00000083097747 w00000083307626 w00000082894901 w00000080830759 w00000083199824 w00000074885843 w00000080768881 w00000079853527 w00000081288247 w00000082083748 w00000077381834 w00000083091881 w00000083120343 w00000083838246 w00000080117564 w00000083823501 w00000083813100 w00000083819304 w00000081587311 w00000077775953 w00000083214605 w00000082992148 w00000083305954
### Das Auswertedatum ist : 
2019-03-29 um 10:00:00Uhr

#Information:
##regel1a:
w00000082458348,w00000083291882,w00000083097747,w00000083307626,w00000082894901
##regel1b:
Keine Daten gefunden
##regel2a:
w00000082458348,w00000083291882,w00000083097747,w00000083307626,w00000082894901
##regel2b:
Keine Daten gefunden
##regel3a:
w00000080830759,w00000083199824,w00000074885843,w00000080768881,w00000079853527
##regel3b:
w00000081288247,w00000082083748,w00000077381834,w00000083091881,w00000083120343
##regel4a:
w00000083838246,w00000080117564,w00000083823501,w00000083813100,w00000083819304
##regel4b:
w00000081587311,w00000077775953,w00000083214605,w00000082992148,w00000083305954

| Filter Gruppe | [phasen_4_projektierung].[pjg_sezst] | [phasen_4_projektierung].[pja_sezst]                                    | Auswertezeitpunkt                     | erwartete Ergebnis                                         | 
|---------------|--------------------------------------|-------------------------------------------------------------------------|---------------------------------------|------------------------------------------------------------| 
| Regel 1a      | Dummy Datum                          | beliebiger Eintrag                                                      | beliebiger Zeitpunkt                  | 0                                                          | 
| Regel 1b      | leer                                 | beliebiger Eintrag                                                      | beliebiger Zeitpunkt                  | 0                                                          | 
| Regel 2a      | beliebiger Eintrag                   | 2000-01-01 00:00:00                                                     | beliebiger Zeitpunkt                  | 0                                                          | 
| Regel 2b      | beliebiger Eintrag                   | leer                                                                    | beliebiger Zeitpunkt                  | 0                                                          | 
| Regel 3a      | gültiges Datum                       | gleiches Datum  wie [pjg_sezst] wenn vorhanden                          | beliebiger Zeitpunkt                  | 1                                                          | 
| Regel 3b      | gültiges Datum                       | Datum  größer als [pjg_sezst] mit Wochenende oder Feiertag dazwischen   | beliebiger Zeitpunkt                  | Differenz der Datumswerte in Arbeitstagen ohne Uhrzeit + 1 | 
| Regel 4a      | gültiges Datum                       | kein gültiges Datum und nicht leer und nicht gleich 2000-01-01 00:00:00 | gleiches Datum  wie [pjg_sezst]       | 1                                                          | 
| Regel 4b      | gültiges Datum                       | kein gültiges Datum und nicht leer und nicht gleich 2000-01-01 00:00:00 | beliebiger Zeitpunkt nach [pjg_sezst] | Differenz der Datumswerte in Arbeitstagen ohne Uhrzeit + 1 | 


#Zusammenfassung der Verarbeitung:

### Folgende SMA's wurde für diesen Testdatensatz verarbeitet: 
204731523 203274636 204689804 204746892 203838608 204759575 204544283 204614556 204753187 204727589 204863151 204321840 204766138 204645059 204763976 300243532 204726349 204799694 204762836 203709020 204765789 204404589 204142454 204866295 204308603

### Folgende Request ID's (Bafa) wurde für diesen Testdatensatz verarbeitet: 
w00000082458348 w00000083291882 w00000083097747 w00000083307626 w00000082894901 w00000082458348 w00000083291882 w00000083097747 w00000083307626 w00000082894901 w00000080830759 w00000083199824 w00000074885843 w00000080768881 w00000079853527 w00000081288247 w00000082083748 w00000077381834 w00000083091881 w00000083120343 w00000083838246 w00000080117564 w00000083823501 w00000083813100 w00000083819304 w00000081587311 w00000077775953 w00000083214605 w00000082992148 w00000083305954

