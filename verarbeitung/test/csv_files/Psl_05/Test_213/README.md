# Diese Testdaten wurden am 2019-03-05 um 18:45:02Uhr generiert. Als Test Ziel wurde psl_5_meldungen gewählt. 

*Achtung*

Es wird empfohlen die CSV Dateien mit dem Progamm TadViwer zu bearbeiten. 
Diese Programm gibt es für Mac, Windows und Linux.
[<PERSON><PERSON><PERSON><PERSON>](https://www.tadviewer.com/#news)

### Folgende SMA's wurden übergeben: 
000200866756 000200286150 000200200147 000200557014 000200318776 000203652626 000203207908 000202057813 000201352234 000201864707 000200803077 000202587745 000203443837 000202553756 000203040395 000202418950 000202632853 000201281976 000203652626 000204069301 000201840319 000204130058 000204343997 000201412604



#Zusammenfassung der Verarbeitung:

### Folgende SMA's wurde für diesen Testdatensatz verarbeitet: 
000200866756 000200286150 000200200147 000200557014 000200318776 000203652626 000203207908 000202057813 000201352234 000201864707 000200803077 000202587745 000203443837 000202553756 000203040395 000202418950 000202632853 000201281976 000203652626 000204069301 000201840319 000204130058 000204343997 000201412604

# Test_213


## **psl_5_meldungen.x2_erfasst**

### Testfall 1

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag nicht vorhanden.

| sma          | Meldungsart | aend_ts             | verant_arbeitsplatz_text | 
|--------------|-------------|---------------------|--------------------------| 
| 000200866756 | A1          | 2015-12-18 11:00:47 | PTI 11 Dresden           | 
| 000200286150 | A1          | 2015-06-09 07:48:32 | PTI 13 Duisburg          | 
| 000200200147 | A1          | 2015-05-04 08:14:11 | PTI 13 Nürnberg          | 
| 000200557014 | A1          | 2015-09-16 15:23:20 | PTI 24 Braunschweig      | 
| 000200318776 | A1          | 2015-07-07 10:56:32 | PTI 34 Heusenstamm       | 

### Testfall 2

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag vorhanden und das Feld [psl_meldungen].[Erfassungstimestamp] zu dieser Meldungsart ist NULL oder leer oder Dummydatum.

Gibt kein Dummy oder Null Feld

### Testfall 3

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag einmal vorhanden und das Feld [psl_meldungen].[Erfassungstimestamp] zu dieser Meldungsart ist mit einem aktuellen Datum gefüllt.

| sma          | Meldungsart  | aend_ts             | verant_arbeitsplatz_text | 
|--------------|--------------|---------------------|--------------------------| 
| 000203652626 | X2           | 2018-09-07 10:18:03 | PTI 11 Dresden           | 
| 000203207908 | X2           | 2018-10-10 12:52:39 | PTI 13 Duisburg          | 
| 000202057813 | X2           | 2018-09-03 09:52:17 | PTI 13 Nürnberg          | 
| 000201352234 | X2           | 2018-09-03 11:33:11 | PTI 24 Braunschweig      | 
| 000201864707 | X2           | 2018-09-26 07:18:29 | PTI 34 Heusenstamm       | 

### Testfall 4

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag mindestens zweimal vorhanden und das Feld [psl_meldungen].[Erfassungstimestamp] zu dieser Meldungsart ist mit unterschiedlichen Datumseinträgen gefüllt. Sollte dies nicht bei den bevorzugten verant_arbeitsplatz_text zu finden sein, ggf. ein anderes Ressort auswählen.

| sma          | Meldungsart | aend_ts             | verant_arbeitsplatz_text | 
|--------------|-------------|---------------------|--------------------------| 
| 000200803077 | X2          | 2018-09-24 15:25:06 | PTI 11 Dresden           | 
| 000202587745 | X2          | 2018-07-27 09:05:44 | PTI 13 Duisburg          | 
| 000203443837 | X2          | 2018-05-22 08:41:22 | PTI 34 Heusenstamm       | 
| 000202553756 | X2          | 2018-04-03 11:41:09 | PTI 24 Braunschweig      | 
| 000203040395 | X2          | 2018-03-01 14:09:09 | PTI 13 Nürnberg          | 

## **psl_5_meldungen.x2_abschluss**

### Testfall 5

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag vorhanden und das Feld [psl_meldungen].[Abschlusstimestamp] zu dieser Meldungsart ist NULL oder leer oder Dummydatum.

Hier wurden nur Daten für 3 PIT's gefunden.

| sma          | Meldungsart  | aend_ts             | verant_arbeitsplatz_text | 
|--------------|--------------|---------------------|--------------------------| 
| 000202418950 | X2           | 1900-01-01 00:00:00 | PTI 11 Dresden           | 
| 000202632853 | X2           | 1900-01-01 00:00:00 | PTI 13 Nürnberg          | 
| 000201281976 | X2           | 1900-01-01 00:00:00 | PTI 24 Braunschweig      | 

### Testfall 6

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag einmal vorhanden und das Feld [psl_meldungen].[Abschlusstimestamp] zu dieser Meldungsart ist mit einem aktuellen Datum gefüllt.

| sma          | Meldungsart  | aend_ts             | verant_arbeitsplatz_text | 
|--------------|--------------|---------------------|--------------------------| 
| 000203652626 | X2           | 2018-09-11 07:14:24 | PTI 11 Dresden           | 
| 000204069301 | X2           | 2018-12-05 12:01:29 | PTI 13 Duisburg          | 
| 000201840319 | X2           | 2018-12-04 12:04:55 | PTI 13 Nürnberg          | 
| 000204130058 | X2           | 2018-12-04 10:14:28 | PTI 24 Braunschweig      | 
| 000204343997 | X2           | 2018-12-03 11:16:53 | PTI 34 Heusenstamm       | 

### Testfall 7

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag mindestens zweimal vorhanden und das Feld [psl_meldungen].[Abschlusstimestamp] zu dieser Meldungsart ist beim letzten Eintrag NULL oder leer oder Dummydatum. Sollte dies nicht bei den bevorzugten verant_arbeitsplatz_text zu finden sein, ggf. ein anderes Ressort auswählen.

| sma          | aend_ts             | meldungsart | erf_ts              | meld_ts             | abschl_ts           | verant_arbeitsplatz_text     | 
|--------------|---------------------|-------------|---------------------|---------------------|---------------------|------------------------------|
| 000202632853 | 2018-08-20 00:00:00 | X2          | 2017-11-28 13:09:35 | 2017-11-28 13:08:14 | 1900-01-01 00:00:00 | 000900032940 PTI 13 Nürnberg | 
| 000202632853 | 2018-08-20 00:00:00 | X2          | 2017-12-21 09:18:35 | 2017-12-21 09:18:07 | 2018-02-26 12:58:57 | 000900033493 PTI 13 Nürnberg | 


### Testfall 8

[psl_meldungen].[Meldungsart] = "X2" ist zu diesem Auftrag mindestens zweimal vorhanden und das Feld [psl_meldungen].[Abschlusstimestamp] zu dieser Meldungsart ist mit unterschiedlichen Datumseinträgen gefüllt. Sollte dies nicht bei den bevorzugten verant_arbeitsplatz_text zu finden sein, ggf. ein anderes Ressort auswählen.

| sma          | Meldungsart | aend_ts             | verant_arbeitsplatz_text | 
|--------------|-------------|---------------------|--------------------------| 
| 000202587745 | X2          | 2018-08-01 07:25:11 | PTI 13 Duisburg          | 
| 000201412604 | X2          | 2018-02-05 15:32:36 | PTI 11 Dresden           | 
| 000203040395 | X2          | 2018-05-02 12:31:02 | PTI 13 Nürnberg          | 
| 000203443837 | X2          | 2018-05-23 08:40:11 | PTI 34 Heusenstamm       | 
| 000202553756 | X2          | 2018-05-02 08:50:42 | PTI 24 Braunschweig      | 

