# Diese Testdaten wurden am 2019-04-04 um 06:35:09Uhr generiert. Als Test Ziel wurde psl_5_meldungen gewählt. 

*Achtung*

Es wird empfohlen die CSV Dateien mit dem Progamm TadViwer zu bearbeiten. 
Diese Programm gibt es für Mac, Windows und Linux.
[TadViewer](https://www.tadviewer.com/#news)

### Folgende SMA's wurden übergeben: 
000200721830 000204039041 000204611091 000203584991 000204735890 000203681210 000203860889 000204495217 000204179998 000204698524 000204420111 000203334296 000204487156 000204482405 000204519596

### Das Auswertedatum ist : 
2019-04-04 um 06:35:09Uhr

psl_meldungen.Meldungsart = "Z1" mit psl_meldungen.Codierung = "40" ist zu diesem Auftrag nicht vorhanden.

| sma          | verant_arbeitsplatz_text | codierung | meldungsart | 
|--------------|--------------------------|-----------|-------------| 
| 000204420111 | PTI 11 Dresden           |           | A1          | 
| 000203334296 | PTI 13 Duisburg          |           | A1          | 
| 000204487156 | PTI 13 Nürnberg          |           | A1          | 
| 000204482405 | PTI 24 Braunschweig      |           | A1          | 
| 000204519596 | PTI 34 Heusenstamm       |           | A1          | 

psl_meldungen.Meldungsart = "Z1" mit psl_meldungen.Codierung = "40" ist zu diesem Auftrag einmal vorhanden und das Feld psl_meldungen.Meldungstimestamp zu dieser Meldungsart ist mit einem aktuellen Datum gefüllt.

| sma          | verant_arbeitsplatz_text | codierung | meldungsart | 
|--------------|--------------------------|-----------|-------------| 
| 000200721830 | PTI 11 Dresden           | 0040      | Z1          | 
| 000204039041 | PTI 13 Duisburg          | 0040      | Z1          | 
| 000204611091 | PTI 13 Nürnberg          | 0040      | Z1          | 
| 000203584991 | PTI 24 Braunschweig      | 0040      | Z1          | 
| 000204735890 | PTI 34 Heusenstamm       | 0040      | Z1          | 

psl_meldungen.Meldungsart = "Z1" mit psl_meldungen.Codierung = "40" ist zu diesem Auftrag mindestens zweimal vorhanden und das Feld psl_meldungen.Meldungstimestamp zu dieser Meldungsart ist mit unterschiedlichen Datumseinträgen gefüllt. Sollte dies nicht bei den bevorzugten verant_arbeitsplatz_text zu finden sein, ggf. ein anderes Ressort auswählen.

| sma          | verant_arbeitsplatz_text | codierung | meldungsart | 
|--------------|--------------------------|-----------|-------------| 
| 000203681210 | PTI 11 Dresden           | 0040      | Z1          | 
| 000203860889 | PTI 13 Duisburg          | 0040      | Z1          | 
| 000204495217 | PTI 13 Nürnberg          | 0040      | Z1          | 
| 000204179998 | PTI 24 Braunschweig      | 0040      | Z1          | 
| 000204698524 | PTI 34 Heusenstamm       | 0040      | Z1          | 



#Zusammenfassung der Verarbeitung:

### Folgende SMA's wurde für diesen Testdatensatz verarbeitet: 
000200721830 000204039041 000204611091 000203584991 000204735890 000203681210 000203860889 000204495217 000204179998 000204698524 000204420111 000203334296 000204487156 000204482405 000204519596

