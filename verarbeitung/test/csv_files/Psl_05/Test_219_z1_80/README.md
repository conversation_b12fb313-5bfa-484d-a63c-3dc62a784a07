# Diese Testdaten wurden am 2019-04-05 um 08:01:45Uhr generiert. Als Test Ziel wurde psl_5_meldungen gewählt. 

*Achtung*

Es wird empfohlen die CSV Dateien mit dem Progamm TadViwer zu bearbeiten. 
Diese Programm gibt es für Mac, Windows und Linux.
[Tad<PERSON>iewer](https://www.tadviewer.com/#news)

### Folgende SMA's wurden übergeben: 
000200378970 000203547538 000203986238 000203818498 000204464696 000202631720 000203206568 000202959339 000202320838 000203482837 000200772472 000203840164 000204210338 000203714889 000204453454

### Das Auswertedatum ist : 
2019-04-05 um 08:01:45Uhr

### Information:

psl_meldungen.Meldungsart = "Z1" mit psl_meldungen.Codierung = "80" ist zu diesem Auftrag nicht vorhanden.

| sma          | codierung | meldungsart | verant_arbeitsplatz_text |
|--------------|-----------|-------------|--------------------------|
| 000200772472 |           | A1          | PTI 11 Dresden           |
| 000203840164 |           | A1          | PTI 13 Duisburg          |
| 000204210338 |           | A1          | PTI 13 Nürnberg          |
| 000203714889 | MEGA      | Z3          | PTI 24 Braunschweig      |
| 000204453454 |           | A1          | PTI 34 Heusenstamm       |



psl_meldungen.Meldungsart = "Z1" mit psl_meldungen.Codierung = "80" ist zu diesem Auftrag einmal vorhanden und das Feld psl_meldungen.Meldungstimestamp zu dieser Meldungsart ist mit einem aktuellen Datum gefüllt.

| sma          | codierung | meldungsart | verant_arbeitsplatz_text |
|--------------|-----------|-------------|--------------------------|
| 000200378970 | 0080      | Z1          | PTI 11 Dresden           |
| 000203547538 | 0080      | Z1          | PTI 13 Duisburg          |
| 000203986238 | 0080      | Z1          | PTI 13 Nürnberg          |
| 000203818498 | 0080      | Z1          | PTI 24 Braunschweig      |
| 000204464696 | 0080      | Z1          | PTI 34 Heusenstamm       |


psl_meldungen.Meldungsart = "Z1" mit psl_meldungen.Codierung = "80" ist zu diesem Auftrag mindestens zweimal vorhanden und das Feld psl_meldungen.Meldungstimestamp zu dieser Meldungsart ist mit unterschiedlichen Datumseinträgen gefüllt. Sollte dies nicht bei den bevorzugten verant_arbeitsplatz_text zu finden sein, ggf. ein anderes Ressort auswählen.

| sma          | codierung | meldungsart | verant_arbeitsplatz_text | 
|--------------|-----------|-------------|--------------------------| 
| 000202631720 | 0080      | Z1          | PTI 11 Dresden           | 
| 000203206568 | 0080      | Z1          | PTI 13 Duisburg          | 
| 000202959339 | 0080      | Z1          | PTI 13 Nürnberg          | 
| 000202320838 | 0080      | Z1          | PTI 24 Braunschweig      | 
| 000203482837 | 0080      | Z1          | PTI 34 Heusenstamm       | 


#Zusammenfassung der Verarbeitung:

### Folgende SMA's wurde für diesen Testdatensatz verarbeitet: 
000200378970 000203547538 000203986238 000203818498 000204464696 000202631720 000203206568 000202959339 000202320838 000203482837 000200772472 000203840164 000204210338 000203714889 000204453454

