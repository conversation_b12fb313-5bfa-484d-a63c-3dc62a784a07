import os
from pathlib import Path

from markdown import markdown
import pdfkit

import glob

for filename in glob.iglob('./**/*.md', recursive=True):

    input_filename = Path(filename)
    md_last_change = os.path.getmtime(input_filename)

    output_filename = Path("{}pdf".format(filename[:-2]))

    pdf_last_change = 0
    if output_filename.is_file():
        pdf_last_change = os.path.getmtime(output_filename)

    if round(md_last_change, 0) != round(pdf_last_change, 0):

        print("Write {}".format(output_filename))

        with open(input_filename, 'r') as f:
            html_text = markdown(f.read(), output_format='html5', extensions=['markdown.extensions.tables'])

        options = {
            'page-size': 'Letter',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'custom-header': [
                ('Accept-Encoding', 'gzip')
            ],
            'cookie': [
                ('cookie-name1', 'cookie-value1'),
                ('cookie-name2', 'cookie-value2'),
            ],
            'no-outline': None
        }

        pdfkit.from_string(html_text, output_filename, options=options, css="pdf.css")

        os.utime(output_filename, (md_last_change, md_last_change))

print("fertig")