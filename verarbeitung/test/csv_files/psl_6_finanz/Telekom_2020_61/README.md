# Diese Testdaten wurden am 2020-02-24 um 09:39:30Uhr generiert. Als Test Ziel wurde psl_6_finanz gewählt. 

*Achtung*

Es wird empfohlen die CSV Dateien mit dem Progamm TadViwer zu bearbeiten. 
Diese Programm gibt es für Mac, Windows und Linux.
[TadViewer](https://www.tadviewer.com/#news)

### Folgende SMA's wurden übergeben: 
000201139392 000200001502 000200000016 000200000000 000200000009 000200000001 000300150751 000200044144 000200049405 000200059818 000200052251 000200000016 000200000086 000201139392 000200029632 000200017070 000200089384

### Das Auswertedatum ist : 
2020-02-24 um 09:39:30Uhr

# Information

### Zufällige Sma Plan/Ist

| sma_psl      | Budgetart |
|--------------|-----------|
| 000201139392 | ERL       |
| 000200001502 | ERT       |
| 000200000016 | INV1      |
| 000200000000 | INV2      |
| 000200000009 | KST1      |
| 000200000001 | KST2      |
| 000300150751 | SON       |


### Zufällige Sma Obligo

| sma_psl      | Budgetart |
|--------------|-----------|
| 000201139392 | ERL       |
| 000200029632 | INV1      |
| 000200017070 | KST1      |

### mehrere KOA pro BUDA Plan/Ist

| sma_psl      | Budgetart |
|--------------|-----------|
| 000200044144 | INV1      |
| 000200049405 | INV2      |
| 000200059818 | INV2      |
| 000200052251 | KST1      |
| 000200000016 | KST2      |
| 000200000086 | KST2      |


### mehrere KOA pro BUDA Obligo

| sma_psl      | Budgetart | 
|--------------|-----------| 
| 000200089384 | KST1      | 




#Zusammenfassung der Verarbeitung:

### Folgende SMA's wurde für diesen Testdatensatz verarbeitet: 
000201139392 000200001502 000200000016 000200000000 000200000009 000200000001 000300150751 000200044144 000200049405 000200059818 000200052251 000200000016 000200000086 000201139392 000200029632 000200017070 000200089384

