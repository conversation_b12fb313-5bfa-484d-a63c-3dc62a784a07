# Test: Beide Regeln kombiniert

## Beschreibung
Testet die Kombination beider Duplikat-Erkennungsregeln.
Regel 1 hat Priorität bei Überschneidungen.

## Testdaten
- **w00000096461339**: 
  - Regel 1: 2023-01-01 → 2023-01-01 (aufeinanderfolgend)
  - Regel 2: 2023-02-01 mit Tag- und Nacht-Datensätzen
- **w00000096461340**:
  - Regel 1: 2023-03-01 → 2023-03-01 (aufeinanderfolgend)
  - Regel 2: 2023-03-02 mit Tag- und Nacht-Datensätzen

## Erwartetes Ergebnis
4 Duplikate werden erkannt:
- 2 mit `regel = 'aufeinanderfolgend'`
- 2 mit `regel = 'zeitbasiert_0-2h'`

## Testdatum
2024-01-15 14:30:00
