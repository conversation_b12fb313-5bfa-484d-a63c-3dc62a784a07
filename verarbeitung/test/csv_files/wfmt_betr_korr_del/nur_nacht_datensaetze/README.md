# Test: <PERSON><PERSON>cht-Datensätze

## Beschreibung
Testet das Verhalten wenn nur Nacht-Datensätze (0-2 <PERSON><PERSON>) vorhanden sind, aber keine Tag-Datensätze.
Nach Regel 2 werden nur dann Nacht-Datensätze gelöscht, wenn auch Tag-Datensätze existieren.

## Testdaten
- **w00000096461339**: 3 Datensätze mit gleichem `hist_betriebsber_korr`, alle zwischen 01:00-01:45
- **w00000096461340**: 2 Datensätze mit gleichem `hist_betriebsber_korr`, alle zwischen 00:15-00:30

## Erwartetes Ergebnis
Keine Duplikate werden erkannt, da keine Tag-Datensätze existieren.
`wfmt_betr_korr_del` bleibt leer.

## Testdatum
2024-01-15 14:30:00
