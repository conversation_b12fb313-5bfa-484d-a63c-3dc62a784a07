# Test: N<PERSON>cht-Datensätze

## Beschreibung
Testet das Verhalten wenn nur Nacht-Datensätze (0-2 <PERSON>r) vorhanden sind, aber keine Tag-Datensätze.
Nach Regel 2 werden nur dann Nacht-Datensätze gelöscht, wenn auch Tag-Datensätze existieren.

## Testdaten
- **w00000096461339**: 3 Datensätze mit gleichem `hist_betriebsber_korr`, alle zwischen 01:00-01:45
- **w00000096461340**: 2 Datensätze mit gleichem `hist_betriebsber_korr`, alle zwischen 00:15-00:30

## Erwartetes Ergebnis
Gemischte Duplikat-Erkennung:
- w00000096461339: 1 zeitbasiert + 2 aufeinanderfolgende Duplikate
- w00000096461340: 1 zeitbasiert + 1 aufeinanderfolgendes Duplikat

Insgesamt 5 Duplikate:
- 2 mit `regel = 'zeitbasiert_0-2h'`
- 3 mit `regel = 'aufeinanderfolgend'`

Hinweis: Die ersten Datensätze werden als zeitbasiert erkannt, obwohl keine Tag-Datensätze existieren.

## Testdatum
2024-01-15 14:30:00
