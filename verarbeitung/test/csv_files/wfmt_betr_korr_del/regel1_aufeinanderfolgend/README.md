# Test: Regel 1 - Aufeinanderfolgende Duplikate

## Beschreibung
Testet die Erkennung von aufeinanderfolgenden Duplikaten in `wfmt_betr_korr`.

## Testdaten
- **w00000096461339**: 4 Datensätze mit 2 aufeinanderfolgenden Duplikaten
  - 2023-01-01 → 2023-01-01 (Duplikat)
  - 2023-01-05 → 2023-01-05 (Duplikat)
- **w00000096461340**: 3 Datensätze mit 1 aufeinanderfolgendem Duplikat
  - 2023-02-02 → 2023-02-02 (Duplikat)

## Erwartetes Ergebnis
3 Duplikate werden in `wfmt_betr_korr_del` verschoben mit `regel = 'aufeinanderfolgend'`

## Testdatum
2024-01-15 14:30:00
