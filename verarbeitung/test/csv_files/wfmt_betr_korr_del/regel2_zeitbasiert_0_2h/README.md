# Test: Regel 2 - Zeitbasierte Duplikate (0-2 <PERSON><PERSON>)

## Beschreibung
Testet die Erkennung von zeitbasierten Duplikaten zwischen 0-2 <PERSON><PERSON>.
Wenn sowohl Tag- als auch Nacht-Datensätze existieren, werden ALLE Nacht-Datensätze gelöscht.

## Testdaten
- **w00000096461339**: Vers<PERSON>ede<PERSON> `hist_betriebsber_korr` Werte mit Tag- und Nacht-Datensätzen
  - 2023-12-15: 1 Tag-Datensatz (10:29), 2 Nacht-<PERSON>ns<PERSON>ze (01:07) → 2 Nacht-Datensätze gelöscht
  - 2023-12-20: 1 Tag-Datensatz (10:29), 4 Nacht-Date<PERSON><PERSON>ze (01:07-01:14) → 4 Nacht-Datensätze gelöscht
  - 2023-12-13: Nur 1 Nacht-Datensatz (01:13) → gel<PERSON><PERSON><PERSON> (da andere hist_betriebsber_korr Werte Tag-Datensätze haben)

## <PERSON><PERSON><PERSON><PERSON><PERSON> Ergebnis
5 Nacht-Datensätze werden in `wfmt_betr_korr_del` verschoben mit `regel = 'zeitbasiert_0-2h'`

## Testdatum
2024-01-15 14:30:00
