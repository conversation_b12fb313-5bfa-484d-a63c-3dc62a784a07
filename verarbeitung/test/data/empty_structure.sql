-- Config <PERSON>bell<PERSON>

CREATE TABLE IF NOT EXISTS `dag_modules` (
    `name` varchar(200) NOT NULL,
    `run_function_path` varchar(255),
    `run_function_name` varchar(255),
    `dependend_tasks` varchar(255),
    `maincolumn` varchar(255),
    `tables` varchar(255),
    `result_tables` varchar(255),
    `task_type` varchar(255),
    `num_processes` int,
    `max_package_size` int,
    `min_package_size` int,
    PRIMARY KEY (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- Eingangstabellen

-- Create syntax for TABLE 'wmsti_psl_001'
CREATE TABLE IF NOT EXISTS `wmsti_psl_001` (
  `wmsti_zustres` varchar(30) NOT NULL,
  `wmsti_bafa` varchar(35) NOT NULL,
  `wmsti_bafaaenddatum` datetime NOT NULL,
  `wmsti_requidbafa` char(15) NOT NULL,
  `wmsti_refidba` char(15) NOT NULL,
  `wmsti_ref_bafaid_ggst` char(15) NOT NULL,
  `wmsti_statusbafa` varchar(128) NOT NULL,
  `wmsti_storno` tinyint(1) DEFAULT NULL,
  `wmsti_auftrkurzbeschr` varchar(255) NOT NULL,
  `wmsti_auftrnrag` varchar(50) NOT NULL,
  `wmsti_projektid` varchar(30) NOT NULL,
  `wmsti_eingdtagdatum` datetime NOT NULL,
  `wmsti_eingbbndatum` datetime NOT NULL,
  `wmsti_bafabetrbereitdatum` datetime NOT NULL,
  `wmsti_betrbereitkorrdatum` datetime NOT NULL,
  `wmsti_bafatechenddatum_ts` datetime NOT NULL,
  `wmsti_agbzieldatum` datetime NOT NULL,
  `wmsti_servicelevel_id` varchar(3) NOT NULL,
  `wmsti_onkz` mediumint(15) unsigned NOT NULL,
  `wmsti_asb` mediumint(15) unsigned NOT NULL,
  `wmsti_auftrfall` varchar(60) NOT NULL,
  `wmsti_teilauftrfall` varchar(60) NOT NULL,
  `wmsti_prozessausloeser` varchar(10) NOT NULL,
  `wmsti_pgz_bezeichnung` varchar(255) NOT NULL,
  `wmsti_pgz` char(2) NOT NULL,
  `wmsti_arbeitsplan_bez` varchar(100) NOT NULL,
  `wmsti_referenz_nr` varchar(15) NOT NULL,
  `wmsti_serg` varchar(20) NOT NULL,
  `wmsti_vorsystem` varchar(12) NOT NULL,
  `wmsti_vorsystemres` varchar(30) NOT NULL,
  `wmsti_bwlrelevant` int(11) NOT NULL DEFAULT '0',
  `wmsti_smauftrnr_manuell` bigint(12) unsigned zerofill NOT NULL,
  `wmsti_smauftrnr` bigint(12) unsigned zerofill NOT NULL,
  `wmsti_pspelement` varchar(25) NOT NULL,
  `wmsti_bezugselement_pmps` varchar(40) NOT NULL,
  `wmsti_wunschbegdatum` datetime NOT NULL,
  `wmsti_wunschendedatum` datetime NOT NULL,
  `wmsti_prio_bbn` varchar(15) NOT NULL,
  `wmsti_auftrnrfremd` varchar(15) NOT NULL,
  `wmsti_lsz` varchar(15) NOT NULL,
  `wmsti_lszerg` varchar(15) NOT NULL,
  `wmsti_vzgres_betrbereit` varchar(30) NOT NULL,
  `wmsti_vertragsbes` varchar(150) NOT NULL,
  `wmsti_vpsza` varchar(20) NOT NULL,
  `wmsti_vpszb` varchar(20) NOT NULL,
  `wmsti_vpszschaltst` varchar(20) NOT NULL,
  `wmsti_auftrartvorsystem` varchar(15) NOT NULL,
  `wmsti_ts_nichtprodreif` datetime NOT NULL,
  `wmsti_zeitstempel_prodreif` datetime NOT NULL,
  `wmsti_nichtprodreif` varchar(15) NOT NULL,
  `wmsti_merker_nichtprodreif` varchar(15) NOT NULL,
  `wmsti_ber_bafa_betriebsbereit` datetime NOT NULL,
  `wmsti_master` char(5) NOT NULL,
  `wmsti_master_ba` varchar(15) NOT NULL,
  `wmsti_aktarbgrp` varchar(30) NOT NULL,
  `wfmt_onkz_b` mediumint(8) unsigned NOT NULL,
  `wfmt_schaltstelle` varchar(40) NOT NULL DEFAULT 'n.v.',
  `wfmt_ordnungsnummer` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_produktbeschreibung` varchar(255) NOT NULL DEFAULT 'n.v.',
  `wfmt_kunden_name` varchar(40) NOT NULL DEFAULT 'n.v.',
  `wfmt_kund_anspr_name` varchar(120) NOT NULL DEFAULT 'n.v.',
  `wfmt_kundenlok_endst_a` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_vt_punkt` varchar(255) NOT NULL DEFAULT 'n.v.',
  `wfmt_montage_a_ort` varchar(40) NOT NULL DEFAULT 'n.v.',
  `wfmt_montage_a_strasse` varchar(60) NOT NULL DEFAULT 'n.v.',
  `wfmt_montage_a_hausnr` varchar(60) NOT NULL DEFAULT 'n.v.',
  `wfmt_kundenlok_endst_b` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_vt_punkt_b` varchar(255) NOT NULL DEFAULT 'n.v.',
  `wfmt_montage_b_ort` varchar(40) NOT NULL DEFAULT 'n.v.',
  `wfmt_montage_b_strasse` varchar(60) NOT NULL DEFAULT 'n.v.',
  `wfmt_montage_b_hausnr` varchar(60) NOT NULL DEFAULT 'n.v.',
  `wfmt_auftr_hausnr_zusatz` varchar(5) NOT NULL DEFAULT 'n.v.',
  `wfmt_schaediger` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_accessgroupid` varchar(9) NOT NULL DEFAULT 'n.v.',
  `wfmt_ref_bafa_stornoausloeser` varchar(15) NOT NULL DEFAULT 'n.v.',
  `wfmt_stornovonvorsystem` smallint(3) DEFAULT 2,
  `wfmt_stornodatum` datetime NOT NULL,
  `wfmt_stornogrund` varchar(255) NOT NULL DEFAULT 'n.v.',
  `wfmt_ansprpartner_vorsytem_orge` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_sma_ansprechparner` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_sm_asp_regional` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_vzg_text_betrber` varchar(69) NOT NULL DEFAULT 'n.v.',
  `wfmt_kopplungskenner` varchar(256) NOT NULL DEFAULT 'n.v.',
  `wfmt_auftragspositionsnummer` varchar(50) NOT NULL DEFAULT 'n.v.',
  `wfmt_freitext1` varchar(100) NOT NULL DEFAULT 'n.v.',
  `wfmt_freitext2` varchar(50) NOT NULL DEFAULT 'n.v.',
  `wfmt_kdnr` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_montageadr_a` varchar(234) NOT NULL DEFAULT 'n.v.',
  `wfmt_servicelevel` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_arbeitsplan_id` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_mont_a_plz` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_mont_a_zus_str` varchar(60) NOT NULL DEFAULT 'n.v.',
  `wfmt_mont_b_plz` varchar(30) NOT NULL DEFAULT 'n.v.',
  `wfmt_mont_b_zus_str` varchar(60) NOT NULL DEFAULT 'n.v.',
  `wfmt_ressort_typ` varchar(50) NOT NULL DEFAULT 'n.v.',
  `wfmt1_rahmenvertragsnummer` VARCHAR(10) default 'n.v.',
  `wfmt1_beginn_verzoegerung` datetime default null,
  `wfmt1_ende_verzoegerung` datetime default null,
  `wfmt1_auftragstyp` VARCHAR(50) default 'n.v.',
  `wfmt1_lieferfristdiagnosefest` datetime default null,
  `wfmt1_lieferfristproblemloesungfest` datetime default null,
  `wfmt1_uib` tinyint(1) default 0,
  `wfmt1_wandlungstermin` datetime default null,
  `wfmt1_fsz_a` VARCHAR(30) default 'n.v.',
  `wfmt1_fsz_b` VARCHAR(30) default 'n.v.',
  `wfmt1_fsz_schaltstelle` VARCHAR(6) default 'n.v.',
  `wfmt1_medienentscheidung` VARCHAR(50) default 'n.v.',
  `wfmt1_vzg_text_techn_fertig` VARCHAR(69) default 'n.v.',
  `wfmt1_vzg_schluessel_techn_fertig` VARCHAR(10) default 'n.v.',
  `wfmt1_endsz` VARCHAR(27) default 'n.v.',
  `wfmt1_kombireferenznr` VARCHAR(15) default 'n.v.',
  `vlt` datetime default null,
  `vrt` datetime default null,
  `vrd` decimal(12,2) DEFAULT NULL,
  `vlw` VARCHAR(7) default 'n.v.',
  `mhl_stufe` VARCHAR(4) default 'n.v.',
  `auftraggeberprioritaet` VARCHAR(4) default 'n.v.',
  `lsz_zusatz` VARCHAR(4) default 'n.v.',
  `standortkennung` VARCHAR(52) default 'n.v.',
  `node_id` VARCHAR(10) default 'n.v.',
  `hbue_termin` datetime default null,
  `standortname` VARCHAR(255) default 'n.v.',
  `konfiguration` VARCHAR(50) default 'n.v.',
  `accessgroupbundle` varchar(1) default null,
  `accessgrouptype` varchar(10) DEFAULT 'n.v.',
  `bandbreite_service` varchar(255) DEFAULT 'n.v.',
  `anschluss_id` varchar(10) DEFAULT 'n.v.',
  `kls_id_a` bigint unsigned,
  `kls_id_b` bigint unsigned,
  `abschl_freig_status` smallint(3) DEFAULT -1,
  `abschl_freig_erford` tinyint(1) default 0,
  PRIMARY KEY (`wmsti_requidbafa`),
  KEY `wmsti_psl_001_wmsti_bafaaenddatum_index` (`wmsti_bafaaenddatum`),
  KEY `wmsti_psl_001_wmsti_statusbafa` (`wmsti_statusbafa`),
  KEY `wmsti_psl_001_wmsti_master_ba` (`wmsti_master_ba`),
  KEY `wmsti_psl_001_wmsti_refidba` (`wmsti_refidba`),
  KEY `wmsti_psl_001_wmsti_smauftrnr` (`wmsti_smauftrnr`),
  KEY `wmsti_psl_001_wmsti_smauftrnr_manuell` (`wmsti_smauftrnr_manuell`),
  KEY `wmsti_auftrnrag` (`wmsti_auftrnrag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS `wfmt_bemerkung` (
    `wmsti_requidbafa` varchar(15) NOT NULL,
    `wmsti_bafaaenddatum` datetime NOT NULL,
    `wfmt1_bemerkung_vorsystem` LONGTEXT,
    `wfmt1_bemerkung` LONGTEXT,

    PRIMARY KEY (`wmsti_requidbafa`),
    KEY `wmsti_bafaaenddatum` (`wmsti_bafaaenddatum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- Create syntax for TABLE 'psl_geschaeftsfall'
CREATE TABLE IF NOT EXISTS `psl_geschaeftsfall` (
  `aenderungsdatum` datetime NOT NULL,
  `sm_auftragsnummer` bigint(12) unsigned zerofill NOT NULL,
  `durchf_produktives_ressort_id` int(11) unsigned NOT NULL,
  `durchf_produktives_ressort_text_mittel` varchar(40) NOT NULL,
  `plangruppe` char(6) NOT NULL,
  `plangruppenzaehler` char(2) NOT NULL,
  `geschaeftsfall_menge_beauftragt` float NOT NULL DEFAULT '0',
  `geschaeftsfall_menge_geplant` float NOT NULL DEFAULT '0',
  `geschaeftsfall_menge_ist` float NOT NULL DEFAULT '0',
  PRIMARY KEY (`sm_auftragsnummer`,`durchf_produktives_ressort_id`,`plangruppe`,`plangruppenzaehler`),
  KEY `psl_geschaeftsfall_sm_auftragsnummer` (`sm_auftragsnummer`),
  KEY `aenderungsdatum` (`aenderungsdatum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'psl_meldungen'
CREATE TABLE IF NOT EXISTS `psl_meldungen` (
  `sma` bigint(12) unsigned zerofill NOT NULL,
  `aend_ts` datetime NOT NULL,
  `meldungsart` varchar(10) NOT NULL,
  `codierung` varchar(10) NOT NULL,
  `erf_ts` datetime NOT NULL,
  `meld_ts` datetime NOT NULL,
  `abschl_ts` datetime NOT NULL,
  `meldungsnummer` char(12) NOT NULL DEFAULT '',
  PRIMARY KEY (`meldungsnummer`),
  KEY `erf_ts` (`erf_ts`),
  KEY `abschl_ts` (`abschl_ts`),
  KEY `meldungsnummer` (`meldungsnummer`),
  KEY `meld_ts` (`meld_ts`),
  KEY `sma` (`sma`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='WMSTI PSL Meldungen';

-- Create syntax for TABLE 'sm_auftrag_psl_allgemein'
CREATE TABLE IF NOT EXISTS `sm_auftrag_psl_allgemein` (
  `region_niederlassung_text_mittel` varchar(40) NOT NULL,
  `sm_auftragsnummer` bigint(12) unsigned zerofill NOT NULL,
  `auftrag_quellsystem` varchar(40) NOT NULL,
  `psp_element` varchar(40) NOT NULL,
  `psp_element_text` varchar(100) NOT NULL,
  `pm_ps_bezugselement` varchar(40) NOT NULL,
  `benefit_type` char(2) NOT NULL,
  `decision_unit` char(2) NOT NULL,
  `decision_package` char(2) NOT NULL,
  `systemstatus` char(4) NOT NULL,
  `systemstatus_historie` varchar(50) NOT NULL,
  `planergruppe` varchar(10) NOT NULL,
  `ih_leistungsart` varchar(10) NOT NULL,
  `verant_arbeitsplatz_text` varchar(40) NOT NULL,
  `ih_leistungsart_text` varchar(100) NOT NULL,
  `technischer_platz_nr` varchar(30) NOT NULL,
  `technischer_platz_text` varchar(100) NOT NULL,
  `fakturaverzicht` varchar(10) NOT NULL,
  `planstarttermin` datetime NOT NULL,
  `planendtermin` datetime NOT NULL,
  `iststarttermin` datetime NOT NULL,
  `erfassungsdatum` datetime NOT NULL,
  `aenderungsdatum` datetime NOT NULL,
  `datum_frei` datetime NOT NULL,
  `technabschluess` datetime NOT NULL,
  `auftragsart` char(12) NOT NULL,
  `auftrags_beschreibung` varchar(100) NOT NULL,
  `t_ladedatum` datetime NOT NULL,
  PRIMARY KEY (`sm_auftragsnummer`),
  KEY `technischer_platz_nr` (`technischer_platz_nr`),
  KEY `t_ladedatum` (`t_ladedatum`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `sm_auftrag_psl_finanzen` (
  `sm_auftragsnummer` bigint(12) unsigned zerofill NOT NULL,
  `budgetart` tinyint(2) unsigned zerofill NOT NULL,
  `planwert` decimal(12,2) NOT NULL,
  `ist_co_beleg` decimal(12,2) NOT NULL,
  `obligo` decimal(12,2) NOT NULL,
  `verfuegt` decimal(12,2) NOT NULL,
  `t_ladedatum` datetime NOT NULL,
  PRIMARY KEY (`sm_auftragsnummer`,`budgetart`,`t_ladedatum`),
  KEY `sm_auftrag_psl_finanzen_sma` (`sm_auftragsnummer`),
  KEY `budgetart` (`budgetart`),
  KEY `t_ladedatum` (`t_ladedatum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'statusergaenzungen'
CREATE TABLE IF NOT EXISTS `statusergaenzungen` (
  `ref_bafa_requestid` char(15) NOT NULL,
  `se_kenner` char(15) NOT NULL,
  `se_bezeichnung` varchar(15) NOT NULL,
  `se_zeitstempel` datetime NOT NULL,
  `se_reihenfolge` int(15) NOT NULL,
  `del_flag` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`ref_bafa_requestid`,`se_kenner`),
  KEY `ref_bafa_requestid` (`ref_bafa_requestid`),
  KEY `se_bezeichnung` (`se_bezeichnung`),
  KEY `se_kenner` (`se_kenner`),
  KEY `se_zeitstempel` (`se_zeitstempel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Alle Statusergänzugnen';

-- Create syntax for TABLE 'linientechnische_veraenderung'
CREATE TABLE IF NOT EXISTS `linientechnische_veraenderung` (
  `sma` bigint(12) unsigned zerofill NOT NULL,
  `technischer_abschluss_tag` datetime DEFAULT NULL,
  PRIMARY KEY (`sma`),
  KEY `technischer_abschluss_tag` (`technischer_abschluss_tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'workflow_auftrag_zeitpunkte'
CREATE TABLE IF NOT EXISTS `workflow_auftrag_zeitpunkte` (
  `twf_auftrag_id` char(15) NOT NULL,
  `twf_produktion_abgeschlossen` datetime NOT NULL,
  `twf_auftragende` datetime NOT NULL,
  `twf_1_sollterminverschiebung` datetime NOT NULL,
  `twf_aktuelle_sollterminverschiebung` datetime NOT NULL,
  `twf_produktion_steht_seit` datetime NOT NULL,
  `twf_datenstand` datetime NOT NULL,
  PRIMARY KEY (`twf_auftrag_id`),
  KEY `workflow_auftrag_zeitpunkte_twf_datenstand` (`twf_datenstand`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `psl_koa_plan_ist` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `plan` decimal(12,2) NOT NULL,
  `ist` decimal(12,2) NOT NULL,
  `t_ladedatum` datetime NOT NULL,
  `kostenart` int(11) NOT NULL DEFAULT '0',
  `psl_ladedatum` datetime,
  `csbi_ladedatum` datetime NOT NULL,
  PRIMARY KEY (`sma_psl`,`kostenart`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `psl_koa_obligo` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `obligo` decimal(12,2) NOT NULL,
  `kostenart` int(11) DEFAULT NULL,
  `tab` tinyint(1) DEFAULT '0',
  KEY `sma_psl` (`sma_psl`,`kostenart`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS  `b2b_zele_fa` (
  `arbeitsauftrags_id` char(15) NOT NULL,
  `ref_ba` char(15) NOT NULL,
  `fs_auftragsnummer` varchar(30) NOT NULL,
  `sm_auftrag` varchar(30) NOT NULL,
  `abrufnummer` varchar(30) NOT NULL,
  `rahmenvertragsnummer` varchar(10) NOT NULL,
  `relevanz_aa` varchar(30) NOT NULL,
  `fs_folgesystem` int(15) NOT NULL DEFAULT 0,
  `zugewiesen_an_nl` varchar(255) NOT NULL,
  `zugewisesen_an_ressort` varchar(50) NOT NULL,
  `status` varchar(128) NOT NULL,
  `storno` tinyint(1) DEFAULT NULL,
  `standard_arbeitsauftrag` varchar(80) NOT NULL,
  `teilauftragsfall_ti` varchar(60) NOT NULL,
  `fs_techn_angek` datetime NOT NULL,
  `ressort_typ` varchar(50) NOT NULL,
  `referenz_nr_tf` varchar(15) NOT NULL,
  `fsfl_vg_verknuepft` tinyint(1) DEFAULT NULL,
  `ist_beginntermin_aa` datetime NOT NULL,
  `ist_ende_termin_aa` datetime NOT NULL,
  `soll_ende_termin_aa` datetime NOT NULL,
  `spaet_sollend` datetime NOT NULL,
  `timestamp_tf` datetime NOT NULL,
  `timestamp_zw` datetime NOT NULL,
  `anderungsdatum` datetime NOT NULL,
  `del_flag` tinyint(1) DEFAULT 0,
  `rahmenvertragsposition` varchar(6) default 'n.v.',
  `rv_positionsname` varchar(255) default 'n.v.',
  `kreditorenname` varchar(255) default 'n.v.',
  `kreditorennummer` varchar(10) default 'n.v.',
  `kopplung_rv_ba` tinyint(1) default 0,
  `soll_ende_aus_fsfa` datetime NOT NULL,
  `auftragskurzbeschreibung` varchar(255) default 'n.v.',
  PRIMARY KEY (`arbeitsauftrags_id`),
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='WFM-T_V4E_VIEW_BEAST_AA';


CREATE TABLE IF NOT EXISTS `bbt_aktivitaeten` (
  `aktivitaets_id` int(9) NOT NULL,
  `bbk_id` int(9) NOT NULL,
  `aktivitaetstyp_id` smallint(5) DEFAULT NULL,
  `datum` datetime NOT NULL,
  `quelle` varchar(50) NULL DEFAULT NULL,
  `aktivitaetstext` text NULL DEFAULT NULL,
  `t_erstellungsdatum` datetime NOT NULL,
  primary key (`aktivitaets_id`),
  KEY `bbk_id` (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `bbt_auftragsdaten` (
  `nlpti` varchar(110) NOT NULL,
  `bbk_id` int(9) NOT NULL,
  `kls_id` bigint(12),
  `kundenanliegen` varchar(50) NOT NULL,
  `termin_steuerung` datetime,
  `termin_geplant` datetime,
  `baureife_ist` datetime,
  `baureife_sol` datetime,
  `kritischer_korb_grund` varchar(100),
  `uebergabe_kritisch_zeitpunkt` datetime,
  `workflow_version_mit_baureife` tinyint(1) DEFAULT '0',
  `buchbarkeit_hergestellt_ist` datetime,
  `zeitpunkt_storno_kunde` datetime,
  `zeitpunkt_storno_manuell` datetime,
  `zeitpunkt_storno_wmsti` datetime,
  `zeitpunkt_storno_nichtausbau` datetime,
  `kontakt_status_id` varchar(50) NOT NULL,
  `kontakt_status_value` varchar(50) NOT NULL,
  `auftragsentwurf_ist` datetime,
  `auftragserfassung_ist` datetime,
  `unterlagen_versandt_ist` datetime,
  `unterlagen_gescannt_ist` datetime,
  `unterlagen_validiert_ist` datetime,
  `technik_beauftragt_ist` datetime,
  `projektiert_ist` datetime,
  `tiefbau_beauftragt_ist` datetime,
  `apl_montiert_ist` datetime,
  `gf_netzanbindung_herg_ist` datetime,
  `produkt_gebucht_ist` datetime,
  `fakturierung_erfolgt_ist` datetime,
  `eingangskanal` varchar(60),
  `termin_wmsti` datetime,
  `t_prod_bedanz_jn` tinyint(1) DEFAULT '0',
  `anzahl_we` int(10),
  `anzahl_ge` int(10),
  `anzahl_sonderleitungen` int(10),
  `apl_innen_jn` tinyint(1) DEFAULT '0',
  `wiederbebauung_id` tinyint(1) DEFAULT '0',
  `baumassnahme` varchar(30),
  `baumassnahme_detail` varchar(50),
  `ks_team` varchar(40),
  `gebietskenner_id` varchar(10),
  `ne4_auskundung_id` tinyint(1) DEFAULT '0',
  `koordinierung_energieversorger` tinyint(1) DEFAULT '0',
  `termin_koordination` datetime,
  `wmsti_id` varchar(15),
  `baustelle_plz` varchar(100),
  `baustelle_ort` varchar(100),
  `baustelle_strasse` varchar(100),
  `baustelle_hausnr` varchar(100),
  `baustelle_hausnr_zus` varchar(10),
  `tiefbauunternehmen` varchar(200),
  `T_ERSTELLUNGSDATUM` datetime NOT NULL,
  primary key (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `bzm_light_mengen` (
  `id` int(11) NOT NULL,
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `kreditor` varchar(10) NOT NULL,
  `gewerk` varchar(100) NOT NULL,
  `art` int(11) NOT NULL,
  `menge` int(11) NOT NULL ,
  `zeitstempel` datetime NOT NULL,
  `letze_erfassung` tinyint(1) DEFAULT NULL,
  primary key (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `bzm_light_termine` (
  `id` int(11) NOT NULL,
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `kreditor` varchar(10) NOT NULL,
  `gewerk` varchar(100) NOT NULL,
  `beginn` datetime NOT NULL,
  `ende` datetime NOT NULL,
  `zeitstempel` datetime NOT NULL,
  primary key (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `wfmt_betr_korr` (
  `requidbafa` varchar(15) NOT NULL,
  `hist_betriebsber_korr` DATETIME NULL,
  `aenderungsdatum` DATETIME NOT NULL,
  primary key (`requidbafa`,`aenderungsdatum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `wfmt_betr_korr_del` (
  `requidbafa` varchar(15) NOT NULL,
  `hist_betriebsber_korr` DATETIME NULL,
  `aenderungsdatum` DATETIME NOT NULL,
  `geloescht_am` DATETIME NOT NULL,
  `regel` varchar(50) NOT NULL,
  primary key (`requidbafa`,`aenderungsdatum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `wfmt_vzg_hist` (
    `requidbafa` varchar(15) NOT NULL,
    `verzoegerungsgrund`varchar(69),
    `erstelldatum` DATETIME,
    `vzg_art` varchar(13),
    `vzg_schluessel` varchar(10),
    `vzg_ressort` varchar(30),
    `bemerkung` varchar(1000),
    `aenderungsdatum` DATETIME NOT NULL,
    `fa_requid` varchar(15),
  primary key (`requidbafa`,`aenderungsdatum`,`vzg_art`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `kollo_offene_auftraege` (
    `phase` varchar(255),
    `status` varchar(255),
    `t_iaa` varchar(255),
    `kollo_auftragsnr` varchar(255),
    `produktklassen` varchar(255),
    `pti_soll` datetime,
    `pti_ist` datetime,
    `stoe_eingang` datetime,
    `verzoeg_grund` varchar(255),
    `beteiligung` varchar(255),
    `wmsti_nr_angebot` varchar(255),
    `wmsti_nr_auftrag` varchar(255),
    `sma_uetech` varchar(255),
    `sma_ltech` varchar(255),
    primary key (`kollo_auftragsnr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `wfmt_ps` (
    `arbeitsauftrags_id` varchar(15) NOT NULL,
    `ref_ba` varchar(15),
    `relevanz_aa` varchar(30) NOT NULL,
    `status` varchar(128) NOT NULL,
    `storno` int(15),
    `standard_arbeitsauftrag` varchar(80),
    `anderungsdatum` datetime NOT NULL,
    `del_flag` tinyint(1) DEFAULT 0,
    PRIMARY KEY (`arbeitsauftrags_id`),
    KEY `del_flag` (`del_flag`),
    KEY `ref_ba` (`ref_ba`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `trommelbeweg` (
  `t_ladedatum` datetime NOT NULL,
  `matbeleg_nr` varchar(10) NOT NULL,
  `matbeleg_jahr` smallint(2) unsigned NOT NULL,
  `matbeleg_pos` smallint(2)  NOT NULL,
  `bw_zeilen_id` mediumint(9) NOT NULL,
  `best_material` VARCHAR(18) NOT NULL,
  `chargen_nr` VARCHAR(10) NOT NULL,
  `werk` CHAR(4) NOT NULL,
  `bewegungsart` CHAR(3) NOT NULL,
  `menge` decimal(22) NOT NULL,
  `buchungsdatum` datetime,
  `auftrags_nr` BIGINT(12),
  PRIMARY KEY (`best_material`,`chargen_nr`,`matbeleg_nr`,`matbeleg_jahr`,`matbeleg_pos`,`bw_zeilen_id`),
  KEY `trommelbeweg_index` (`best_material`,`chargen_nr`,`matbeleg_nr`,`matbeleg_jahr`,`matbeleg_pos`,`bw_zeilen_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `trom_matcharge` (
    `t_ladedatum` datetime NOT NULL,
    `material_nr` VARCHAR(18) NOT NULL,
    `werk` VARCHAR(4) NOT NULL,
    `chargen_nr` VARCHAR(10) NOT NULL,
    `status_verlust` char(1) NOT NULL,
    `datum_verlust` datetime NOT NULL,
    `loeschvormerkung_charge_werk` char(1),
    `dat_letzt_wareneingang` datetime,
    `dat_erst_wareneing_trom` datetime,
    `dat_letzt_warenausg_trom` datetime,
    `belegnr_letzt_beweg_trom` varchar(10),
    `belegnr_letzt_wareneingang` varchar(10),
    `dat_letzt_wareneing_trom` datetime,
    `belegnr_letzt_warenausg` varchar(10),
    `dat_letzt_aenderung` datetime,
    `t_gueltig_von` datetime,
    `t_gueltig_bis` datetime,
    PRIMARY KEY (`t_gueltig_von`,`material_nr`,`werk`,`chargen_nr`),
    KEY `matcharge_index` (`t_gueltig_von`, `material_nr`, `werk`, `chargen_nr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `gbgs_cio` (
      `ba_nummer` int unsigned,
      `auftrag_info_supplier` varchar(15),
      `naechster_schritt` varchar(125),
      `neubaulokation` varchar(5),
      `nbg_erfassungsstatus` varchar(15),
      `ba_status` varchar(25),
      `zu_bauen_bis` DATETIME,
      `wunschtermin` DATETIME,
      `best_nummer` varchar(25) NOT NULL,
      `erstell_datum_bestellung` DATETIME NOT NULL,
      `best_status` varchar(20),
      `statusgrund` varchar(80),
      `rollout_partn_initial` varchar(35),
      `rollout_partn_lokal` varchar(35),
      `rollout_partn_regel` varchar(35),
      `mandant` int unsigned,
      `kls_id` bigint unsigned NOT NULL,
      `fol_id` bigint unsigned,
      `gebauede_typ` varchar(20),
      `we` int unsigned NOT NULL,
      `ge` int unsigned NOT NULL,
      `zb` int unsigned NOT NULL,
      `gf_id` varchar(10),
      `erstelldatum` datetime,
      `aenderungsdatum` datetime,
      `auftrags_typ` varchar(20) NOT NULL,
      `area_nr` varchar(50) NOT NULL,
      `area_name` varchar(150),
      `area_status` varchar(20) NOT NULL,
      `ausbau_status` varchar(15) NOT NULL,
      `eigent_zust_status` varchar(10),
      `zust_status_ne3` varchar(10),
      `zust_status_ne4` varchar(10),
      `grund_nichtzust` varchar(40),
      `onkz` int unsigned,
      `asb` int unsigned,
      `nvt` varchar(10),
      `plz` varchar(5),
      `ort` varchar(50),
      `strasse` varchar(50),
      `hausnr` varchar(10),
      `hausnr_zusatz` varchar(10),
      `terminbuchung_status` varchar(25),
      `start_inst` datetime,
      `ende_inst` datetime,
      `wo_id_gf_ta` int unsigned,
      `wo_status_gf_ta` varchar(15),
      `bau_gf_ta_umgesetzt` datetime,
      `ba_supplier_partyid` bigint unsigned,
      `supplier_bauauftrag` varchar(82),
      `phase_bauauftrag` varchar(20),
      `nvt_prog_status_ba` varchar(30),
      `wartegrund_ba` varchar(35),
      `wv_datum_ba` datetime,
      `erw_loesung_am` datetime,
      `wartegrund_ba_supplier` varchar(100),
      `warteg_ba_supplier_partyid` bigint unsigned,
      `auskund_erfordl` varchar(5),
      `auskund_erledigt` varchar(5),
      `terminstatus_auskund` varchar(25),
      `wo_id_auskundung` int unsigned,
      `wo_status_auskund` varchar(15),
      `ausk_plan_start` datetime,
      `ausk_plan_ende` datetime,
      `ausk_umgesetzt` datetime,
      `ausk_supplier_partyid` bigint unsigned,
      `supplier_auskundung` varchar(82),
      `phase_ausk` varchar(20),
      `gf_ap` varchar(45),
      `inventarstatus_gf_ap` varchar(10),
      `inst_status_bau_gf_ap` varchar(10),
      `status_bau_gf_ap` varchar(25),
      `wo_id_gf_ap` int unsigned,
      `wo_status_gf_ap` varchar(15),
      `bau_gf_ap_plan_start` datetime,
      `bau_gf_ap_plan_ende` datetime,
      `umgesetzt_bau_ap` datetime,
      `gfap_supplier_partyid` bigint unsigned,
      `gf_ap_supplier` varchar(100),
      `phase_gf_ap` varchar(20),
      `wartegrund_gebaeude` varchar(55),
      `wv_datum_gebaeude` datetime,
      `erw_loesung_gebaeude` datetime,
      `wartegrund_gebaeude_supplier` varchar(100),
      `warteg_gebaeude_supplier_partyid` bigint unsigned,
      `tsg_id_ausk` int unsigned,
      `tsg_id_gfap` int unsigned,
      `tsg_id_dose` int unsigned,
      `passiv_provid` int unsigned,
      `partyid_kunde` bigint unsigned,
      `carrier_name` varchar(60),
      `cio_quelle` varchar(10),
      `terminbu_bauauftrag` varchar(10),
      `terminbu_gf_ap` varchar(10),
      `terminbu_auskund` varchar(10),
      `stornierungsgrund` varchar(80),
      `bppd_id` bigint unsigned,
      `rufnr_supplier_bau` varchar (40),
      `mail_supplier_bau` varchar (60),
      `rufnr_supplier_ausk` varchar (40),
      `mail_supplier_ausk` varchar (60),
      `rufnr_supplier_gfap` varchar (40),
      `mail_supplier_gfap` varchar (60),
      `tarifbest` varchar(5),
      `aenddatum_bestellung` DATETIME,
      `has_rule_violation` varchar(1),
      `ba_nr_intern` bigint unsigned NOT NULL,
      `bedarfspunktname` varchar(150),
      `bedarfspunkt_id` varchar(150),
      `erst_wg_bauauftrag` datetime,
      `update_wg_bauauftrag` datetime,
      `erst_wg_gebauede` datetime,
      `update_wg_gebauede` datetime,
      `t_ladedatum` datetime,
      `quelle` varchar(4),
      key `ba_nummer` (`ba_nummer`),
      key `fol_id` (`fol_id`),
      key `erstell_datum_bestellung` (`erstell_datum_bestellung`),
      key `best_nummer` (`best_nummer`),
      key `kombi_index` (`erstell_datum_bestellung`, `best_nummer`),
      key `kombi_index_komplett` (`ba_nummer`, `erstell_datum_bestellung`, `best_nummer`),
      key `t_ladedatum` (t_ladedatum),
      key `ba_nr_intern` (`ba_nr_intern`),
      primary key (`ba_nr_intern`, `quelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `ggs_cio_multi` (
      `ba_nummer` int unsigned,
      `auftrag_info_supplier` varchar(15),
      `naechster_schritt` varchar(125),
      `neubaulokation` varchar(5),
      `nbg_erfassungsstatus` varchar(15),
      `ba_status` varchar(25),
      `zu_bauen_bis` DATETIME,
      `wunschtermin` DATETIME,
      `best_nummer` varchar(25) NOT NULL,
      `erstell_datum_bestellung` DATETIME NOT NULL,
      `best_status` varchar(20),
      `statusgrund` varchar(80),
      `rollout_partn_initial` varchar(35),
      `rollout_partn_lokal` varchar(35),
      `rollout_partn_regel` varchar(35),
      `mandant` int unsigned,
      `kls_id` bigint unsigned NOT NULL,
      `fol_id` bigint unsigned,
      `gebauede_typ` varchar(20),
      `we` int unsigned NOT NULL,
      `ge` int unsigned NOT NULL,
      `zb` int unsigned NOT NULL,
      `gf_id` varchar(10),
      `erstelldatum` datetime,
      `aenderungsdatum` datetime,
      `auftrags_typ` varchar(20) NOT NULL,
      `area_nr` varchar(50) NOT NULL,
      `area_name` varchar(150),
      `area_status` varchar(20) NOT NULL,
      `ausbau_status` varchar(15) NOT NULL,
      `eigent_zust_status` varchar(10),
      `zust_status_ne3` varchar(10),
      `zust_status_ne4` varchar(10),
      `grund_nichtzust` varchar(40),
      `onkz` int unsigned,
      `asb` int unsigned,
      `nvt` varchar(10),
      `plz` varchar(5),
      `ort` varchar(50),
      `strasse` varchar(50),
      `hausnr` varchar(10),
      `hausnr_zusatz` varchar(10),
      `terminbuchung_status` varchar(25),
      `start_inst` datetime,
      `ende_inst` datetime,
      `wo_id_gf_ta` int unsigned,
      `wo_status_gf_ta` varchar(15),
      `bau_gf_ta_umgesetzt` datetime,
      `ba_supplier_partyid` bigint unsigned,
      `supplier_bauauftrag` varchar(82),
      `phase_bauauftrag` varchar(20),
      `nvt_prog_status_ba` varchar(30),
      `wartegrund_ba` varchar(35),
      `wv_datum_ba` datetime,
      `erw_loesung_am` datetime,
      `wartegrund_ba_supplier` varchar(100),
      `warteg_ba_supplier_partyid` bigint unsigned,
      `auskund_erfordl` varchar(5),
      `auskund_erledigt` varchar(5),
      `terminstatus_auskund` varchar(25),
      `wo_id_auskundung` int unsigned,
      `wo_status_auskund` varchar(15),
      `ausk_plan_start` datetime,
      `ausk_plan_ende` datetime,
      `ausk_umgesetzt` datetime,
      `ausk_supplier_partyid` bigint unsigned,
      `supplier_auskundung` varchar(82),
      `phase_ausk` varchar(20),
      `gf_ap` varchar(45),
      `inventarstatus_gf_ap` varchar(10),
      `inst_status_bau_gf_ap` varchar(10),
      `status_bau_gf_ap` varchar(25),
      `wo_id_gf_ap` int unsigned,
      `wo_status_gf_ap` varchar(15),
      `bau_gf_ap_plan_start` datetime,
      `bau_gf_ap_plan_ende` datetime,
      `umgesetzt_bau_ap` datetime,
      `gfap_supplier_partyid` bigint unsigned,
      `gf_ap_supplier` varchar(100),
      `phase_gf_ap` varchar(20),
      `wartegrund_gebaeude` varchar(55),
      `wv_datum_gebaeude` datetime,
      `erw_loesung_gebaeude` datetime,
      `wartegrund_gebaeude_supplier` varchar(100),
      `warteg_gebaeude_supplier_partyid` bigint unsigned,
      `tsg_id_ausk` int unsigned,
      `tsg_id_gfap` int unsigned,
      `tsg_id_dose` int unsigned,
      `passiv_provid` int unsigned,
      `partyid_kunde` bigint unsigned,
      `carrier_name` varchar(60),
      `cio_quelle` varchar(10),
      `terminbu_bauauftrag` varchar(10),
      `terminbu_gf_ap` varchar(10),
      `terminbu_auskund` varchar(10),
      `stornierungsgrund` varchar(80),
      `bppd_id` bigint unsigned,
      `rufnr_supplier_bau` varchar (40),
      `mail_supplier_bau` varchar (60),
      `rufnr_supplier_ausk` varchar (40),
      `mail_supplier_ausk` varchar (60),
      `rufnr_supplier_gfap` varchar (40),
      `mail_supplier_gfap` varchar (60),
      `tarifbest` varchar(5),
      `aenddatum_bestellung` DATETIME,
      `has_rule_violation` varchar(1),
      `ba_nr_intern` bigint unsigned NOT NULL,
      `bedarfspunktname` varchar(150),
      `bedarfspunkt_id` varchar(150),
      `erst_wg_bauauftrag` datetime,
      `update_wg_bauauftrag` datetime,
      `erst_wg_gebauede` datetime,
      `update_wg_gebauede` datetime,
      `t_ladedatum` datetime,
      `quelle` varchar(4),
      #key `ba_nummer` (`ba_nummer`),
      #key `fol_id` (`fol_id`),
      #key `erstell_datum_bestellung` (`erstell_datum_bestellung`),
      #key `best_nummer` (`best_nummer`),
      #key `kombi_index` (`erstell_datum_bestellung`, `best_nummer`),
      #key `kombi_index_komplett` (`ba_nummer`, `erstell_datum_bestellung`, `best_nummer`),
      #key `t_ladedatum` (t_ladedatum),
      key `ba_nr_intern` (`ba_nr_intern`),
      primary key (`ba_nr_intern`, `quelle`, `t_ladedatum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_dtag_bulk
(
    `kls_id`                      bigint(12) unsigned NOT NULL,
    `fol_id`                      bigint(12) unsigned NOT NULL,
    `prod_modell`                 varchar(5)   not null,
    `area_nr`                     varchar(50),
    `area_name`                   varchar(150),
    `area_status`                 varchar(20),
    `ausbauentscheid`             datetime,
    `rollout_partn_initial`       varchar(35),
    `rollout_partn_lokal`         varchar(35),
    `rollout_partn_regel`         varchar(35),
    `eigent_zust_status`          varchar(10),
    `grund_nichtzust`             varchar(40),
    `anz_kundenorder_appartm`     int unsigned not null,
    `anz_kundenorder_building`    int unsigned not null,
    `bulk_prefix`                 varchar(5)   not null,
    `proj_name`                   varchar(255),
    `proj_nr`                     int unsigned,
    `plz`                         int unsigned,
    `ort`                         varchar(50),
    `strasse`                     varchar(50),
    `hausnr`                      varchar(10),
    `hausnr_zusatz`               varchar(10),
    `erstelldatum`                datetime     not null,
    `ausbau_status`               varchar(15),
    `phase_ne3_proj`              varchar(20),
    `phase_ne4_proj`              varchar(20),
    `we`                          int unsigned,
    `ge`                          int unsigned,
    `zb`                          int unsigned,
    `onkz`                        int unsigned,
    `asb`                         int unsigned,
    `nvt`                         varchar(10),
    `zu_bauen_bis`                datetime,
    `ne3_supplier`                varchar(100),
    `ne3_kreditor`                bigint unsigned,
    `ne4_supplier`                varchar(100),
    `ne4_kreditor`                bigint unsigned,
    `plandatum_auskundung`        datetime,
    `umgesetzt_auskundung`        datetime,
    `plandatum_gf_ap`             datetime,
    `umgesetzt_gf_ap`             datetime,
    `plandatum_ta`                datetime,
    `umgesetzt_ta`                datetime,
    `plandatum_dpu`               datetime,
    `umgesetzt_dpu`               datetime,
    `technologie`                 varchar(5)   not null,
    `ausbauart`                   varchar(15)  not null,
    `4fs_premium`                 varchar(5),
    `gf_ap`                       varchar(45),
    `invernturstatus_gf_ap`       varchar(10),
    `anz_as`                      int unsigned,
    `ta_gebaut`                   int unsigned,
    `ta_noch_nicht_gebaut`        int unsigned,
    `ta_in_bau`                   int unsigned,
    `ta_canceled`                 int unsigned,
    `status_bulk_proj`            varchar(15)  not null,
    `status_adresse`              varchar(15)  not null,
    `next_step`                   varchar(200),
    `fehler_nachricht`            text,
    `bulk_order_id`               int unsigned not null,
    `vorheriger_ne3_supplier`     varchar(100),
    `vorheriger_ne3_kreditor`     bigint unsigned,
    `vorheriger_ne4_kreditor`     bigint unsigned,
    `vorheriger_ne4_supplier`     varchar(100),
    `ne3_supplier_am`             datetime,
    `ne4_supplier_am`             datetime,
    `wartegrund_bulk`             varchar(60),
    `wv_datum_bulk`               datetime,
    `erw_loesung_am`              datetime,
    `wg_kommentar`                text,
    `wg_bulk_erstellt`            datetime,
    `eigentümer_id`               bigint unsigned,
    `bulk_ersteller_id`           bigint unsigned,
    `nvt_area_status`             varchar(35),
    `kommentar_bulk`              text,
    `ne4_bauart`                  varchar(60),
    `letztes_aend_datum`          datetime,
    `nvt_area_ne4_status`         varchar(35),
    `bedarfspunktname`            varchar(150),
    `bedarfspunkt_id`             varchar(150),
    `anz_geb_dosen`               int unsigned,
    `anz_dunkle_dosen`            int unsigned,
    `tv_bereitgestellt`           datetime,
    `fester_liefertermin`         varchar(5),
    `tv_bereitg_am`               datetime,
    `angebotsnummer`              varchar(15),
    `marktsegment`                varchar(15),
    `htn_committed`               varchar(5),
    `kundord_pruef`               varchar(5),
    `status_auftpruef`            varchar(40),
    `erste_dose_fertig`           datetime,
    `laufz_kt_dosen`              int unsigned,
    `stornogrund`                 varchar(40),
    `nvt_ausb_beauftragt`         varchar(5),
    `t_ladedatum`                 datetime,
    primary key (`bulk_order_id`),
    key `fol_id` (`fol_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_gfp_rop_bulk
(
    `kls_id`                      bigint(12) unsigned NOT NULL,
    `fol_id`                      bigint(12) unsigned NOT NULL,
    `prod_modell`                 varchar(5)   not null,
    `area_nr`                     varchar(50),
    `area_name`                   varchar(150),
    `area_status`                 varchar(20),
    `ausbauentscheid`             datetime,
    `rollout_partn_initial`       varchar(35),
    `rollout_partn_lokal`         varchar(35),
    `rollout_partn_regel`         varchar(35),
    `eigent_zust_status`          varchar(10),
    `grund_nichtzust`             varchar(40),
    `anz_kundenorder_appartm`     int unsigned not null,
    `anz_kundenorder_building`    int unsigned not null,
    `bulk_prefix`                 varchar(5)   not null,
    `proj_name`                   varchar(255),
    `proj_nr`                     int unsigned,
    `plz`                         int unsigned,
    `ort`                         varchar(50),
    `strasse`                     varchar(50),
    `hausnr`                      varchar(10),
    `hausnr_zusatz`               varchar(10),
    `erstelldatum`                datetime     not null,
    `ausbau_status`               varchar(15),
    `phase_ne3_proj`              varchar(20),
    `phase_ne4_proj`              varchar(20),
    `we`                          int unsigned,
    `ge`                          int unsigned,
    `zb`                          int unsigned,
    `onkz`                        int unsigned,
    `asb`                         int unsigned,
    `nvt`                         varchar(10),
    `zu_bauen_bis`                datetime,
    `ne3_supplier`                varchar(100),
    `ne3_kreditor`                bigint unsigned,
    `ne4_supplier`                varchar(100),
    `ne4_kreditor`                bigint unsigned,
    `plandatum_auskundung`        datetime,
    `umgesetzt_auskundung`        datetime,
    `plandatum_gf_ap`             datetime,
    `umgesetzt_gf_ap`             datetime,
    `plandatum_ta`                datetime,
    `umgesetzt_ta`                datetime,
    `plandatum_dpu`               datetime,
    `umgesetzt_dpu`               datetime,
    `technologie`                 varchar(5)   not null,
    `ausbauart`                   varchar(15)  not null,
    `4fs_premium`                 varchar(5),
    `gf_ap`                       varchar(45),
    `invernturstatus_gf_ap`       varchar(10),
    `anz_as`                      int unsigned,
    `ta_gebaut`                   int unsigned,
    `ta_noch_nicht_gebaut`        int unsigned,
    `ta_in_bau`                   int unsigned,
    `ta_canceled`                 int unsigned,
    `status_bulk_proj`            varchar(15)  not null,
    `status_adresse`              varchar(15)  not null,
    `next_step`                   varchar(200),
    `fehler_nachricht`            text,
    `bulk_order_id`               int unsigned not null,
    `vorheriger_ne3_supplier`     varchar(100),
    `vorheriger_ne3_kreditor`     bigint unsigned,
    `vorheriger_ne4_kreditor`     bigint unsigned,
    `vorheriger_ne4_supplier`     varchar(100),
    `ne3_supplier_am`             datetime,
    `ne4_supplier_am`             datetime,
    `wartegrund_bulk`             varchar(60),
    `wv_datum_bulk`               datetime,
    `erw_loesung_am`              datetime,
    `wg_kommentar`                text,
    `wg_bulk_erstellt`            datetime,
    `eigentümer_id`               bigint unsigned,
    `bulk_ersteller_id`           bigint unsigned,
    `nvt_area_status`             varchar(35),
    `kommentar_bulk`              text,
    `ne4_bauart`                  varchar(60),
    `letztes_aend_datum`          datetime,
    `nvt_area_ne4_status`         varchar(35),
    `bedarfspunktname`            varchar(150),
    `bedarfspunkt_id`             varchar(150),
    `anz_geb_dosen`               int unsigned,
    `anz_dunkle_dosen`            int unsigned,
    `tv_bereitgestellt`           datetime,
    `fester_liefertermin`         varchar(5),
    `tv_bereitg_am`               datetime,
    `angebotsnummer`              varchar(15),
    `marktsegment`                varchar(15),
    `htn_committed`               varchar(5),
    `kundord_pruef`               varchar(5),
    `status_auftpruef`            varchar(40),
    `erste_dose_fertig`           datetime,
    `laufz_kt_dosen`              int unsigned,
    `stornogrund`                 varchar(40),
    `nvt_ausb_beauftragt`         varchar(5),
    `t_ladedatum`                 datetime,
    primary key (`bulk_order_id`),
    key `fol_id` (`fol_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_dtag_fol (
    `area_nr` varchar(50) not null,
    `area_status` varchar(20) not null,
    `area_name` varchar(150),
    `int_expans_decision_date` datetime,
    `tenant_id` int unsigned,
    `initiative` varchar(20) not null,
    `passive_provider` int unsigned,
    `aktive_provider` int unsigned,
    `fol_id` bigint unsigned not null,
    `modification_date` datetime,
    `ge` int unsigned not null,
    `installation_status` varchar(15) not null,
    `reason_for_no_constr` varchar(25),
    `we` int unsigned not null,
    `zb` int unsigned not null,
    `kls_id` bigint unsigned not null,
    `has_rule_violation` varchar(1),
    `is_development_location` varchar(1),
    `build_agree_state` varchar(10),
    `asb` int unsigned,
    `nvt` varchar(10),
    `onkz` int unsigned,
    `expan_period_fine_start` datetime,
    `expan_period_fine_end` datetime,
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `ort` varchar(50),
    `plz` int unsigned,
    `strasse` varchar(50),
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `t_ladedatum` datetime,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelaubau_gestartet` varchar(1),
    `technik` varchar(5) NOT NULL,
    `gfap_kostenpflicht` varchar(1),
    `projekt_id` varchar(50),
    `status_not_planned` datetime,
    `status_passed` datetime,
    `status_passed_plus` datetime,
    `status_prepared` datetime,
    `status_ready` datetime,
    `status_connected` datetime,
    `anz_dosen` int unsigned,
    `z_status_ne3` varchar(10),
    `tag_ne_3_zustimmung` datetime,
    `z_status_ne4` varchar(10),
    `tag_ne_4_zustimmung` datetime,
    `tag_erwartete_zustimmung` datetime,
    `lokation_hinweis` varchar(15),
    `ausgekundet_giga` varchar(1),
    `ist_ausgekundet` varchar(1),
    `ne4_bauart` varchar(55) not null,
    `et_daten_geklaert` varchar(1),
    `et_daten_vorhanden` varchar(1),
    `wartegrund` varchar(35),
    `sponsor` varchar(10),
    `datum_wiedervorlage` datetime,
    primary key (`fol_id`),
    key `kls_id` (`kls_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_gfp_rop_fol (
    `kls_id` bigint unsigned not null,
    `fol_id` bigint unsigned not null,
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `we` int unsigned not null,
    `ge` int unsigned not null,
    `zb` int unsigned not null,
    `area_nr` varchar(50) not null,
    `area_status` varchar(20) not null,
    `area_name` varchar(150),
    `int_expans_decision_date` datetime,
    `plz` int unsigned,
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `initiative` varchar(20) not null,
    `passive_provider` int unsigned,
    `aktive_provider` int unsigned,
    `modification_date` datetime,
    `installation_status` varchar(15) not null,
    `build_agree_state` varchar(10),
    `reason_for_no_constr` varchar(25),
    `has_rule_violation` varchar(1),
    `is_development_location` varchar(1),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt` varchar(10),
    `expan_period_fine_start` datetime,
    `expan_period_fine_end` datetime,
    `tenant_id` int unsigned,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelaubau_gestartet` varchar(1),
    `technik` varchar(5) NOT NULL,
    `gfap_kostenpflicht` varchar(1),
    `projekt_id` varchar(50),
    `status_not_planned` datetime,
    `status_passed` datetime,
    `status_passed_plus` datetime,
    `status_prepared` datetime,
    `status_ready` datetime,
    `status_connected` datetime,
    `anz_dosen` int unsigned,
    `z_status_ne3` varchar(10),
    `tag_ne_3_zustimmung` datetime,
    `z_status_ne4` varchar(10),
    `tag_ne_4_zustimmung` datetime,
    `tag_erwartete_zustimmung` datetime,
    `lokation_hinweis` varchar(15),
    `ausgekundet_giga` varchar(1),
    `ist_ausgekundet` varchar(1),
    `ne4_bauart` varchar(55) not null,
    `et_daten_geklaert` varchar(1),
    `et_daten_vorhanden` varchar(1),
    `wartegrund` varchar(35),
    `sponsor` varchar(10),
    `datum_wiedervorlage` datetime,
    `t_ladedatum` datetime,
    primary key (`fol_id`),
    key `kls_id` (`kls_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_supplier (
    `supplier_name` varchar(100),
    `supplier_partyid` bigint unsigned NOT NULL,
    `kreditor_id` varchar(40),
    `t_ladedatum` datetime,
    primary key (`supplier_partyid`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_dtag_bauaufgabe (
      `auftrag_info_supplier` varchar(15),
      `kls_id`            bigint(12) unsigned NOT NULL,
      `fol_id`            bigint(12) unsigned NOT NULL,
      `produktion`        varchar(5) NOT NULL,
      `gebauede_typ`      varchar(20),
      `auskund_erfordl`   varchar(5),
      `ausbau_status`     varchar(15) NULL DEFAULT NULL,
      `rollout_partn_initial` varchar(35),
      `rollout_partn_lokal` varchar(35),
      `rollout_partn_regel` varchar(35),
      `area_nr` varchar(50) NOT NULL,
      `area_status` varchar(20) NOT NULL,
      `area_name` varchar(150),
      `we` int unsigned NOT NULL,
      `ge` int unsigned NOT NULL,
      `zb` int unsigned NOT NULL,
      `eigent_zust_status` varchar(10),
      `grund_nichtzust` varchar(40),
      `zust_status_ne3` varchar(10),
      `zust_status_ne4` varchar(10),
      `onkz` int unsigned,
      `asb` int unsigned,
      `nvt` varchar(10),
      `ba_geb_stich_status` varchar(20),
      `ba_geb_stich_fertigstellung` datetime,
      `geb_stich_supplier_partyid` bigint unsigned,
      `geb_stich_supplier` varchar(82),
      `phase_geb_stich`   varchar(20),
      `ba_vzk_einbr_status` varchar(20),
      `ba_vzk_einbr_fertigstellung` datetime,
      `vzk_supplier_partyid` bigint unsigned,
      `vzk_supplier` varchar(82),
      `phase_vzk`    varchar(20),
      `ba_nvt_spleiss_stich_status`   varchar(20),
      `ba_nvt_spleiss_fertigstellung` datetime,
      `nvt_supplier_partyid` bigint unsigned,
      `nvt_supplier`  varchar(82),
      `phase_nvt_spleiss` varchar(20),
      `ba_gfap_status`  varchar(20),
      `ba_gfap_fertigstellung` datetime,
      `umgesetzt_bau_ap` datetime,
      `gfap_supplier_partyid` bigint unsigned,
      `gf_ap_supplier` varchar(100),
      `phase_gf_ap` varchar(20),
      `status_bau_gf_ap` varchar(25),
      `wo_id_gf_ap` int unsigned,
      `wo_status_gf_ap` varchar(15),
      `bau_gf_ap_plan_start` datetime,
      `bau_gf_ap_plan_ende` datetime,
      `ba_auskundung_status` varchar(20),
      `ba_auskundung_fertigstellung` datetime,
      `ausk_umgesetzt` datetime,
      `ausk_supplier_partyid` bigint unsigned,
      `supplier_auskundung` varchar(82),
      `phase_ausk` varchar(20),
      `terminstatus_auskund` varchar(25),
      `auskund_erledigt` varchar(5),
      `wo_id_auskundung` int unsigned,
      `wo_status_auskund` varchar(15),
      `ausk_plan_start` datetime,
      `ausk_plan_ende` datetime,
      `ba_steigleitung_status` varchar(20),
      `ba_steigleitung_fertigstellung` datetime,
      `steigleitung_supplier_partyid` bigint unsigned,
      `steigleitung_supplier` varchar(82),
      `phase_steigleitung` varchar(20),
      `plz` varchar(5),
      `ort` varchar(50),
      `strasse` varchar(50),
      `hausnr` varchar(10),
      `hausnr_zusatz` varchar(10),
      `inventarstatus_gf_ap` varchar(10),
      `gf_ap` varchar(45),
      `wartegrund_gebaeude` varchar(55),
      `wv_datum_gebaeude` datetime,
      `erw_loesung_gebaeude` datetime,
      `dispo_fuer` varchar(10),
      `eingang_dispo` datetime,
      `neubaulokation` varchar(5),
      `t_ladedatum` datetime,
      `wfmt_ordernr_ne3_ausbau` varchar(15),
      `wfmt_ordernr_ne4_ausbau` varchar(15),
      `kommentar_wartegrund` text,
      `ba_auskundung_fttb_status` varchar(20),
      `ba_auskundung_fttb_fergigstellung` datetime,
      `ausk_fttb_supplier_partyid` bigint unsigned,
      `supplier_auskundung_fttb` varchar(82),
      `phase_ausk_fttb` varchar(20),
      `ba_weges` varchar(20),
      `ba_weges_fergigstellung` datetime,
      `weges_supplier_partyid` bigint unsigned,
      `supplier_weges` varchar(82),
      `phase_weges` varchar(20),
      `wartegrund_gebaeude_weges` varchar(54),
      `wv_datum_gebaeude_weges` datetime,
      `erw_loesung_gebaeude_weges` datetime,
      `kmr` varchar(20),
      `ba_kmr_fergigstellung` datetime,
      `kmr_supplier_partyid` bigint unsigned,
      `supplier_kmr` varchar(82),
      `phase_kmr` varchar(20),
      `wartegrund_gebaeude_kmr` varchar(54),
      `wv_datum_gebaeude_kmr` datetime,
      `erw_loesung_gebaeude_kmr` datetime,
      `sponsor` varchar(35),
      `bedarfspunktname` varchar(150),
      primary key (fol_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;


CREATE TABLE IF NOT EXISTS ggs_gfp_rop_bauaufgabe (
    `auftrag_info_supplier` varchar(15),
    `kls_id`            bigint(12) unsigned NOT NULL,
    `fol_id`            bigint(12) unsigned NOT NULL,
    `produktion`        varchar(5) NOT NULL,
    `gebauede_typ`      varchar(20),
    `auskund_erfordl`   varchar(5),
    `ausbau_status`     varchar(15) NULL DEFAULT NULL,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `area_nr` varchar(50) NOT NULL,
    `area_status` varchar(20) NOT NULL,
    `area_name` varchar(150),
    `we` int unsigned NOT NULL,
    `ge` int unsigned NOT NULL,
    `zb` int unsigned NOT NULL,
    `eigent_zust_status` varchar(10),
    `grund_nichtzust` varchar(40),
    `zust_status_ne3` varchar(10),
    `zust_status_ne4` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt` varchar(10),
    `ba_geb_stich_status` varchar(20),
    `ba_geb_stich_fertigstellung` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `phase_geb_stich`   varchar(20),
    `ba_vzk_einbr_status` varchar(20),
    `ba_vzk_einbr_fertigstellung` datetime,
    `vzk_supplier_partyid` bigint unsigned,
    `vzk_supplier` varchar(82),
    `phase_vzk`    varchar(20),
    `ba_nvt_spleiss_stich_status`   varchar(20),
    `ba_nvt_spleiss_fertigstellung` datetime,
    `nvt_supplier_partyid` bigint unsigned,
    `nvt_supplier`  varchar(82),
    `phase_nvt_spleiss` varchar(20),
    `ba_gfap_status`  varchar(20),
    `ba_gfap_fertigstellung` datetime,
    `umgesetzt_bau_ap` datetime,
    `gfap_supplier_partyid` bigint unsigned,
    `gf_ap_supplier` varchar(100),
    `phase_gf_ap` varchar(20),
    `status_bau_gf_ap` varchar(25),
    `wo_id_gf_ap` int unsigned,
    `wo_status_gf_ap` varchar(15),
    `bau_gf_ap_plan_start` datetime,
    `bau_gf_ap_plan_ende` datetime,
    `ba_auskundung_status` varchar(20),
    `ba_auskundung_fertigstellung` datetime,
    `ausk_umgesetzt` datetime,
    `ausk_supplier_partyid` bigint unsigned,
    `supplier_auskundung` varchar(82),
    `phase_ausk` varchar(20),
    `terminstatus_auskund` varchar(25),
    `auskund_erledigt` varchar(5),
    `wo_id_auskundung` int unsigned,
    `wo_status_auskund` varchar(15),
    `ausk_plan_start` datetime,
    `ausk_plan_ende` datetime,
    `ba_steigleitung_status` varchar(20),
    `ba_steigleitung_fertigstellung` datetime,
    `steigleitung_supplier_partyid` bigint unsigned,
    `steigleitung_supplier` varchar(82),
    `phase_steigleitung` varchar(20),
    `plz` varchar(5),
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `inventarstatus_gf_ap` varchar(10),
    `gf_ap` varchar(45),
    `wartegrund_gebaeude` varchar(55),
    `wv_datum_gebaeude` datetime,
    `erw_loesung_gebaeude` datetime,
    `dispo_fuer` varchar(10),
    `eingang_dispo` datetime,
    `neubaulokation` varchar(5),
    `t_ladedatum` datetime,
    `wfmt_ordernr_ne3_ausbau` varchar(15),
    `wfmt_ordernr_ne4_ausbau` varchar(15),
    `kommentar_wartegrund` text,
    `ba_auskundung_fttb_status` varchar(20),
    `ba_auskundung_fttb_fergigstellung` datetime,
    `ausk_fttb_supplier_partyid` bigint unsigned,
    `supplier_auskundung_fttb` varchar(82),
    `phase_ausk_fttb` varchar(20),
    `ba_weges` varchar(20),
    `ba_weges_fergigstellung` datetime,
    `weges_supplier_partyid` bigint unsigned,
    `supplier_weges` varchar(82),
    `phase_weges` varchar(20),
    `wartegrund_gebaeude_weges` varchar(54),
    `wv_datum_gebaeude_weges` datetime,
    `erw_loesung_gebaeude_weges` datetime,
    `kmr` varchar(20),
    `ba_kmr_fergigstellung` datetime,
    `kmr_supplier_partyid` bigint unsigned,
    `supplier_kmr` varchar(82),
    `phase_kmr` varchar(20),
    `wartegrund_gebaeude_kmr` varchar(54),
    `wv_datum_gebaeude_kmr` datetime,
    `erw_loesung_gebaeude_kmr` datetime,
    `sponsor` varchar(35),
    `bedarfspunktname` varchar(150),
    primary key (fol_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_hashtag (
    `is_in` varchar(15) NOT NULL,
    `ba_nummer` int unsigned,
    `hashtag` varchar(10) NOT NULL,
    `hashtag_detail` varchar(70),
    `fol_id` bigint unsigned NOT NULL,
    `t_ladedatum` datetime
) ENGINE=InnoDB DEFAULT CHARSET = utf8;


CREATE TABLE IF NOT EXISTS ggs_dtag_nvt_area (
    `area_nr` varchar(50) NOT NULL,
    `area_name` varchar(150),
    `area_status` varchar(25) NOT NULL,
    `nvt_name` varchar(20),
    `nvt` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt_area_prg_status` varchar(35),
    `regel_vvm` varchar(70),
    `regel_at_grob_von` varchar(70),
    `regel_at_grob_bis` varchar(70),
    `regel_nachinst_von` varchar(70),
    `regel_nachinst_bis` varchar(70),
    `regel_rvm_start` varchar(70),
    `beginn_vvm` datetime,
    `ende_vvm` datetime,
    `beginn_ausbau` datetime,
    `ende_ausbau` datetime,
    `ausbau_grob_von` datetime,
    `ausbau_grob_bis` datetime,
    `nachinst_von` datetime,
    `nachinst_bis` datetime,
    `rvm_start` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `nvt_vzk_supplier_partyid` bigint unsigned,
    `nvt_vzk_supplier` varchar(82),
    `trassen_supplier_partyid` bigint unsigned,
    `trassen_supplier` varchar(82),
    `gfap_supplier_partyid` bigint unsigned,
    `gfap_supplier` varchar(82),
    `ne4_ftth_supplier_partyid` bigint unsigned,
    `ne4_ftth_supplier` varchar(82),
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelausb_gestartet` varchar(5),
    `nvt_kls_id` bigint unsigned not null,
    `plz` varchar(5),
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `anz_planned` int unsigned not null,
    `summe_bau` int unsigned not null,
    `anz_passed` int unsigned not null,
    `anz_passed_plus` int unsigned not null,
    `anz_tube_deployed` int unsigned not null,
    `anz_prepared` int unsigned not null,
    `anz_ready` int unsigned not null,
    `anz_connected` int unsigned not null,
    `anz_not_planned` int unsigned not null,
    `area_prog_bid` int unsigned,
    `nvt_area` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (nvt_area)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_gfp_rop_nvt_area (
    `area_nr` varchar(50) NOT NULL,
    `area_name` varchar(150),
    `area_status` varchar(25) NOT NULL,
    `nvt_name` varchar(20),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt` varchar(10),
    `nvt_area_prg_status` varchar(35),
    `regel_vvm` varchar(70),
    `regel_at_grob_von` varchar(70),
    `regel_at_grob_bis` varchar(70),
    `regel_nachinst_von` varchar(70),
    `regel_nachinst_bis` varchar(70),
    `regel_rvm_start` varchar(70),
    `beginn_vvm` datetime,
    `ende_vvm` datetime,
    `beginn_ausbau` datetime,
    `ende_ausbau` datetime,
    `ausbau_grob_von` datetime,
    `ausbau_grob_bis` datetime,
    `nachinst_von` datetime,
    `nachinst_bis` datetime,
    `rvm_start` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `nvt_vzk_supplier_partyid` bigint unsigned,
    `nvt_vzk_supplier` varchar(82),
    `trassen_supplier_partyid` bigint unsigned,
    `trassen_supplier` varchar(82),
    `gfap_supplier_partyid` bigint unsigned,
    `gfap_supplier` varchar(82),
    `ne4_ftth_supplier_partyid` bigint unsigned,
    `ne4_ftth_supplier` varchar(82),
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelausb_gestartet` varchar(5),
    `nvt_kls_id` bigint unsigned not null,
    `plz` varchar(5),
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `anz_planned` int unsigned not null,
    `summe_bau` int unsigned not null,
    `anz_passed` int unsigned not null,
    `anz_passed_plus` int unsigned not null,
    `anz_tube_deployed` int unsigned not null,
    `anz_prepared` int unsigned not null,
    `anz_ready` int unsigned not null,
    `anz_connected` int unsigned not null,
    `anz_not_planned` int unsigned not null,
    `area_prog_bid` int unsigned,
    `nvt_area` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (nvt_area)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `gbgs_gfap` (
    `mandant` int unsigned,
    `nl` varchar(30) NOT NULL,
    `kls_id` bigint unsigned not null,
    `fol_id` bigint(12) unsigned NOT NULL,
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `gebaut_von` varchar(10) NOT NULL,
    `ausbau_status` varchar(15) NOT NULL,
    `regelkonform` varchar(1),
    `technologie` varchar(5) NOT NULL,
    `we` int unsigned NOT NULL,
    `ge` int unsigned NOT NULL,
    `zb` int unsigned NOT NULL,
    `summe` int unsigned NOT NULL,
    `gfap_zuklein` varchar(1),
    `anz_ang_dosen` int unsigned NOT NULL,
    `dosenvergleich` varchar(30) NOT NULL,
    `gfap_bezeichner` varchar(15),
    `material` varchar(40),
    `materialbez` varchar(45),
    `material_nr` varchar(30),
    `min` int unsigned default 0,
    `max` int unsigned default 0,
    `inventurstatus_gf_ap` Varchar(10),
    `supplier_partyid` bigint unsigned,
    `supplier` varchar(82),
    `gebaut_am` datetime,
    `bau_monat` varchar(7),
    `kw` int unsigned,
    `area_nr` varchar(50) NOT NULL,
    `area_name` varchar(150),
    `onkz` int unsigned,
    `asb` int unsigned,
    `plz` int unsigned,
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(5),
    `hausnr_zusatz` varchar(10),
    `soll_umbau` varchar(5),
    `umbau_material_nr` varchar(30),
    `umbau_materialbez` varchar(45),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_dtag_cio` (
      `ba_nummer` int unsigned,
      `auftrag_info_supplier` varchar(15),
      `naechster_schritt` varchar(125),
      `neubaulokation` varchar(5),
      `nbg_erfassungsstatus` varchar(15),
      `ba_status` varchar(25),
      `zu_bauen_bis` DATETIME,
      `wunschtermin` DATETIME,
      `best_nummer` varchar(25) NOT NULL,
      `erstell_datum_bestellung` DATETIME NOT NULL,
      `best_status` varchar(20),
      `statusgrund` varchar(80),
      `rollout_partn_initial` varchar(35),
      `rollout_partn_lokal` varchar(35),
      `rollout_partn_regel` varchar(35),
      `mandant` int unsigned,
      `kls_id` bigint unsigned NOT NULL,
      `fol_id` bigint unsigned,
      `gebauede_typ` varchar(20),
      `we` int unsigned NOT NULL,
      `ge` int unsigned NOT NULL,
      `zb` int unsigned NOT NULL,
      `gf_id` varchar(10),
      `erstelldatum` datetime,
      `aenderungsdatum` datetime,
      `auftrags_typ` varchar(20) NOT NULL,
      `area_nr` varchar(50) NOT NULL,
      `area_name` varchar(150),
      `area_status` varchar(20) NOT NULL,
      `ausbau_status` varchar(15) NOT NULL,
      `eigent_zust_status` varchar(10),
      `zust_status_ne3` varchar(10),
      `zust_status_ne4` varchar(10),
      `grund_nichtzust` varchar(40),
      `onkz` int unsigned,
      `asb` int unsigned,
      `nvt` varchar(10),
      `plz` varchar(5),
      `ort` varchar(50),
      `strasse` varchar(50),
      `hausnr` varchar(10),
      `hausnr_zusatz` varchar(10),
      `terminbuchung_status` varchar(25),
      `start_inst` datetime,
      `ende_inst` datetime,
      `wo_id_gf_ta` int unsigned,
      `wo_status_gf_ta` varchar(15),
      `bau_gf_ta_umgesetzt` datetime,
      `ba_supplier_partyid` bigint unsigned,
      `supplier_bauauftrag` varchar(82),
      `phase_bauauftrag` varchar(20),
      `nvt_prog_status_ba` varchar(30),
      `wartegrund_ba` varchar(35),
      `wv_datum_ba` datetime,
      `erw_loesung_am` datetime,
      `wartegrund_ba_supplier` varchar(100),
      `warteg_ba_supplier_partyid` bigint unsigned,
      `auskund_erfordl` varchar(5),
      `auskund_erledigt` varchar(5),
      `terminstatus_auskund` varchar(25),
      `wo_id_auskundung` int unsigned,
      `wo_status_auskund` varchar(15),
      `ausk_plan_start` datetime,
      `ausk_plan_ende` datetime,
      `ausk_umgesetzt` datetime,
      `ausk_supplier_partyid` bigint unsigned,
      `supplier_auskundung` varchar(82),
      `phase_ausk` varchar(20),
      `gf_ap` varchar(45),
      `inventarstatus_gf_ap` varchar(10),
      `inst_status_bau_gf_ap` varchar(10),
      `status_bau_gf_ap` varchar(25),
      `wo_id_gf_ap` int unsigned,
      `wo_status_gf_ap` varchar(15),
      `bau_gf_ap_plan_start` datetime,
      `bau_gf_ap_plan_ende` datetime,
      `umgesetzt_bau_ap` datetime,
      `gfap_supplier_partyid` bigint unsigned,
      `gf_ap_supplier` varchar(100),
      `phase_gf_ap` varchar(20),
      `wartegrund_gebaeude` varchar(55),
      `wv_datum_gebaeude` datetime,
      `erw_loesung_gebaeude` datetime,
      `wartegrund_gebaeude_supplier` varchar(100),
      `warteg_gebaeude_supplier_partyid` bigint unsigned,
      `tsg_id_ausk` int unsigned,
      `tsg_id_gfap` int unsigned,
      `tsg_id_dose` int unsigned,
      `passiv_provid` int unsigned,
      `partyid_kunde` bigint unsigned,
      `carrier_name` varchar(60),
      `cio_quelle` varchar(10),
      `terminbu_bauauftrag` varchar(10),
      `terminbu_gf_ap` varchar(10),
      `terminbu_auskund` varchar(10),
      `stornierungsgrund` varchar(80),
      `bppd_id` bigint unsigned,
      `rufnr_supplier_bau` varchar (40),
      `mail_supplier_bau` varchar (60),
      `rufnr_supplier_ausk` varchar (40),
      `mail_supplier_ausk` varchar (60),
      `rufnr_supplier_gfap` varchar (40),
      `mail_supplier_gfap` varchar (60),
      `tarifbest` varchar(5),
      `aenddatum_bestellung` DATETIME,
      `has_rule_violation` varchar(1),
      `ba_nr_intern` bigint unsigned NOT NULL,
      `bedarfspunktname` varchar(150),
      `bedarfspunkt_id` varchar(150),
      `erst_wg_bauauftrag` datetime,
      `update_wg_bauauftrag` datetime,
      `erst_wg_gebauede` datetime,
      `update_wg_gebauede` datetime,
      `t_ladedatum` datetime,
      primary key (`ba_nr_intern`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ggs_gfp_rop_cio` (
      `ba_nummer` int unsigned,
      `auftrag_info_supplier` varchar(15),
      `naechster_schritt` varchar(125),
      `neubaulokation` varchar(5),
      `nbg_erfassungsstatus` varchar(15),
      `ba_status` varchar(25),
      `zu_bauen_bis` DATETIME,
      `wunschtermin` DATETIME,
      `best_nummer` varchar(25) NOT NULL,
      `erstell_datum_bestellung` DATETIME NOT NULL,
      `best_status` varchar(20),
      `statusgrund` varchar(80),
      `rollout_partn_initial` varchar(35),
      `rollout_partn_lokal` varchar(35),
      `rollout_partn_regel` varchar(35),
      `mandant` int unsigned,
      `kls_id` bigint unsigned NOT NULL,
      `fol_id` bigint unsigned,
      `gebauede_typ` varchar(20),
      `we` int unsigned NOT NULL,
      `ge` int unsigned NOT NULL,
      `zb` int unsigned NOT NULL,
      `gf_id` varchar(10),
      `erstelldatum` datetime,
      `aenderungsdatum` datetime,
      `auftrags_typ` varchar(20) NOT NULL,
      `area_nr` varchar(50) NOT NULL,
      `area_name` varchar(150),
      `area_status` varchar(20) NOT NULL,
      `ausbau_status` varchar(15) NOT NULL,
      `eigent_zust_status` varchar(10),
      `zust_status_ne3` varchar(10),
      `zust_status_ne4` varchar(10),
      `grund_nichtzust` varchar(40),
      `onkz` int unsigned,
      `asb` int unsigned,
      `nvt` varchar(10),
      `plz` varchar(5),
      `ort` varchar(50),
      `strasse` varchar(50),
      `hausnr` varchar(10),
      `hausnr_zusatz` varchar(10),
      `terminbuchung_status` varchar(25),
      `start_inst` datetime,
      `ende_inst` datetime,
      `wo_id_gf_ta` int unsigned,
      `wo_status_gf_ta` varchar(15),
      `bau_gf_ta_umgesetzt` datetime,
      `ba_supplier_partyid` bigint unsigned,
      `supplier_bauauftrag` varchar(82),
      `phase_bauauftrag` varchar(20),
      `nvt_prog_status_ba` varchar(30),
      `wartegrund_ba` varchar(35),
      `wv_datum_ba` datetime,
      `erw_loesung_am` datetime,
      `wartegrund_ba_supplier` varchar(100),
      `warteg_ba_supplier_partyid` bigint unsigned,
      `auskund_erfordl` varchar(5),
      `auskund_erledigt` varchar(5),
      `terminstatus_auskund` varchar(25),
      `wo_id_auskundung` int unsigned,
      `wo_status_auskund` varchar(15),
      `ausk_plan_start` datetime,
      `ausk_plan_ende` datetime,
      `ausk_umgesetzt` datetime,
      `ausk_supplier_partyid` bigint unsigned,
      `supplier_auskundung` varchar(82),
      `phase_ausk` varchar(20),
      `gf_ap` varchar(45),
      `inventarstatus_gf_ap` varchar(10),
      `inst_status_bau_gf_ap` varchar(10),
      `status_bau_gf_ap` varchar(25),
      `wo_id_gf_ap` int unsigned,
      `wo_status_gf_ap` varchar(15),
      `bau_gf_ap_plan_start` datetime,
      `bau_gf_ap_plan_ende` datetime,
      `umgesetzt_bau_ap` datetime,
      `gfap_supplier_partyid` bigint unsigned,
      `gf_ap_supplier` varchar(100),
      `phase_gf_ap` varchar(20),
      `wartegrund_gebaeude` varchar(55),
      `wv_datum_gebaeude` datetime,
      `erw_loesung_gebaeude` datetime,
      `wartegrund_gebaeude_supplier` varchar(100),
      `warteg_gebaeude_supplier_partyid` bigint unsigned,
      `tsg_id_ausk` int unsigned,
      `tsg_id_gfap` int unsigned,
      `tsg_id_dose` int unsigned,
      `passiv_provid` int unsigned,
      `partyid_kunde` bigint unsigned,
      `carrier_name` varchar(60),
      `cio_quelle` varchar(10),
      `terminbu_bauauftrag` varchar(10),
      `terminbu_gf_ap` varchar(10),
      `terminbu_auskund` varchar(10),
      `stornierungsgrund` varchar(80),
      `bppd_id` bigint unsigned,
      `rufnr_supplier_bau` varchar (40),
      `mail_supplier_bau` varchar (60),
      `rufnr_supplier_ausk` varchar (40),
      `mail_supplier_ausk` varchar (60),
      `rufnr_supplier_gfap` varchar (40),
      `mail_supplier_gfap` varchar (60),
      `tarifbest` varchar(5),
      `aenddatum_bestellung` DATETIME,
      `has_rule_violation` varchar(1),
      `ba_nr_intern` bigint unsigned NOT NULL,
      `bedarfspunktname` varchar(150),
      `bedarfspunkt_id` varchar(150),
      `erst_wg_bauauftrag` datetime,
      `update_wg_bauauftrag` datetime,
      `erst_wg_gebauede` datetime,
      `update_wg_gebauede` datetime,
      `t_ladedatum` datetime,
      primary key (`ba_nr_intern`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS adressen_inland (
    `kls_id` bigint unsigned not null,
    `objekt_id` bigint unsigned,
    `adresstyp` varchar(2) not null,
    `hausnummer` int unsigned,
    `hsnr_zusatz` varchar(6),
    `strasse_name` varchar(40),
    `strasse_kurz` varchar(25),
    `strasse_phonetisch` varchar(40),
    `wohnort_name` varchar(40),
    `wohnort_zusatz` varchar(40),
    `wohnort_gdtnr` varchar(11),
    `wohnort_kuerzel` varchar(3),
    `gemeinde_name` varchar(40),
    `gemeinde_zusatz` varchar(40),
    `gemeinde_gdgtnr` varchar(11),
    `gemeinde_kuerzel` varchar(3),
    `postort_name` varchar(40),
    `postort_zusatz` varchar(40),
    `postort_kurz` varchar(24),
    `posfach` varchar(6),
    `postleitzahl` varchar(5),
    `bundesland` varchar(3),
    `bezirk` varchar(40),
    `landkreis_name` varchar(40),
    `landkreis_zusatz` varchar(40),
    `aenderungs_kz` varchar(1) not null,
    `aenderungsdatum` datetime not null,
    `aenderungszeit` time not null,
    `id_strasse` bigint unsigned,
    `id_wohort` bigint unsigned,
    `id_gemeinde` bigint unsigned,
    `id_postort` bigint unsigned,
    `aenderungs_nr` int unsigned,
    `postort_gdgtnr` varchar(11),
    `postort_kuerzel` varchar(3),
    `geo_laenge` decimal(8,5), -- 5 digits after dicimal point means 3 digits left pre decimal point
    `geo_breite` decimal(8,5),
    `laengen_kz` varchar(1),
    `breiten_kz` varchar(1),
    `asb` int unsigned,
    `onkz` int unsigned,
    primary key (`kls_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `dbbgl_1_valid_temp`(
  `sma` varchar(50) NOT NULL,
  `updated_at` datetime NOT NULL,
  `distance_typ` varchar(50),
  `distance_laenge` decimal(12,2),
  `kreditor_nr` bigint unsigned,
  `kreditor_name` varchar(70),
  `t_ladedatum` datetime NOT NULL,
  `vorhanden_ep1` tinyint(1) NOT NULL,
  PRIMARY KEY (`sma`, `distance_typ`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_1_valid`(
    `sma` bigint unsigned NOT NULL,
    `updated_at` datetime NOT NULL,
    `distance_typ` varchar(50),
    `distance_laenge` decimal(12,2),
    `kreditor_nr` bigint unsigned,
    `kreditor_name` varchar(70),
    `t_ladedatum` datetime NOT NULL,
    `vorhanden_ep1` tinyint(1) NOT NULL,
    PRIMARY KEY (`sma`, `distance_typ`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_1_invalid_temp`(
    `sma` varchar(50) NOT NULL,
    `updated_at` datetime NOT NULL,
    `distance_typ` varchar(50),
    `distance_laenge` decimal(12,2),
    `kreditor_nr` bigint unsigned,
    `kreditor_name` varchar(70),
    `t_ladedatum` datetime NOT NULL,
    `vorhanden_ep1` tinyint(1) NOT NULL,
    PRIMARY KEY (`sma`, `distance_typ`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE  = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_1_invalid`(
  `sma` varchar(50) NOT NULL,
  `updated_at` datetime NOT NULL,
  `distance_typ` varchar(50),
  `distance_laenge` decimal(12,2),
  `kreditor_nr` bigint unsigned,
  `kreditor_name` varchar(70),
  `t_ladedatum` datetime NOT NULL,
  `vorhanden_ep1` tinyint(1) NOT NULL,
  PRIMARY KEY (`sma`, `distance_typ`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_2_temp`(
    `sma` bigint unsigned NOT NULL,
    `sma_status` varchar(20) NOT NULL,
    `geometryId` varchar(40) NOT NULL,
    `geometry_reference` varchar(40),
    `place` varchar(40),
    `token` varchar(40),
    `created` datetime NOT NULL,
    `type` varchar(10) NOT NULL,
    `initial_hash` varchar(40),
    # property fields
    `edited` datetime,
    `comment` text,
    `material` varchar(100),
    `typeId` varchar(40),
    `objectId` varchar(25) NOT NULL,
    `erfasst_am` datetime,
    `grabenbreite` float unsigned,
    `verlegetiefe` float,
    `georef_statuscolor` varchar(6),
    `tiefbauverfahren` varchar(50) NOT NULL,
    `anzahl_rohrverband` int unsigned,
    `postprocessingpending` tinyint(1),
    # property fields
    `geography` text,
    `geog` text,
    `length` float unsigned,
    `source` varchar(15) NOT NULL,
    `provider_name` varchar(40),
    `status` varchar(10) NOT NULL,
    `t_ladedatum` datetime NOT NULL,
    `vorhanden_ep2` tinyint(1) NOT NULL,
    PRIMARY KEY (`geometryId`),
    KEY `sma` (`sma`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_2` (
     `sma` bigint unsigned NOT NULL,
     `sma_status` varchar(20) NOT NULL,
     `geometryId` varchar(40) NOT NULL,
     `geometry_reference` varchar(40),
     `place` varchar(40),
     `token` varchar(40),
     `created` datetime NOT NULL,
     `type` varchar(10) NOT NULL,
     `initial_hash` varchar(40),
     `edited` datetime,
     `comment` text,
     `material` varchar(100),
     `typeId` varchar(40),
     `objectId` varchar(25) NOT NULL,
     `erfasst_am` datetime,
     `grabenbreite` float unsigned,
     `verlegetiefe` float,
     `georef_statuscolor` varchar(6),
     `tiefbauverfahren` varchar(50) NOT NULL,
     `anzahl_rohrverband` int unsigned,
     `postprocessingpending` tinyint(1),
     `geography` text,
     `geog` text,
     `length` float unsigned,
     `source` varchar(15) NOT NULL,
     `provider_name` varchar(40),
     `status` varchar(10) NOT NULL,
     `t_ladedatum` datetime NOT NULL,
     `vorhanden_ep2` tinyint(1) NOT NULL,
     PRIMARY KEY (`geometryId`),
     KEY `sma` (`sma`),
     KEY `vorhanden_ep2` (`vorhanden_ep2`),
     KEY `type` (`type`,`vorhanden_ep2`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_2_temp`(
     `sma` bigint unsigned NOT NULL,
     `sma_status` varchar(20) NOT NULL,
     `geometryId` varchar(40) NOT NULL,
     `geometry_reference` varchar(40),
     `place` varchar(40),
     `token` varchar(40),
     `created` datetime NOT NULL,
     `type` varchar(10) NOT NULL,
     `initial_hash` varchar(40),
# property fields
     `edited` datetime,
     `comment` text,
     `material` varchar(100),
     `typeId` varchar(40),
     `objectId` varchar(25) NOT NULL,
     `erfasst_am` datetime NOT NULL,
     `grabenbreite` float unsigned,
     `verlegetiefe` float,
     `georef_statuscolor` varchar(6) NOT NULL,
     `tiefbauverfahren` varchar(50) NOT NULL,
     `anzahl_rohrverband` int unsigned,
     `postprocessingpending` tinyint(1),
# property fields
     `geography` text,
     `geog` text,
     `length` float unsigned,
     `source` varchar(15) NOT NULL,
     `provider_name` varchar(40),
     `status` varchar(10) NOT NULL,
     `t_ladedatum` datetime NOT NULL,
     `vorhanden_ep1` tinyint(1) NOT NULL,
     PRIMARY KEY (`geometryId`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_2_1_detailmenge` (
     `sma` bigint unsigned NOT NULL,
     `sma_status` varchar(20) NOT NULL,
     `geometryId` varchar(40) NOT NULL,
     `geometry_reference` varchar(40),
     `created` datetime NOT NULL,
     `type` varchar(10) NOT NULL,
     `edited` datetime,
     `comment` text,
     `material` varchar(100),
     `objectId` varchar(25) NOT NULL,
     `erfasst_am` datetime NOT NULL,
     `grabenbreite` float unsigned,
     `verlegetiefe` float,
     `georef_statuscolor` varchar(6) NOT NULL,
     `tiefbauverfahren` varchar(50) NOT NULL,
     `anzahl_rohrverband` int,
     `postprocessingpending` tinyint(1),
     `length` float unsigned,
     `source` varchar(15) NOT NULL,
     `provider_name` varchar(40),
     `status` varchar(10) NOT NULL,
     `t_ladedatum` datetime NOT NULL,
     `vorhanden_ep2` tinyint(1) NOT NULL,
     PRIMARY KEY (`geometryId`),
     KEY `sma` (sma)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `dbbgl_2_2_laenge` (
    `sma` bigint unsigned NOT NULL,
    `sma_status` varchar(20) NOT NULL,
    `type` varchar(10) NOT NULL,
    `erfasst_am` datetime NOT NULL,
    `t_ladedatum` datetime NOT NULL,
    `laenge_001` decimal(12,2),
    `laenge_002` decimal(12,2),
    `laenge_003` decimal(12,2),
    `laenge_004` decimal(12,2),
    `laenge_005` decimal(12,2),
    `laenge_006` decimal(12,2),
    `laenge_007` decimal(12,2),
    `laenge_008` decimal(12,2),
    `laenge_009` decimal(12,2),
    `laenge_010` decimal(12,2),
    `laenge_011` decimal(12,2),
    `laenge_012` decimal(12,2),
    `laenge_013` decimal(12,2),
    `laenge_014` decimal(12,2),
    `laenge_015` decimal(12,2),
    `laenge_016` decimal(12,2),
    `laenge_017` decimal(12,2),
    `laenge_018` decimal(12,2),
    `laenge_019` decimal(12,2),
    `laenge_020` decimal(12,2),
    `laenge_021` decimal(12,2),
    `laenge_022` decimal(12,2),
    `laenge_023` decimal(12,2),
    `laenge_024` decimal(12,2),
    `laenge_025` decimal(12,2),
    PRIMARY KEY (`sma`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;


CREATE TABLE IF NOT EXISTS `dbbgl_2_3_stueck` (
    `sma` bigint unsigned NOT NULL,
    `sma_status` varchar(20) NOT NULL,
    `type` varchar(20) NOT NULL,
    `erfasst_am` datetime NOT NULL,
    `t_ladedatum` datetime NOT NULL,
    `stueck_001` int,
    `stueck_002` int,
    `stueck_003` int,
    `stueck_004` int,
    `stueck_005` int,
    `stueck_006` int,
    `stueck_007` int,
    `stueck_008` int,
    `stueck_009` int,
    `stueck_010` int,
    `stueck_011` int,
    `stueck_012` int,
    `stueck_013` int,
    `stueck_014` int,
    `stueck_015` int,
    `stueck_016` int,
    `stueck_017` int,
    `stueck_018` int,
    `stueck_019` int,
    `stueck_020` int,
    `stueck_021` int,
    `stueck_022` int,
    `stueck_023` int,
    `stueck_024` int,
    `stueck_025` int,
    PRIMARY KEY (`sma`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

-- REF TABLES

CREATE TABLE IF NOT EXISTS `ref_art` (
  `art` int(11) NOT NULL,
  `bezeichnung` varchar(100) NOT NULL,
  primary key (`art`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Referenz für Container';


CREATE TABLE IF NOT EXISTS `ref_kreditor` (
  `kreditor` int(10) unsigned zerofill NOT NULL,
  `firmenname` varchar(100) NOT NULL,
  primary key (`kreditor`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Referenz für Container';

CREATE TABLE IF NOT EXISTS `ref_benefit_type_decision_unit` (
  `benefit_type` char(2) DEFAULT NULL,
  `decision_unit` char(2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Referenz für Container';

CREATE TABLE IF NOT EXISTS `referenz_gewerke_ausnahme` (
  `gewerke_neu` varchar(20) NOT NULL,
  `1_gf` char(8) NOT NULL,
  `1_gf_pgz` char(2) NOT NULL,
  `2_gf` char(8) NOT NULL,
  PRIMARY KEY (`gewerke_neu`,`1_gf`,`1_gf_pgz`,`2_gf`),
  KEY `1_gf` (`1_gf`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Referenz für ausgenommene Gewerke';

CREATE TABLE IF NOT EXISTS `ref_nap_gruppen` (
  `AP_GRUPPEN_ID` int(11) NOT NULL DEFAULT '0',
  `AP_GRUPPE` varchar(16) NOT NULL,
  PRIMARY KEY (`AP_GRUPPEN_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='REF NAP Gruppen';

CREATE TABLE IF NOT EXISTS `ref_prod_stoer_tafel` (
  `REF_NR` varchar(10) DEFAULT NULL,
  `BEZEICHNUNG` varchar(19) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_ende_ue_wege` (
  `RANGFOLGE` varchar(6) DEFAULT NULL,
  `BEZEICHNUNG` varchar(53) DEFAULT NULL,
  `RES_ZUS` varchar(6) DEFAULT NULL,
  `ENDE` varchar(4) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_fr_ivh` (
  `prozessausloeser` char(8) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='A.Rah:Sammlung der IVH Geschäftsfälle ';

CREATE TABLE IF NOT EXISTS `ref_fr_container` (
  `lfd_nr` int(2) unsigned NOT NULL AUTO_INCREMENT,
  `auspraegung` varchar(30) NOT NULL,
  `beschreibung` varchar(255) NOT NULL,
  PRIMARY KEY (`lfd_nr`),
  KEY `auspraegung` (`auspraegung`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Referenz für Beast';

-- Create syntax for TABLE 'ref_ni_schema'
CREATE TABLE IF NOT EXISTS `ref_ni_schema` (
  `T_GUELTIG_VON` varchar(19) NOT NULL,
  `T_GUELTIG_BIS` varchar(19) NOT NULL,
  `T_GLB_ORGE_SK` smallint(6) NOT NULL DEFAULT '0',
  `GLB_ORGE_ID` varchar(32) NOT NULL,
  `GLB_ORGE` varchar(111) NOT NULL,
  `GLB_ORGE_ID_PARENT` varchar(22) NOT NULL,
  `GLB_ORGE_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_TYP` varchar(36) NOT NULL,
  `T_GLB_ORGE_1_SK` varchar(2) NOT NULL,
  `GLB_ORGE_1_ID` varchar(19) NOT NULL,
  `GLB_ORGE_1_DMR` varchar(19) NOT NULL,
  `GLB_ORGE_1_BEZ` varchar(19) NOT NULL,
  `GLB_ORGE_1_SORT` varchar(1) NOT NULL,
  `GLB_ORGE_1_TYP` varchar(19) NOT NULL,
  `T_GLB_ORGE_2_SK` int(11) DEFAULT NULL,
  `GLB_ORGE_2_ID` varchar(19) NOT NULL,
  `GLB_ORGE_2_DMR` varchar(19) NOT NULL,
  `GLB_ORGE_2_BEZ` varchar(56) NOT NULL,
  `GLB_ORGE_2_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_2_TYP` varchar(21) NOT NULL,
  `T_GLB_ORGE_3_SK` int(11) DEFAULT NULL,
  `GLB_ORGE_3_ID` varchar(21) NOT NULL,
  `GLB_ORGE_3_DMR` varchar(21) NOT NULL,
  `GLB_ORGE_3_BEZ` varchar(35) NOT NULL,
  `GLB_ORGE_3_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_3_TYP` varchar(27) NOT NULL,
  `T_GLB_ORGE_4_SK` int(11) DEFAULT NULL,
  `GLB_ORGE_4_ID` varchar(22) NOT NULL,
  `GLB_ORGE_4_DMR` varchar(22) NOT NULL,
  `GLB_ORGE_4_BEZ` varchar(44) NOT NULL,
  `GLB_ORGE_4_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_4_TYP` varchar(36) NOT NULL,
  `T_GLB_ORGE_5_SK` int(11) DEFAULT NULL,
  `GLB_ORGE_5_ID` varchar(32) NOT NULL,
  `GLB_ORGE_5_DMR` varchar(32) NOT NULL,
  `GLB_ORGE_5_BEZ` varchar(70) NOT NULL,
  `GLB_ORGE_5_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_5_TYP` varchar(27) NOT NULL,
  `T_GLB_ORGE_6_SK` int(11) DEFAULT NULL,
  `GLB_ORGE_6_ID` varchar(29) NOT NULL,
  `GLB_ORGE_6_DMR` varchar(29) NOT NULL,
  `GLB_ORGE_6_BEZ` varchar(110) NOT NULL,
  `GLB_ORGE_6_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_6_TYP` varchar(17) NOT NULL,
  `T_GLB_ORGE_7_SK` int(11) DEFAULT NULL,
  `GLB_ORGE_7_ID` varchar(9) NOT NULL,
  `GLB_ORGE_7_DMR` varchar(9) NOT NULL,
  `GLB_ORGE_7_BEZ` varchar(111) NOT NULL,
  `GLB_ORGE_7_SORT` int(11) DEFAULT NULL,
  `GLB_ORGE_7_TYP` varchar(14) NOT NULL,
  `T_GLB_ORGE_8_SK` varchar(255) NOT NULL,
  `GLB_ORGE_8_ID` varchar(255) NOT NULL,
  `GLB_ORGE_8_DMR` varchar(255) NOT NULL,
  `GLB_ORGE_8_BEZ` varchar(255) NOT NULL,
  `GLB_ORGE_8_SORT` varchar(255) NOT NULL,
  `GLB_ORGE_8_TYP` varchar(255) NOT NULL,
  `T_GLB_ORGE_9_SK` varchar(255) NOT NULL,
  `GLB_ORGE_9_ID` varchar(255) NOT NULL,
  `GLB_ORGE_9_DMR` varchar(255) NOT NULL,
  `GLB_ORGE_9_BEZ` varchar(255) NOT NULL,
  `GLB_ORGE_9_SORT` varchar(255) NOT NULL,
  `GLB_ORGE_9_TYP` varchar(255) NOT NULL,
  `T_GLB_ORGE_10_SK` varchar(255) NOT NULL,
  `GLB_ORGE_10_ID` varchar(255) NOT NULL,
  `GLB_ORGE_10_DMR` varchar(255) NOT NULL,
  `GLB_ORGE_10_BEZ` varchar(255) NOT NULL,
  `GLB_ORGE_10_SORT` varchar(255) NOT NULL,
  `GLB_ORGE_10_TYP` varchar(255) NOT NULL,
  `GLB_ORGE_TREE_LEVEL` int(11) DEFAULT NULL,
  PRIMARY KEY (`T_GLB_ORGE_SK`),
  KEY `T_GLB_ORGE_SK` (`T_GLB_ORGE_SK`),
  KEY `GLB_ORGE_4_ID` (`GLB_ORGE_4_ID`),
  KEY `GLB_ORGE_5_ID` (`GLB_ORGE_5_ID`),
  KEY `GLB_ORGE_6_ID` (`GLB_ORGE_6_ID`),
  KEY `T_GLB_ORGE_4_SK` (`T_GLB_ORGE_4_SK`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'ref_orge'
CREATE TABLE IF NOT EXISTS `ref_orge` (
  `t_gueltig_von` varchar(20) NOT NULL,
  `t_gueltig_bis` varchar(20) NOT NULL,
  `t_ladedatum` varchar(20) NOT NULL,
  `twf_ressort_id` varchar(20) NOT NULL,
  `glb_orge_id` varchar(50) NOT NULL,
  PRIMARY KEY (`t_gueltig_von`,`t_gueltig_bis`,`t_ladedatum`,`twf_ressort_id`,`glb_orge_id`),
  KEY `twf_ressort_id` (`twf_ressort_id`),
  KEY `twf_ressort_id_2` (`twf_ressort_id`),
  KEY `glb_orge_id` (`glb_orge_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `orge` (
  `ZUS_RESSORT` varchar(20) NOT NULL,
  `ORGE_ID` varchar(50) NOT NULL,
  `ORGE_4` varchar(22) NOT NULL,
  `ORGE_4_SK` int(11) DEFAULT NULL,
  `ORGE_5` varchar(32) NOT NULL,
  `ORGE_5_SK` int(11) DEFAULT NULL,
  `ORGE_6` varchar(29) NOT NULL,
  `ORGE_6_SK` int(11) DEFAULT NULL,
  `ORGE_SK` smallint(6) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ZUS_RESSORT`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_fr_bewertet` (
  `id` tinyint(2) unsigned NOT NULL,
  `fr_bewertet` varchar(50) NOT NULL,
  `beschreibung` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `uewege_lsz_ausnahme` (
  `lsz` varchar(15) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_koa_buda` (
  `koa` int(11) NOT NULL DEFAULT '0',
  `koa_txt` varchar(255) NOT NULL,
  `buda` varchar(20) NOT NULL,
  PRIMARY KEY (`koa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_af_container` (
  `af_id` varchar(15) NOT NULL,
  `af_name` varchar(60) NOT NULL,
  `container` varchar(50) NOT NULL,
  `g_ab` datetime,
  `g_bis` datetime,
  KEY `af_id` (`af_id`),
  KEY `g_ab` (`g_ab`),
  KEY `g_bis` (`g_bis`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Referenz für Container';

CREATE TABLE IF NOT EXISTS `ref_wfmt_gruppen` (
  `gruppenname` varchar(128) NOT NULL,
  `aenderungsdatum` DATETIME NOT NULL,
  `nl` varchar(30) NULL,
  `ressort_typ` varchar(50) NULL,
  `ressortname` varchar(255) NULL,
  `ressort_lang` varchar(60) NULL,
  PRIMARY KEY (gruppenname)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_bwa` (
    `bwa` VARCHAR(3) NOT NULL,
    `bwatext` VARCHAR(20),
    PRIMARY KEY (`bwa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_werk` (
    `werk` VARCHAR(4) NOT NULL,
    `such_2` VARCHAR(13),
    `such_1` VARCHAR(20),
    `werk_plz` VARCHAR(8),
    `werk_ort` VARCHAR(17),
    `werk_name_2` VARCHAR(33),
    `werk_name` VARCHAR(40),
    `version` VARCHAR(7),
    PRIMARY KEY (`werk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `ref_nvt` (
    `prozessausloeser` VARCHAR(10) NOT NULL,
    PRIMARY KEY (`prozessausloeser`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_strukturplan` (
    `gf_struktur` VARCHAR(10) NOT NULL,
    PRIMARY KEY (gf_struktur)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `ref_dbbgl_bw`(
    `distance_typ` varchar(50),
    `bauweise` int,
    `sum_2` int,
    `sum_1` int,
    PRIMARY KEY (distance_typ)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `ref_dbbgl_data_properties`(
    `property` varchar(50),
    PRIMARY KEY (property)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `ref_dbbgl_st`(
    `objectId` varchar(50),
    `stueck` int,
    `stueck_2` int,
    `stueck_1` int,
    PRIMARY KEY (objectId)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `processing_parameters`(
    table_name varchar(200) NOT NULL,
    max_package_size int,
    min_package_size int,
    num_processes int,
    last_num_processes int,
    fixed_num_processes int,
    ram_factor int,
    last_processing_duration int,
    last_ram_usage int,
    last_processing_timestamp datetime,
    PRIMARY KEY (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- IWAN TABLES

-- Create syntax for TABLE 'iwan_ap_serg_reihenfolge'
CREATE TABLE IF NOT EXISTS `iwan_ap_serg_reihenfolge` (
  `alle_serg` text NOT NULL,
  `id_ap` int(11) NOT NULL,
  `ref_id_ap_glob` int(11) NOT NULL,
  `arbeitsplan` varchar(100) NOT NULL,
  `version` int(11) NOT NULL,
  `status` int(11) NOT NULL,
  `versions_serg` varchar(10) NOT NULL,
  `daprida_serg` varchar(10) NOT NULL,
  `aktiviert` datetime NOT NULL,
  `relevanz` text NOT NULL,
  `arbeitsplan_ohnepti` varchar(96) NOT NULL COMMENT 'Feld arbeitsplan ohne PTI_ für performantere joins',
  PRIMARY KEY (`arbeitsplan`,`version`),
  KEY `arbeitsplan_ohnepti` (`arbeitsplan_ohnepti`),
  KEY `ref_id_ap_glob` (`ref_id_ap_glob`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'iwan_bezugsdauer'
CREATE TABLE IF NOT EXISTS `iwan_bezugsdauer` (
  `uid` smallint(5) unsigned NOT NULL,
  `id_ap` smallint(5) unsigned NOT NULL,
  `arbeitsplan` varchar(100) NOT NULL,
  `version` tinyint(3) unsigned NOT NULL,
  `rang` tinyint(3) unsigned NOT NULL,
  `schritt` varchar(30) NOT NULL,
  `profil` varchar(30) NOT NULL,
  `serg` varchar(20) NOT NULL,
  `bezugs_zeitpunkt` varchar(45) NOT NULL,
  `bezugsdauer` varchar(100) NOT NULL,
  `kalender` varchar(255) NOT NULL,
  `bezugsdauer_in_minuten` mediumint(9) NOT NULL,
  `aktivierung_iwan` datetime NOT NULL,
  `arbeitsplan_ohnepti` varchar(96) NOT NULL COMMENT 'Feld arbeitsplan ohne PTI_ für performantere joins',
  PRIMARY KEY (`uid`),
  KEY `arbeitsplan` (`arbeitsplan`,`version`),
  KEY `serg` (`serg`),
  KEY `arbeitsplan_ohnepti` (`arbeitsplan_ohnepti`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'iwan_gf_pgz_detail'
CREATE TABLE IF NOT EXISTS `iwan_gf_pgz_detail` (
  `rs` varchar(10) NOT NULL,
  `mgl_rs` varchar(255),
  `gf` varchar(10) NOT NULL,
  `pgz` char(2) NOT NULL,
  `gfpl_name` varchar(100) NOT NULL,
  `verwendung` varchar(10) NOT NULL,
  `gf_typ` char(15),
  `anlkl` varchar(255),
  `merkmal` varchar(100),
  `def_ref_nr` varchar(10),
  `kostst` varchar(1000),
  `gf_pgz` VARCHAR(255) NOT NULL DEFAULT '',
  KEY `gf_pgz` (`gf`,`pgz`),
  KEY `gf_pgz_index` (`gf_pgz`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'iwan_projektgruppe'
CREATE TABLE IF NOT EXISTS `iwan_projektgruppe` (
  `ap_gruppen_id` smallint(5) unsigned NOT NULL,
  `ap_gruppe` varchar(20) NOT NULL,
  `ap` varchar(100) NOT NULL,
  `id_ap` smallint(5) unsigned NOT NULL,
  `version` tinyint(3) unsigned NOT NULL,
  `v_serg` char(50) NOT NULL,
  `y_serg` char(50) NOT NULL,
  `arbeitsplan_ohnepti` varchar(96) NOT NULL COMMENT 'Feld arbeitsplan ohne PTI_ für performantere joins',
  KEY `arbeitsplan_ohnepti` (`arbeitsplan_ohnepti`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- CALCULATED TABLES

CREATE TABLE IF NOT EXISTS `wfm_17_nvt` (
  `requidbafa` char(15) NOT NULL,
  `nvt_ap_` tinyint(1) DEFAULT NULL,
  `hk_` tinyint(1) DEFAULT NULL,
  `nvt` varchar(10) DEFAULT NULL,
  `gf_hk` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`requidbafa`),
  KEY `nvt` (`nvt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'wms_1_allgemein'
CREATE TABLE IF NOT EXISTS `wms_1_allgemein` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `bafa` char(2) NOT NULL,
  `statusbafa` varchar(30) NOT NULL,
  `storno` tinyint(1) NOT NULL,
  `auftrkurzbeschr` varchar(150) NOT NULL,
  `onkz` smallint(5) unsigned NOT NULL,
  `asb` smallint(3) unsigned NOT NULL DEFAULT '0',
  `servicelevel_id` tinyint(3) unsigned NOT NULL,
  `ref_bafaid_ggst` varchar(15) NOT NULL,
  `vorsystemres` varchar(12) NOT NULL,
  `vorsystem` varchar(12) NOT NULL,
  `auftrnrag` varchar(50) NOT NULL,
  `projekt_id` varchar(30) NOT NULL,
  `sma_anlegen` tinyint(3) unsigned NOT NULL,
  `smauftrnr_tcom` bigint(12) unsigned zerofill NOT NULL,
  `smauftrnr_manuell` bigint(12) unsigned zerofill NOT NULL,
  `pspelement` varchar(25) NOT NULL,
  `auftrfall` varchar(60) NOT NULL,
  `teilauftrfall` varchar(60) NOT NULL,
  `prozessausloeser` char(8) NOT NULL,
  `pgz` char(2) NOT NULL,
  `wmsti_summenauftrag` varchar(15) NOT NULL,
  `wmsti_auftrnrfremd` varchar(15) NOT NULL,
  `wmsti_lsz` varchar(15) NOT NULL,
  `wmsti_lszerg` varchar(15) NOT NULL,
  `wmsti_vpsza` varchar(20) NOT NULL,
  `wmsti_vpszb` varchar(20) NOT NULL,
  `wmsti_vpszschaltst` varchar(20) NOT NULL,
  `wmsti_auftrartvorsystem` varchar(15) NOT NULL,
  `wmsti_aktarbgrp` varchar(30) NOT NULL,
  `statusbafa_int` mediumint(5) NOT NULL DEFAULT '0',
  `servicelevel` varchar(30) NOT NULL,
  `wmsti_bezugselement_pmps` varchar(40) NOT NULL,
  `wmsti_zustres` varchar(30) NOT NULL,
  `ressort_typ` varchar(50) NOT NULL,
  `lsz_zusatz` VARCHAR(4) NOT NULL,
  PRIMARY KEY (`requidbafa`),
  KEY `vorsystem` (`vorsystem`),
  KEY `statusbafa` (`statusbafa`),
  KEY `statusbafa_int` (`statusbafa_int`),
  KEY `vorsystem_2` (`vorsystem`,`statusbafa_int`),
  KEY `smauftrnr_tcom` (`smauftrnr_tcom`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST - Allgemeine WMS Daten';

-- Create syntax for TABLE 'wms_2_termine'
CREATE TABLE IF NOT EXISTS `wms_2_termine` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `wunschbegdatum` datetime NOT NULL,
  `wunschendedatum` datetime NOT NULL,
  `agbzieldatum` datetime NOT NULL,
  `eingdtagdatum` datetime NOT NULL,
  `bafaaenddatum` datetime NOT NULL,
  `bafabetrbereitdatum` datetime NOT NULL,
  `betrbereitkorrdatum` datetime NOT NULL,
  `betrbereitkorr_jjjjmm` mediumint(6) unsigned zerofill NOT NULL,
  `betrbereitkorr_jjkw` smallint(4) unsigned zerofill NOT NULL,
  `bafatechenddatum_ts` datetime NOT NULL,
  `berechneter_bafa_betriebsbereit` datetime NOT NULL,
  `gew_eck_ende` varchar(25) Default NULL,
  `bbkorr_gewende` smallint(5) NOT NULL,
  `beginn_verzoegerung` datetime default null,
  `ende_verzoegerung` datetime default null,
  `lieferfristdiagnosefest` datetime default null,
  `lieferfristproblemloesungfest` datetime default null,
  `wandlungstermin` datetime default null,
  PRIMARY KEY (`requidbafa`),
  KEY bafaaenddatum (bafaaenddatum)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST - WMS Termine';


-- Create syntax for TABLE 'wms_3_lieferzeit'
CREATE TABLE IF NOT EXISTS `wms_3_lieferzeit` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `lz_bea_kt` int(11) NOT NULL DEFAULT '0',
  `lz_plan_kt` int(11) NOT NULL DEFAULT '0',
  `lz_verbr_kt` int(11) NOT NULL DEFAULT '0',
  `lz_verbr_dtag_kt` int(11) NOT NULL DEFAULT '0',
  `lz_verbr_dtag_at` int(11) NOT NULL DEFAULT '0',
  `lz_verbr_kt_proz` decimal(12,4) DEFAULT NULL,
  `lz_rest_kt` int(11) NOT NULL DEFAULT '0',
  `lz_rest_kt_proz` decimal(12,4) DEFAULT NULL,
  `lz_ist_kt` int(11) NOT NULL DEFAULT '0',
  `nachlauf_kt` int(11) NOT NULL DEFAULT '0',
  `lz_bea_at` int(11) NOT NULL DEFAULT '0',
  `lz_plan_at` int(11) NOT NULL DEFAULT '0',
  `lz_verbr_at` int(11) NOT NULL DEFAULT '0',
  `lz_bea_verbr_at_proz` decimal(12,4) DEFAULT NULL,
  `lz_verbr_at_proz` decimal(12,4) DEFAULT NULL,
  `lz_rest_at` int(11) NOT NULL DEFAULT '0',
  `lz_rest_at_proz` decimal(12,4) DEFAULT NULL,
  `lz_ist_at` int(11) NOT NULL DEFAULT '0',
  `nachlauf_at` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 3 Lieferzeit';

-- Create syntax for TABLE 'wms_4_arbeitsplan'
CREATE TABLE IF NOT EXISTS `wms_4_arbeitsplan` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `arbeitsplan_bez` varchar(100) DEFAULT NULL,
  `arb_plan` char(3) NOT NULL DEFAULT 'V00',
  `wmsti_referenz_nr` varchar(15) DEFAULT NULL,
  `statusergaenzung` varchar(10) DEFAULT NULL,
  `gewerke` varchar(15) DEFAULT NULL,
  `arb_plan_int` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `arbeitsplan_id` varchar(30) NOT NULL,
  `auftragstyp` VARCHAR(50) default 'n.v.',
  `kombireferenznr` VARCHAR(15) default 'n.v.',
  PRIMARY KEY (`requidbafa`),
  KEY `auftragstyp` (`auftragstyp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 4 Arbeitsplan';

-- Create syntax for TABLE 'wms_5_prodreif'
CREATE TABLE IF NOT EXISTS `wms_5_prodreif` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `wmsti_ts_nichtprodreif` datetime DEFAULT NULL,
  `wmsti_zeitstempel_prodreif` datetime DEFAULT NULL,
  `wmsti_nichtprodreif` tinyint(1) DEFAULT NULL,
  `merker_nichtprodreif` tinyint(1) DEFAULT NULL,
  `uib` tinyint(1) default 0,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 5 Prodreif';


-- Create syntax for TABLE 'wms_6_master'
CREATE TABLE IF NOT EXISTS `wms_6_master` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `master` char(2) DEFAULT NULL,
  `ref_master_ba` char(15) DEFAULT NULL,
  `kask_ba_master` char(15) DEFAULT NULL,
  PRIMARY KEY (`requidbafa`),
  KEY `kask_ba_master` (`kask_ba_master`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 6 Master';

-- Create syntax for TABLE 'wms_7_gefaehrdet'
CREATE TABLE IF NOT EXISTS `wms_7_gefaehrdet` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `arbplan_gefaehrdet` varchar(15) DEFAULT NULL,
  `betrbereitkorr_gefahr` char(5) DEFAULT NULL,
  `zrb_gefaehrdet` char(5) DEFAULT NULL,
    PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 7 Gefaehrdet';


-- Create syntax for TABLE 'wms_8_prio'
CREATE TABLE IF NOT EXISTS `wms_8_prio` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `wmsti_prio_bbn` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 8 Prio';


-- Create syntax for TABLE 'wms_9_zusatz'
CREATE TABLE IF NOT EXISTS `wms_9_zusatz` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `wmsti_vzgres_betrbereit` varchar(25) DEFAULT NULL,
  `wmsti_vertragsbes` varchar(150) DEFAULT NULL,
  `vzg_text` varchar(69) DEFAULT NULL,
  `vzg_text_techn_fertig` VARCHAR(69) default 'n.v.',
  `vzg_schluessel_techn_fertig` VARCHAR(10) default 'n.v.',
  `rahmenvertragsnummer` VARCHAR(10) default 'n.v.',
  `medienentscheidung` VARCHAR(50) default 'n.v.',
  `auftraggeberprioritaet` VARCHAR(4) not null,
  `abschl_freig_status` smallint(3) DEFAULT -1,
  `abschl_freig_erford` tinyint(1) default 0,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 9 Zusatz';

-- Create syntax for TABLE 'wms_10_steuernd'
CREATE TABLE IF NOT EXISTS `wms_10_steuernd` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `psl_steuerung` char(8) DEFAULT NULL,
  `bwl_steuernd` varchar(4) NOT NULL,
  `bwl_steuernd_int` mediumint(5) NOT NULL DEFAULT '0',
  PRIMARY KEY (`requidbafa`),
  KEY `bwl_steuernd` (`bwl_steuernd`),
  KEY `bwl_steuernd_int` (`bwl_steuernd_int`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Wms 10 Steuernd';

-- Create syntax for TABLE 'wfm_11_montageort'
CREATE TABLE IF NOT EXISTS `wfm_11_montageort` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `kundenlokation_a` varchar(30) NOT NULL,
  `mont_a_plz` varchar(30) NOT NULL,
  `mont_a_ort` varchar(40) NOT NULL,
  `mont_a_str` varchar(60) NOT NULL,
  `mont_a_hnr` varchar(60) NOT NULL,
  `mont_a_zus_str` varchar(30) NOT NULL,
  `kundenlokation_b` varchar(30) NOT NULL,
  `mont_b_plz` varchar(30) NOT NULL,
  `mont_b_ort` varchar(40) NOT NULL,
  `mont_b_str` varchar(60) NOT NULL,
  `mont_b_hnr` varchar(60) NOT NULL,
  `mont_b_zus_str` varchar(30) NOT NULL,
  `mont_a_adresse` varchar(234) NOT NULL,
  `mont_b_adresse` varchar(234) NOT NULL,
  `kls_id_mont_a` bigint unsigned not null,
  `kls_id_mont_b` bigint unsigned not null,
  `kls_id_a` bigint unsigned,
  `kls_id_b` bigint unsigned,
  PRIMARY KEY (`requidbafa`),
  KEY `kls_id_mont_a` (kls_id_mont_a),
  KEY `kls_id_a` (kls_id_a)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='wfm 11 Montageort';

-- Create syntax for TABLE 'wfm_12_technisch'
CREATE TABLE IF NOT EXISTS `wfm_12_technisch`
(
    `requidbafa`              char(15),
    `onkz_b`                  mediumint(8) unsigned NOT NULL,
    `schaltst_txt`            varchar(40),
    `ordnr`                   varchar(30),
    `produktbeschr`           varchar(255),
    `vt_punkt`                varchar(255),
    `vt_punkt_b`              varchar(255),
    `koppl_kenner`            varchar(256),
    `auftragspositionsnummer` varchar(50),
    `fsz_a` VARCHAR(30) default 'n.v.',
    `fsz_b` VARCHAR(30) default 'n.v.',
    `fsz_schaltstelle` VARCHAR(6) default 'n.v.',
    `endsz` VARCHAR(27) default 'n.v.',
    `standortkennung` VARCHAR(52) not null,
    `node_id` VARCHAR(10) not null,
    `hbue_termin` datetime default null,
    `standortname` VARCHAR(255) not null,
    `konfiguration` VARCHAR(50) not null,
    `accessgroupid` VARCHAR(9) default null,
    `accessgroupbundle` varchar(1) default null,
    `accessgrouptype` varchar(10) DEFAULT 'n.v.',
    `bandbreite_service` varchar(255) DEFAULT 'n.v.',
    `anschluss_id` varchar(10) DEFAULT 'n.v.',
    PRIMARY KEY (`requidbafa`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='wfm 12 Technisch';

-- Create syntax for TABLE 'wfm_13_storno'
CREATE TABLE IF NOT EXISTS `wfm_13_storno` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
    `stornoausloeser` varchar(15) NOT NULL,
    `stornovomvorsystem` smallint(3) DEFAULT 2,
    `stornodatum` datetime NOT NULL,
    `stornogrund` varchar(255) NOT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='wfm 13 Storno';


-- Create syntax for TABLE 'wfm_14_kunde'
CREATE TABLE IF NOT EXISTS `wfm_14_kunde` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
    `kunde` varchar(40) NOT NULL,
    `kund_anspr_name` varchar(120) NOT NULL,
    `kdnr` varchar(30) NOT NULL,
    `schaediger` varchar(30) NOT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='wfm 14 Kunde';

-- Create syntax for TABLE 'wfm_15_asp'
CREATE TABLE IF NOT EXISTS `wfm_15_asp` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
    `sma_ansp` varchar(30) NOT NULL DEFAULT 'n.v.',
    `sm_asp_regional` varchar(30) NOT NULL DEFAULT 'n.v.',
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='wfm 15 ASP';

-- Create syntax for TABLE 'wfm_15_asp'
CREATE TABLE IF NOT EXISTS `wfm_16_termine_mhl` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `vlt` datetime default null,
  `vrt` datetime default null,
  `vrd` decimal(12,2) DEFAULT NULL,
  `vlw` VARCHAR(7) not null ,
  `mhl_stufe` VARCHAR(4) not null,
  `vlw_jjkw`   VARCHAR(4) NOT NULL DEFAULT '',
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='wfm_16_termine_mhl';


-- Create syntax for TABLE 'psl_1_allgemein'
CREATE TABLE IF NOT EXISTS `psl_1_allgemein` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `verant_arbeitsplatz_text` varchar(40) DEFAULT NULL,
  `planergruppe` varchar(10) DEFAULT NULL,
  `auftrag_quellsystem` varchar(40) DEFAULT NULL,
  `auftrags_beschreibung` varchar(100) DEFAULT NULL,
  `technischer_platz_nr` varchar(30) DEFAULT NULL,
  `technischer_platz_text` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Psl 1 Allgemein';

-- Create syntax for TABLE 'psl_2_psp'
CREATE TABLE IF NOT EXISTS `psl_2_psp` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `ih_leistungsart_text` varchar(100) DEFAULT NULL,
  `auftragsart` varchar(10) DEFAULT NULL,
  `pm_ps_bezugselement` varchar(40) DEFAULT NULL,
  `psp_element` varchar(40) DEFAULT NULL,
  `psp_element_text` varchar(100) DEFAULT NULL,
  `benefit_type` char(2) DEFAULT NULL,
  `decision_unit` char(2) DEFAULT NULL,
  `decision_package` char(2) DEFAULT NULL,
  `jahreskenner` smallint(6) DEFAULT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Psl 2 Psp';

-- Create syntax for TABLE 'psl_3_termine'
CREATE TABLE IF NOT EXISTS `psl_3_termine` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `erfassungsdatum` datetime DEFAULT NULL,
  `aenderungsdatum` datetime DEFAULT NULL,
  `datum_frei` datetime DEFAULT NULL,
  `technabschluss` datetime DEFAULT NULL,
  `planstarttermin` datetime DEFAULT NULL,
  `iststarttermin` datetime DEFAULT NULL,
  `planendtermin` datetime DEFAULT NULL,
  PRIMARY KEY (`sma_psl`),
  KEY aenderungsdatum (aenderungsdatum)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Psl 3 Termine';

-- Create syntax for TABLE 'psl_4_status'
CREATE TABLE IF NOT EXISTS `psl_4_status` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `systemstatus` char(4) DEFAULT NULL,
  `systemstatus_historie` varchar(50) DEFAULT NULL,
  `fakturaverzicht` varchar(10) DEFAULT NULL,
  `systemstatus_int` mediumint(5) NOT NULL DEFAULT '0',
  PRIMARY KEY (`sma_psl`),
  KEY `systemstatus_int` (`systemstatus_int`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Psl 4 Status';

-- Create syntax for TABLE 'psl_5_meldungen'
CREATE TABLE IF NOT EXISTS `psl_5_meldungen` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `a1_meldung` datetime DEFAULT NULL,
  `a2_erfasst` datetime DEFAULT NULL,
  `a2_abschluss` datetime DEFAULT NULL,
  `b2_erfasst` datetime DEFAULT NULL,
  `b2_abschluss` datetime DEFAULT NULL,
  `x2_erfasst` datetime DEFAULT NULL,
  `x2_abschluss` datetime DEFAULT NULL,
  `z0_meldung` datetime DEFAULT NULL,
  `z1_40_meldung` datetime DEFAULT NULL,
  `z1_60_meldung` datetime DEFAULT NULL,
  `z1_70_meldung` datetime DEFAULT NULL,
  `z1_80_meldung` datetime DEFAULT NULL,
  `z1_900_meldung` datetime DEFAULT NULL,
  `z2_erfasst` datetime DEFAULT NULL,
  `z2_abschluss` datetime DEFAULT NULL,
  `z3_mega_meldung` datetime DEFAULT NULL,
  `a2_offen` tinyint(1) DEFAULT NULL,
  `b2_offen` tinyint(1) DEFAULT NULL,
  `x2_offen` tinyint(1) DEFAULT NULL,
  `z2_offen` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Psl 5 Meldungen';

-- Create syntax for TABLE 'psl_6_finanz'
CREATE TABLE IF NOT EXISTS `psl_6_finanz` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `inv1_plan` decimal(11,2) NOT NULL,
  `inv1_obligo` decimal(11,2) NOT NULL,
  `inv1_ist` decimal(11,2) NOT NULL,
  `inv2_plan` decimal(11,2) NOT NULL,
  `inv2_ist` decimal(11,2) NOT NULL,
  `kost1_plan` decimal(11,2) NOT NULL,
  `kost1_obligo` decimal(11,2) NOT NULL,
  `kost1_ist` decimal(11,2) NOT NULL,
  `kost2_plan` decimal(11,2) NOT NULL,
  `kost2_ist` decimal(11,2) NOT NULL,
  `ist_rest` decimal(11,2) NOT NULL,
  `plan_rest` decimal(11,2) NOT NULL,
  `obligo_rest` decimal(11,2) NOT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Psl 6 Finanz';


CREATE TABLE IF NOT EXISTS `phasen_0_arbeitsplan` (
    `requidbafa` varchar(15) NOT NULL,
    `apid` int,
    `ref_id_ap_glob` int,
    `all_ip` tinyint(1) unsigned,
    PRIMARY KEY (`requidbafa`),
    KEY `ref_id_ap_glob` (`ref_id_ap_glob`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'phasen_1_daprida'
CREATE TABLE IF NOT EXISTS `phasen_1_daprida` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `diagnose` varchar(20) DEFAULT NULL,
  `angebot` varchar(15) DEFAULT NULL,
  `projektierung` varchar(20) DEFAULT NULL,
  `realisierung` varchar(20) DEFAULT NULL,
  `inbetriebnahme` varchar(15) DEFAULT NULL,
  `dokumentation` varchar(20) DEFAULT NULL,
  `abschluss` varchar(15) DEFAULT NULL,
  `esel_messung` varchar(15) DEFAULT NULL,
  `phasenstatus` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST - DAPRIDA Phasendaten';

-- Create syntax for TABLE 'phasen_2_diagnose'
CREATE TABLE IF NOT EXISTS `phasen_2_diagnose` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `eingbbndatum` datetime DEFAULT NULL,
  `der_sezst` datetime DEFAULT NULL,
  `der_gefaehrdet` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 2 Diagnose';

-- Create syntax for TABLE 'phasen_3_angebot'
CREATE TABLE IF NOT EXISTS `phasen_3_angebot` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `anb_sezst` datetime DEFAULT NULL,
  `anv_sezst` datetime DEFAULT NULL,
  `wandlung_angebot` char(4) NOT NULL DEFAULT 'Nein',
  `ane_sezst` datetime DEFAULT NULL,
  `ake_ang_sezst` datetime DEFAULT NULL,
  `akl_ang_sezst` datetime DEFAULT NULL,
  `akn_ang_sezst` datetime DEFAULT NULL,
  `proj_ltg_e_sezst` datetime DEFAULT NULL,
  `vlt_erl_sezst` datetime DEFAULT NULL,
  `vrt_erl_sezst` datetime DEFAULT NULL,
  `ser_sezst` datetime DEFAULT NULL,
  `suee_sezst` datetime DEFAULT NULL,
  `sia_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 3 Angebot';

-- Create syntax for TABLE 'phasen_4_projektierung'
CREATE TABLE IF NOT EXISTS `phasen_4_projektierung` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `pjg_sezst` datetime DEFAULT NULL,
  `pja_sezst` datetime DEFAULT NULL,
  `wsl_sezst` datetime DEFAULT NULL,
  `wsn_sezst` datetime DEFAULT NULL,
  `wsa_sezst` datetime DEFAULT NULL,
  `wse_sezst` datetime DEFAULT NULL,
  `mb_sezst` datetime DEFAULT NULL,
  `sb_sezst` datetime DEFAULT NULL,
  `pe_sezst` datetime DEFAULT NULL,
  `n4an_sezst` datetime DEFAULT NULL,
  `n4al_sezst` datetime DEFAULT NULL,
  `nae_sezst` datetime DEFAULT NULL,
  `fmbm_sezst` datetime DEFAULT NULL,
  `sfs_sezst` datetime DEFAULT NULL,
  `ake_pe_sezst` datetime DEFAULT NULL,
  `akl_pe_sezst` datetime DEFAULT NULL,
  `akn_pe_sezst` datetime DEFAULT NULL,
  `suee_pe_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 4 Projektierung';

-- Create syntax for TABLE 'phasen_5_realisierung'
CREATE TABLE IF NOT EXISTS `phasen_5_realisierung` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `reg_sezst` datetime DEFAULT NULL,
  `rea_sezst` datetime DEFAULT NULL,
  `m61_sezst` datetime DEFAULT NULL,
  `m62_sezst` datetime DEFAULT NULL,
  `m63_sezst` datetime DEFAULT NULL,
  `mg_sezst` datetime DEFAULT NULL,
  `moe_sezst` datetime DEFAULT NULL,
  `rfc_rea_sezst` datetime DEFAULT NULL,
  `nbe_sezst` datetime DEFAULT NULL,
  `tiefb_erl_sezst` datetime DEFAULT NULL,
  `t_mont_mgl_sezst` datetime DEFAULT NULL,
  `t_mont_erl_sezst` datetime DEFAULT NULL,
  `e_mont_erl_sezst` datetime DEFAULT NULL,
  `abstim_mgl_sezst` datetime DEFAULT NULL,
  `vlt_abstim_sezst` datetime DEFAULT NULL,
  `abstim_erl_sezst` datetime DEFAULT NULL,
  `pruef_smk_sezst` datetime DEFAULT NULL,
  `smk_ok_sezst` datetime DEFAULT NULL,
  `hbue_ist_sezst` datetime DEFAULT NULL,
  `erl_ueweg_l_sezst` datetime DEFAULT NULL,
  `bau_aussen_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 5 Realisierung';

-- Create syntax for TABLE 'phasen_6_inbetriebnahme'
CREATE TABLE IF NOT EXISTS `phasen_6_inbetriebnahme` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `rfc_inb_sezst` datetime DEFAULT NULL,
  `sv_sezst` datetime DEFAULT NULL,
  `imm_sezst` datetime DEFAULT NULL,
  `ime_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 6 Inbetriebnahme';

-- Create syntax for TABLE 'phasen_7_dokumentation'
CREATE TABLE IF NOT EXISTS `phasen_7_dokumentation` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `dog_sezst` datetime DEFAULT NULL,
  `dbg_sezst` datetime DEFAULT NULL,
  `doa_sezst` datetime DEFAULT NULL,
  `dok_sezst` datetime DEFAULT NULL,
  `rft_sezst` datetime DEFAULT NULL,
  `qs_sezst` datetime DEFAULT NULL,
  `dic_sezst` datetime DEFAULT NULL,
  `nmv_sezst` datetime DEFAULT NULL,
  `dov_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 7 Dokumentation';

-- Create syntax for TABLE 'phasen_8_abschluss'
CREATE TABLE IF NOT EXISTS `phasen_8_abschluss` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `ab_moeglich` datetime DEFAULT NULL,
  `smn_sezst` datetime DEFAULT NULL,
  `sk_sezst` datetime DEFAULT NULL,
  `fe_sezst` datetime DEFAULT NULL,
  `ab_sezst` datetime DEFAULT NULL,
  `tfnf_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 8 Abschluss';

-- Create syntax for TABLE 'phasen_9_esel'
CREATE TABLE IF NOT EXISTS `phasen_9_esel` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `esl_sezst` datetime DEFAULT NULL,
  `emm_sezst` datetime DEFAULT NULL,
  `eme_sezst` datetime DEFAULT NULL,
  `dem_sezst` datetime DEFAULT NULL,
  `dee_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 9 Esel';

-- Create syntax for TABLE 'phasen_10_gefaehrdet'
CREATE TABLE IF NOT EXISTS `phasen_10_gefaehrdet` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `dog_gefaehrdet` varchar(15) DEFAULT NULL,
  `dbg_gefaehrdet` varchar(15) DEFAULT NULL,
  `dok_gefaehrdet` varchar(15) DEFAULT NULL,
  `qs_gefaehrdet` varchar(15) DEFAULT NULL,
  `dic_gefaehrdet` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 10 Gefaehrdet';

-- Create syntax for TABLE 'phasen_11_sergaktuell'
CREATE TABLE IF NOT EXISTS `phasen_11_sergaktuell` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `serg_aktuell` varchar(25) DEFAULT NULL,
  `serg_aktuell_datum` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 11 Sergaktuell';

-- Create syntax for TABLE 'phasen_12_pslserg'
CREATE TABLE IF NOT EXISTS `phasen_12_pslserg` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `diag_abgeschl` datetime DEFAULT NULL,
  `ab2_sezst` datetime DEFAULT NULL,
  `psl_tabg` varchar(8) NOT NULL DEFAULT 'Nein',
  PRIMARY KEY (`requidbafa`),
  KEY `ab2_sezst` (`ab2_sezst`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 12 Pslserg';

CREATE TABLE IF NOT EXISTS `phasen_13_ausk_status` (
    `requidbafa` char(15) NOT NULL DEFAULT '',
    `ausk_erledigt` tinyint(1) DEFAULT NULL,
    `mitwirkung` tinyint(1) DEFAULT NULL,
     PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen 13 Ausk Status';

-- Create syntax for TABLE 'phasen_laufzeit'
CREATE TABLE IF NOT EXISTS `phasen_2_diagnose_lz` (
`requidbafa` char(15) NOT NULL DEFAULT '',
`eingbbn_der_at` mediumint(9) NOT NULL DEFAULT '0',
PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen Laufzeit';


-- Create syntax for TABLE 'phasen_laufzeit'
CREATE TABLE IF NOT EXISTS `phasen_3_angebot_lz` (
`requidbafa` char(15) NOT NULL DEFAULT '',
`anb_ane_at` mediumint(9) NOT NULL DEFAULT '0',
`anb_anv_at` mediumint(9) NOT NULL DEFAULT '0',
PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen Laufzeit';

-- Create syntax for TABLE 'phasen_laufzeit'
CREATE TABLE IF NOT EXISTS `phasen_4_projektierung_lz` (
`requidbafa` char(15) NOT NULL DEFAULT '',
`pjg_pe_at`  mediumint(9) NOT NULL DEFAULT '0',
`pjg_pja_at` mediumint(9) NOT NULL DEFAULT '0',
`pjg_wsn_at` mediumint(9) NOT NULL DEFAULT '0',
`pjg_wsa_at` mediumint(9) NOT NULL DEFAULT '0',
`wsa_wse_at` mediumint(9) NOT NULL DEFAULT '0',
`fmbm_pe_at` mediumint(9)NOT NULL DEFAULT '0',
`n4an_nae_at` mediumint(9) NOT NULL DEFAULT '0',
`pjg_fmbm_at` mediumint(9) NOT NULL DEFAULT '0',
`pjg_n4an_at` mediumint(9) NOT NULL DEFAULT '0',
PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen Laufzeit';

-- Create syntax for TABLE 'phasen_laufzeit'
CREATE TABLE IF NOT EXISTS `phasen_5_realisierung_lz` (
`requidbafa` char(15) NOT NULL DEFAULT '',
`reg_rfc_at` mediumint(9) NOT NULL DEFAULT '0',
`reg_m61_at` mediumint(9) NOT NULL DEFAULT '0',
`reg_m62_at` mediumint(9) NOT NULL DEFAULT '0',
`reg_m63_at` mediumint(9) NOT NULL DEFAULT '0',
`reg_moe_at` mediumint(9) NOT NULL DEFAULT '0',
`reg_nbe_at` mediumint(9) NOT NULL DEFAULT '0',
PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen Laufzeit';

-- Create syntax for TABLE 'phasen_laufzeit'
CREATE TABLE IF NOT EXISTS `phasen_6_inbetriebnahme_lz` (
`requidbafa` char(15) NOT NULL DEFAULT '',
`rfc_ime_at` mediumint(9) NOT NULL DEFAULT '0',
`rfc_imm_at` mediumint(9) NOT NULL DEFAULT '0',
PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen Laufzeit';

-- Create syntax for TABLE 'phasen_laufzeit'
CREATE TABLE IF NOT EXISTS `phasen_7_dokumentation_lz` (
`requidbafa` char(15) NOT NULL DEFAULT '',
`dog_qs_at` mediumint(9) NOT NULL DEFAULT '0',
`dog_dbg_at` mediumint(9) NOT NULL DEFAULT '0',
PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Phasen Laufzeit';


-- Create syntax for TABLE 'basis_1_wms_psl'
CREATE TABLE IF NOT EXISTS `basis_1_wms_psl` (
  `id` int NOT NULL AUTO_INCREMENT,
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `t_glb_orge_sk` smallint(6) DEFAULT NULL,
  `beteiligte_orge` char(30) DEFAULT NULL,
  `sma_verbunden` bigint(12) unsigned zerofill NOT NULL,
  `refidba` char(15) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `requidbafa` (`requidbafa`),
  KEY `sma_verbunden` (`sma_verbunden`),
  KEY `beteiligte_orge` (`beteiligte_orge`),
  KEY `t_glb_orge_sk` (`t_glb_orge_sk`),
  KEY `refidba` (`refidba`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TEST grundlegende Verknüpfungsdaten für Beast';

-- Create syntax for TABLE 'ueweg_1_allgemein'
CREATE TABLE IF NOT EXISTS `ueweg_1_allgemein` (
  `requidbafa` char(15) NOT NULL COMMENT 'Request-ID',
  `container` varchar(30) DEFAULT NULL COMMENT 'Container',
  `container_bng` varchar(30) DEFAULT NULL COMMENT 'Container BNG',
  `endstelle` varchar(10) DEFAULT NULL COMMENT 'Endstelle',
  `tn_id` bigint(11) DEFAULT NULL COMMENT 'TN-ID',
  `dcip` char(4) DEFAULT NULL COMMENT 'DCIP',
  `Kom_Index` varchar(50) NOT NULL,
  `dcip_int` tinyint(1) NOT NULL,
  `cluster` tinyint(2) NOT NULL,
  PRIMARY KEY (`requidbafa`),
  KEY `tn_id` (`tn_id`),
  KEY `dcip` (`dcip`),
  KEY `Kom_Index` (`Kom_Index`),
  KEY `requidbafa` (`requidbafa`, `dcip_int`,`tn_id`),
  KEY `tn_id_2` (`tn_id`,`dcip_int`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `fin_1_berechnet` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `plankosten_gesamt` decimal(12,2) DEFAULT NULL,
  `istkosten_gesamt` decimal(12,2) DEFAULT NULL,
  `plankosten_1` decimal(12,2) DEFAULT NULL,
  `istkosten_1` decimal(12,2) DEFAULT NULL,
  `verfuegt_1` decimal(12,2) DEFAULT NULL,
  `auftragsrestplan_1` decimal(12,2) DEFAULT NULL,
  `plankosten_ueberschritten` varchar(4) DEFAULT NULL,
  `plan_verfuegt_1` decimal(12,4) DEFAULT NULL,
  `plan_ist_1` decimal(12,4) DEFAULT NULL,
  `plan_ist_2` decimal(12,4) DEFAULT NULL,
  `ael_quote` decimal(12,4) DEFAULT NULL,
  `obligo_gesamt` decimal(12,2) NOT NULL,
  `inv_plan` decimal(12,2) NOT NULL,
  `inv_ist` decimal(12,2) NOT NULL,
  `kost_plan` decimal(12,2) NOT NULL,
  `kost_ist` decimal(12,2) NOT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST Finanzdaten';

CREATE TABLE IF NOT EXISTS `dwh_1_wms` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `produktion_abgeschlossen` datetime DEFAULT NULL,
  `auftragsende` datetime DEFAULT NULL,
  `erste_sollterminverschiebung` datetime DEFAULT NULL,
  `aktuelle_sollterminverschiebung` datetime DEFAULT NULL,
  `produktion_steht_seit` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST DWH Daten';

CREATE TABLE IF NOT EXISTS `mega_1_techabschl` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `technabschl_megaplan` datetime DEFAULT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST - Megaplandaten';

CREATE TABLE IF NOT EXISTS `gf_1_allgemein` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `storno_gf` varchar(4) NOT NULL,
  `summe_gf_plan` float NOT NULL DEFAULT '0',
  `summe_gf_ist` float NOT NULL DEFAULT '0',
  `alle_gf_pgz_plan` varchar(255) not NULL,
  `alle_gf_pgz_ist` varchar(255) not NULL,
  `l1_02b_plan` float NOT NULL DEFAULT '0',
  `l1_04b_oder_r_plan` float NOT NULL DEFAULT '0',
  `l1_08a_oder_r_plan` float NOT NULL DEFAULT '0',
  `l1_10a_oder_r_plan` float NOT NULL DEFAULT '0',
  `l1_10c_plan` float NOT NULL DEFAULT '0',
  `l1_05cdrs_plan` float NOT NULL DEFAULT '0',
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Beast Geschäftsfalldaten';

CREATE TABLE IF NOT EXISTS `gf_2_detail` (
    `aenderungsdatum` datetime NOT NULL,
    `sma_gf` bigint(12) unsigned zerofill NOT NULL,
    `durchf_produktives_ressort_id` int(11) unsigned NOT NULL,
    `durchf_produktives_ressort_text_mittel` varchar(40) NOT NULL,
    `gf` char(6) NOT NULL,
    `pgz` char(2) NOT NULL,
    `gf_menge_beauftragt` float NOT NULL DEFAULT '0',
    `gf_menge_geplant` float NOT NULL DEFAULT '0',
    `gf_menge_ist` float NOT NULL DEFAULT '0',
    `gf_menge_0` tinyint(1) NOT NULL DEFAULT 0,
    `gf_pgz` VARCHAR(255) NOT NULL DEFAULT '',
    PRIMARY KEY (`sma_gf`,`durchf_produktives_ressort_id`,`gf`,`pgz`),
    KEY `gf_pgz_index` (`gf_pgz`),
    KEY `sma_gf` (`sma_gf`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `bzm_basis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `bzm_id` int(12) NOT NULL,
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  primary key (`id`),
  UNIQUE KEY (`bzm_id`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `bzm_1_detail` (
  `id` int(11) NOT NULL,
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `kreditor` varchar(10) NOT NULL,
  `gewerk` varchar(100) NOT NULL,
  `art` int(11) NOT NULL,
  `menge` int(11) NOT NULL ,
  `zeitstempel` datetime NOT NULL,
  `letze_erfassung` tinyint(1) not null,
  `soll_beginn` datetime NOT NULL,
  `soll_ende` datetime NOT NULL,
  primary key (`id`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `bzm_2_summen` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `tiefbau_ist_start_termin` datetime NOT NULL,
  `oberirdisch_ist_start_termin` datetime NOT NULL,
  `kabelzug_ist_start_termin` datetime NOT NULL,
  `gehause_ist_start_termin` datetime NOT NULL,
  `onebox_ist_start_termin` datetime NOT NULL,
  `tiefbau_soll_start_termin` datetime NOT NULL,
  `oberirdisch_soll_start_termin` datetime NOT NULL,
  `kabelzug_soll_start_termin` datetime NOT NULL,
  `gehause_soll_start_termin` datetime NOT NULL,
  `onebox_soll_start_termin` datetime NOT NULL,
  `tiefbau_tage_mit_sollterminanpassung` int(11) NOT NULL,
  `oberirdisch_tage_mit_sollterminanpassung` int(11) NOT NULL,
  `kabelzug_tage_mit_sollterminanpassung` int(11) NOT NULL,
  `gehause_tage_mit_sollterminanpassung` int(11) NOT NULL,
  `onebox_tage_mit_sollterminanpassung` int(11) NOT NULL,
  `rotberichtigung_tage_mit_sollterminanpassung` int(11) NOT NULL,
  `tiefbau_tage_mit_erfassung` int(11) NOT NULL,
  `oberirdisch_tage_mit_erfassung` int(11) NOT NULL,
  `kabelzug_tage_mit_erfassung` int(11) NOT NULL,
  `gehause_tage_mit_erfassung` int(11) NOT NULL,
  `onebox_tage_mit_erfassung` int(11) NOT NULL,
  `tiefbau_ist_menge_gesamt` int(11) NOT NULL,
  `oberirdisch_ist_menge_gesamt` int(11) NOT NULL,
  `kabelzug_ist_menge_gesamt` int(11) NOT NULL,
  `gehause_ist_menge_gesamt` int(11) NOT NULL,
  `onebox_ist_menge_gesamt` int(11) NOT NULL,
  `rotberichtigung_ist_menge_gesamt` int(11) NOT NULL,
  `tiefbau_ist_ende_termin` datetime NOT NULL,
  `oberirdisch_ist_ende_termin` datetime NOT NULL,
  `kabelzug_ist_ende_termin` datetime NOT NULL,
  `gehause_ist_ende_termin` datetime NOT NULL,
  `onebox_ist_ende_termin` datetime NOT NULL,
  `rotberichtigung_ist_ende_termin` datetime NOT NULL,
  `tiefbau_soll_ende_termin` datetime NOT NULL,
  `oberirdisch_soll_ende_termin` datetime NOT NULL,
  `kabelzug_soll_ende_termin` datetime NOT NULL,
  `gehause_soll_ende_termin` datetime NOT NULL,
  `onebox_soll_ende_termin` datetime NOT NULL,
  `rotberichtigung_soll_ende_termin` datetime NOT NULL,
  `offene_bauweise_mit_oberflaeche_ist_menge` int(11) NOT NULL,
  `offene_bauweise_ohne_oberflaeche_ist_menge` int(11) NOT NULL,
  `spuehlbohren_ist_menge` int(11) NOT NULL,
  `kabelpflug_ist_menge` int(11) NOT NULL,
  `trenching_ist_menge` int(11) NOT NULL,
  `nvt_ist_menge` int(11) NOT NULL,
  `mfg_ist_menge` int(11) NOT NULL,
  `kabel_ist_menge` int(11) NOT NULL,
  `rohr_ist_menge` int(11) NOT NULL,
  `one_box_ist_menge` int(11) NOT NULL,
  `mindertiefe_ist_menge` int(11) NOT NULL,
  `kreditor` varchar(255) NOT NULL,
  `firmenname` varchar(255) NOT NULL,
  primary key (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `fr_1_wms` (
  `requidbafa` char(15) NOT NULL,
  `vh_abschl_bis` datetime NOT NULL,
  `verbl_ktage` int(11) NOT NULL,
  `fr_bewertet` tinyint(2) NOT NULL COMMENT 'Durch Referenz',
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `fr_2_psl` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  `z1_60_nachlauf_kt` tinyint(1) NOT NULL,
  `lt_alt_frist` tinyint(1) NOT NULL,
  `lt_aip_frist` tinyint(1) NOT NULL,
  `st_alt_frist` tinyint(1) NOT NULL,
  `st_aip_frist` tinyint(1) NOT NULL,
  `lt_frist_14` tinyint(1) NOT NULL,
  `lt_stoe_frist` tinyint(1) NOT NULL,
  `z1_60_14` tinyint(1) NOT NULL,
  `z1_60_bartl` tinyint(1) NOT NULL,
  `z1_60_tabg28` tinyint(1) NOT NULL,
  `z1_60_tabg90` tinyint(1) NOT NULL,
  `frei_z1_60` tinyint(1) NOT NULL,
  `betr_korrig14_z1_60` tinyint(1) NOT NULL,
  `z1_60_fehlt` tinyint(1) NOT NULL,
  `z1_80_fehlt` tinyint(1) NOT NULL,
  `tabg_fehlt` tinyint(1) NOT NULL,
  `z1_60_tf` tinyint(1) NOT NULL,
  `restlz_2kt` tinyint(1) NOT NULL,
  `a1_erof_1at` tinyint(1) NOT NULL,
  `z1_70_ohne_z1_60` tinyint(1) NOT NULL,
  PRIMARY KEY (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `nap_1_analyse` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `nutzung_ap` tinyint(1) DEFAULT NULL,
  `ae_der` tinyint(1) DEFAULT NULL,
  `pe_tf` tinyint(1) DEFAULT NULL,
  `rfc_tf` tinyint(1) DEFAULT NULL,
  `qs_tf` tinyint(1) DEFAULT NULL,
  `moe_dbg` tinyint(1) DEFAULT NULL,
  `phasenreihe_ist` varchar(30) DEFAULT NULL,
  `phasenreihe_soll` varchar(30) DEFAULT NULL,
  `phasenfehler` tinyint(1) DEFAULT NULL,
  `nutzung_ap_sf` tinyint(1) DEFAULT NULL,
  `ap_monat` varchar(14) DEFAULT NULL,
  `ap_gruppen_id` tinyint(3) DEFAULT '0',
  PRIMARY KEY (`requidbafa`),
  KEY `nutzung_ap` (`nutzung_ap`),
  KEY `ap_monat` (`ap_monat`),
  KEY `ae_der` (`ae_der`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BEAST - Nutzung Arbeitspläne Daten';

CREATE TABLE IF NOT EXISTS `vrt_termin` (
  `requidbafa` char(15) NOT NULL,
  `1_vrt` datetime DEFAULT NULL,
  `vrt` datetime DEFAULT NULL,
  `rea_fertig_bis` datetime DEFAULT NULL,
  `kask_status` varchar(30) DEFAULT NULL,
  `kask_eingbbndatum` datetime DEFAULT NULL,
  `kask_lz_verbr_at` int(11) NOT NULL DEFAULT '0',
  `kask_lz_rest_at` int(11) NOT NULL DEFAULT '0',
  `kask_lz_verbr_at_proz` decimal(12,4) DEFAULT NULL,
  `kask_tfnf_sezst` datetime DEFAULT NULL,
  PRIMARY KEY (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Vrt termin';

CREATE TABLE IF NOT EXISTS `b2b_zele_basis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `aa_id` char(15) NOT NULL,
  `ref_ba` char(15) NOT NULL,
  `sm_auftrag` varchar(30) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `aa_id` (`aa_id`),
  KEY `aa_id_index` (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='b2b Zele Basis';

CREATE TABLE IF NOT EXISTS `b2b_1_allgemein` (
  `aa_id` char(15) NOT NULL,
  `relevanz_aa` varchar(30) NOT NULL,
  `status` varchar(128) NOT NULL,
  `storno` tinyint(1) DEFAULT NULL,
  `standard_aa` varchar(80) NOT NULL,
  `teilauftragsfall_ti` varchar(60) NOT NULL,
  `referenz_nr_tf` varchar(15) NOT NULL,
  `ressort_typ` varchar(50) NOT NULL,
  `fs_folgesystem` varchar(15) NOT NULL,
  `fsfl_vg_verknuepft` tinyint(1) DEFAULT NULL,
  `fs_techn_angek` datetime NOT NULL,
  `zugewiesen_an_nl` varchar(255) NOT NULL,
  `zugewisesen_an_ressort` varchar(50) NOT NULL,
  `fs_auftragsnr` varchar(30) NOT NULL,
  `fs_auftragskurzbeschreibung` varchar(255) NOT NULL,
  PRIMARY KEY (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='b2b 1 Allgemein';

CREATE TABLE IF NOT EXISTS `b2b_2_termine` (
  `aa_id` char(15) NOT NULL,
  `ist_beginntermin_aa` datetime NOT NULL,
  `ist_ende_termin_aa` datetime NOT NULL,
  `soll_ende_termin_aa` datetime NOT NULL,
  `spaet_sollend` datetime NOT NULL,
  `timestamp_tf` datetime NOT NULL,
  `timestamp_zw` datetime NOT NULL,
  `anderungsdatum` datetime NOT NULL,
  `soll_ende_aus_fsfa` date default null,
  PRIMARY KEY (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='b2b 2 Termine';


CREATE TABLE IF NOT EXISTS `b2b_3_rv` (
  `aa_id` char(15) NOT NULL,
  `abrufnummer` varchar(30) NOT NULL,
  `rvertragsnr` varchar(10) NOT NULL,
  `rahmenvertragsposition` varchar(6) default 'n.v.',
  `rv_positionsname` varchar(255) default 'n.v.',
  `kreditorenname` varchar(255) default 'n.v.',
  `kreditorennummer` varchar(10) default 'n.v.',
  `kopplung_rv_ba` tinyint(1) default 0,
  PRIMARY KEY (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='b2b 3 Rahmenvertrag ';


CREATE TABLE IF NOT EXISTS `bbt_basis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `bbk_id` int(9) NOT NULL,
  `wmsti_id` varchar(15) NOT NULL,
  `nlpti` varchar(110) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY (`bbk_id`),
  KEY `wmsti_id` (`wmsti_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='bbt basis';

CREATE TABLE IF NOT EXISTS `bbt_baustelle` (
  `bbk_id` int(9) NOT NULL,
  `baustelle_plz` varchar(100) NOT NULL,
  `baustelle_ort` varchar(100) NOT NULL,
  `baustelle_strasse` varchar(100) NOT NULL,
  `baustelle_hausnr` varchar(100) NOT NULL,
  `baustelle_hausnr_zus` varchar(10) NOT NULL,
  PRIMARY KEY (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='bbt baustelle';

CREATE TABLE IF NOT EXISTS `bbt_termine` (
  `bbk_id` int(9) NOT NULL,
  `termin_steuerung` datetime NOT NULL,
  `termin_geplant` datetime NOT NULL,
  `baureife_ist` datetime NOT NULL,
  `baureife_sol` datetime NOT NULL,
  `buchbarkeit_hergestellt_ist` datetime NOT NULL,
  `zeitpunkt_storno_kunde` datetime NOT NULL,
  `zeitpunkt_storno_manuell` datetime NOT NULL,
  `zeitpunkt_storno_wmsti` datetime NOT NULL,
  `zeitpunkt_storno_nichtausbau` datetime NOT NULL,
   `auftragsentwurf_ist` datetime NOT NULL,
  `auftragserfassung_ist` datetime NOT NULL,
  `unterlagen_versandt_ist` datetime NOT NULL,
  `unterlagen_gescannt_ist` datetime NOT NULL,
  `unterlagen_validiert_ist` datetime NOT NULL,
  `technik_beauftragt_ist` datetime NOT NULL,
  `projektiert_ist` datetime NOT NULL,
  `tiefbau_beauftragt_ist` datetime NOT NULL,
  `apl_montiert_ist` datetime NOT NULL,
  `gf_netzanbindung_herg_ist` datetime NOT NULL,
  `produkt_gebucht_ist` datetime NOT NULL,
  `fakturierung_erfolgt_ist` datetime NOT NULL,
  `termin_wmsti` datetime NOT NULL,
  `termin_koordination` datetime NOT NULL,
  PRIMARY KEY (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='bbt termine';

CREATE TABLE IF NOT EXISTS `bbt_allgemein` (
  `bbk_id` int(9) NOT NULL,
  `kls_id` bigint(12) NOT NULL,
  `kundenanliegen` varchar(50) NOT NULL,
  `kritischer_korb_grund` varchar(100) NOT NULL,
  `uebergabe_kritisch_zeitpunkt` datetime NOT NULL,
  `workflow_version_mit_baureife` tinyint(1) DEFAULT '0',
  `kontakt_status_id` varchar(50) NOT NULL,
  `kontakt_status_value` varchar(50) NOT NULL,
  `eingangskanal` varchar(60) NOT NULL,
  `t_prod_bedanz_jn` tinyint(1) DEFAULT '0',
  `anzahl_we` int(10) NOT NULL,
  `anzahl_ge` int(10) NOT NULL,
  `anzahl_sonderleitungen` int(10) NOT NULL,
  `apl_innen_jn` tinyint(1) DEFAULT '0',
  `wiederbebauung_id` tinyint(1) DEFAULT '0',
  `baumassnahme` varchar(30) NOT NULL,
  `baumassnahme_detail` varchar(50) NOT NULL,
  `ks_team` varchar(40) NOT NULL,
  `gebietskenner_id` varchar(10) NOT NULL,
  `ne4_auskundung_id` tinyint(1) DEFAULT '0',
  `koordinierung_energieversorger` tinyint(1) DEFAULT '0',
  `tiefbauunternehmen` varchar(200) NOT NULL,
  PRIMARY KEY (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='bbt baustelle';

CREATE TABLE IF NOT EXISTS `nap_1_processing_bafa`(
  `bafa` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`bafa`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_bafa` (
  `bafa` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`bafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_bafa` (
 `bafa` char(15) NOT NULL DEFAULT '',
 PRIMARY KEY (`bafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ba` (
 `ba` char(15) NOT NULL DEFAULT '',
 PRIMARY KEY (`ba`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_bafa_vrt` (
  `bafa` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`bafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_arbeitsauftrags_id` (
  `aa_id` char(15) NOT NULL,
  PRIMARY KEY (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_arbeitsauftrags_id` (
   `aa_id` char(15) NOT NULL,
   PRIMARY KEY (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_gruppe` (
  `gruppe` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`gruppe`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `processing_bafa_complete_without_agbs_tagbs_pabgs` (
  `bafa` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`bafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_bafa_complete_without_agbs_pabgs` (
  `bafa` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`bafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_bafa_by_sma` (
  `bafa` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`bafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_sma` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  PRIMARY KEY (`sma_psl`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_sma` (
    `sma_psl` bigint(12) unsigned zerofill NOT NULL,
    PRIMARY KEY (`sma_psl`),
    KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_bzm_sma` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  PRIMARY KEY (`sma_psl`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_bzm_sma` (
    `sma_psl` bigint(12) unsigned zerofill NOT NULL,
    PRIMARY KEY (`sma_psl`),
    KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_delta_sma` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  PRIMARY KEY (`sma_psl`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_delta_sma` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  PRIMARY KEY (`sma_psl`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_delta_sma_finanz` (
  `sma_psl` bigint(12) unsigned zerofill NOT NULL,
  PRIMARY KEY (`sma_psl`),
  KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_delta_sma_finanz` (
 `sma_psl` bigint(12) unsigned zerofill NOT NULL,
 PRIMARY KEY (`sma_psl`),
 KEY `sma_psl` (`sma_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_bbk_id` (
  `bbk_id` int(9) NOT NULL,
  PRIMARY KEY (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `manual_processing_bbk_id` (
    `bbk_id` int(9) NOT NULL,
    PRIMARY KEY (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_trommelbeweg` (
    `matbeleg_nr` varchar(10) NOT NULL,
    `matbeleg_jahr` smallint(2) unsigned NOT NULL,
    `matbeleg_pos` smallint(2)  NOT NULL,
    `bw_zeilen_id` mediumint(9) NOT NULL,
    `best_material` VARCHAR(18) NOT NULL,
    `chargen_nr` VARCHAR(10) NOT NULL,
    PRIMARY KEY (`best_material`,`chargen_nr`,`matbeleg_nr`,`matbeleg_jahr`,`matbeleg_pos`,`bw_zeilen_id`),
    KEY `trommelbeweg_index` (`best_material`,`chargen_nr`,`matbeleg_nr`,`matbeleg_jahr`,`matbeleg_pos`,`bw_zeilen_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_matcharge` (
    `t_gueltig_von` DATETIME,
    `chargen_nr` VARCHAR(10) NOT NULL,
    `werk`VARCHAR(4) NOT NULL,
    `material_nr` VARCHAR(18) NOT NULL,
    PRIMARY KEY (`t_gueltig_von`,`material_nr`,`werk`,`chargen_nr`),
    KEY `matcharge_index` (`t_gueltig_von`, `material_nr`, `werk`, `chargen_nr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_sma_trommel` (
    `trom_sma` bigint(12) unsigned zerofill NOT NULL,
    PRIMARY KEY (`trom_sma`),
    KEY `trom_sma` (`trom_sma`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE  IF NOT EXISTS `processing_check_5_kommentar_index` (
    `kom_index`varchar(50) NOT NULL,
    primary key (`kom_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_check_6_bbk_id` (
    `bbk_id` int(9) NOT NULL,
    primary key (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_abschluss_gruppe` (
    `abschluss_gruppe` char(15) NOT NULL DEFAULT '',
    PRIMARY KEY (`abschluss_gruppe`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `processing_ggs_fol_3_ausbau_status` (
    `fol_id` bigint(12) unsigned NOT NULL,
    PRIMARY KEY (`fol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS processing_kls
(
    `kls_id` bigint unsigned not null,
    primary key (kls_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS manual_processing_kls
(
    `kls_id` bigint unsigned not null,
    primary key (kls_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_gbgs_cio` (
    `ba_nr_intern` bigint unsigned NOT NULL,
    primary key (`ba_nr_intern`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_dtag_cio` (
     `ba_nr_intern` bigint unsigned NOT NULL,
     primary key (`ba_nr_intern`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_gfp_rop_cio` (
     `ba_nr_intern` bigint unsigned NOT NULL,
     primary key (`ba_nr_intern`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_bauaufgabe` (
    `fol_id` bigint(12) unsigned NOT NULL,
    primary key (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_dtag_bauaufgabe` (
    `fol_id` bigint(12) unsigned NOT NULL,
    primary key (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_gfp_rop_bauaufgabe` (
   `fol_id` bigint(12) unsigned NOT NULL,
   primary key (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_gbgs_bulk` (
    `bulk_order_id`               int unsigned not null,
    PRIMARY KEY (`bulk_order_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_dtag_bulk` (
      `bulk_order_id`               int unsigned not null,
      PRIMARY KEY (`bulk_order_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_gfp_rop_bulk` (
      `bulk_order_id`               int unsigned not null,
      PRIMARY KEY (`bulk_order_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS processing_check_8 (
    `auftragsnrag` varchar(50),
    PRIMARY KEY (auftragsnrag)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_wfm_ggs_1_area_kaskade` (
    `requidbafa` varchar(15) NOT NULL,
    primary key (`requidbafa`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_wfm_ggs_1_area_id_a` (
   `kls_id_a` bigint unsigned NOT NULL,
   `area_nr_klsid_a` varchar(50),
   `area_name_klsid_a` varchar(150),
   primary key (`kls_id_a`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_wfm_ggs_1_area_id_mont_a` (
   `kls_id_mont_a` bigint unsigned not null,
   `area_nr_mont_a` varchar(50) NOT NULL,
   `area_name_mont_a` varchar(150),
   primary key (`kls_id_mont_a`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_wfm_ggs_2_nvt_area` (
    `requidbafa` varchar(15) NOT NULL,
    primary key (`requidbafa`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_wfmt_betr_korr_del` (
    `requidbafa` varchar(15) NOT NULL,
    primary key (`requidbafa`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_basis_1_fol` (
    `fol_id` bigint(12) unsigned NOT NULL,
    primary key (`fol_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_ggs_nvt_area_1` (
    `nvt_area` varchar(255),
    primary key (`nvt_area`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_gbgs_gfap` (
    `fol_id` bigint(12) unsigned NOT NULL,
    primary key (`fol_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `processing_dbbgl_2_temp`(
    `sma` bigint(12) unsigned zerofill NOT NULL,
    PRIMARY KEY (`sma`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `betr_korr_1_historie`(
  `requidbafa` char(15) NOT NULL ,
  `hist_betriebsber_korr` DATETIME NULL,
  `aenderungsdatum` DATETIME NOT NULL,
  `anpass_um` INT,
  `anpass_nach` INT,
  `quelle` TINYINT(1),
  `verzoegerungsgrund`varchar(69),
  `vzg_art` varchar(13),
  `vzg_schluessel` varchar(10),
  `vzg_ressort` varchar(30),
  `bemerkung` varchar(1000),
  `vz_empfang` INT,

  primary key (`requidbafa`,`aenderungsdatum`,`quelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `betr_korr_2_auftrag`(
  `requidbafa` char(15) NOT NULL ,
  `erster_betriebsber_korr` DATETIME NOT NULL,
  `erstes_aenderungsdatum` DATETIME NOT NULL,
  `letztes_aenderungsdatum` DATETIME NOT NULL,
  `letzte_anpass_um` INT,
  `letzte_anpass_nach` INT,
  `anzahl` TINYINT(1),
  primary key (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `betr_korr_3_1vzg` (
  `requidbafa` varchar(15) NOT NULL,
  `aenderungsdatum` DATETIME NOT NULL,
  `1_betriebsber_korr_vzg` DATETIME,
  `verzoegerungsgrund`varchar(69),
  `vzg_art` varchar(13),
  `vzg_schluessel` varchar(10),
  `vzg_ressort` varchar(30),
  `bemerkung` varchar(1000),
  `ident_nr` INT,
  primary key (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `constants` (
    `name` char(255) NOT NULL ,
    `value` char(255) NOT NULL,
    primary key (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `kollo_basis` (
    `kollo_auftragsnr` int(7) NOT NULL,
    `kollo_reqid` char(15) NOT NULL,
    `kollo_auftragsart` int(1)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `kollo_offene_auftr` (
    `kollo_auftragsnr` int(7) NOT NULL,
    `phase` varchar(255),
    `status` varchar(255),
    `t_iaa` varchar(255),
    `produktklassen` varchar(255),
    `beteiligung` varchar(255),
    `pti_soll` datetime,
    `pti_ist` datetime,
    `stoe_eingang` datetime,
    `verzoeg_grund` varchar(255),
    `wmsti_nr_angebot` varchar(255),
    `wmsti_nr_auftrag` varchar(255),
    `sma_uetech` varchar(255),
    `sma_ltech` varchar(255),
    primary key (`kollo_auftragsnr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `prod_steht_1` (
    `requidbafa` char(15) NOT NULL,
    `produktion_steht_seit` DATETIME,
    `produktion_unterbr_seit` DATETIME,
    primary key (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `bbt_akt_aktiv` (
       `bbk_id` int(9) NOT NULL,
       `rr_wunsch_tech` datetime,
       `wichtig_hinweis_tech` datetime,
       `bedarfstermin` datetime,
       `quelle_bedarfterm` varchar(50),
       `termin_vor_ort_geaend` datetime,
       `hinweis_vorgangs_check` datetime,
       `rr_wunsch_tech_txt`text NULL DEFAULT NULL,
       `wichtig_hinweis_tech_txt`text NULL DEFAULT NULL,
       `bedarfstermin_txt`text NULL DEFAULT NULL,
       `termin_vor_ort_geaend_txt`text NULL DEFAULT NULL,
       `hinweis_vorgangs_check_txt`text NULL DEFAULT NULL,
       primary key (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `trom_1_bewegt` (
    `trom_id` VARCHAR(38) NOT NULL,
    `trom_typ` VARCHAR(18) NOT NULL,
    `trom_nr` VARCHAR(10) NOT NULL,
    `werk` CHAR(4) NOT NULL,
    `matbeleg_nr` varchar(10) NOT NULL,
    `matbeleg_jahr` smallint(2) unsigned NOT NULL,
    `matbeleg_pos` smallint(2)  NOT NULL,
    `bw_zeilen_id` smallint(3) NOT NULL,
    `bewegungsart` CHAR(3) NOT NULL,
    `buchungsdatum` datetime,
    `sma` BIGINT(12),
    `t_ladedatum` datetime NOT NULL,
    PRIMARY KEY (`trom_typ`,`trom_nr`,`matbeleg_nr`,`matbeleg_jahr`,`matbeleg_pos`,`bw_zeilen_id`),
    key `trom_index` (`trom_typ`,`trom_nr`,`matbeleg_nr`,`matbeleg_jahr`,`matbeleg_pos`,`bw_zeilen_id`),
    key `trom_id` (`trom_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `basis_1_gruppe_sma` (
  `gruppe` char(15) NOT NULL DEFAULT '',
  `sma_verbunden` bigint(12) unsigned zerofill NOT NULL,
  `abschluss_gruppe` char(20) NOT NULL,
  `rekursion` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`gruppe`, `sma_verbunden`),
  key `abschluss_gruppe` (`abschluss_gruppe`),
  key `sma_verbunden` (`sma_verbunden`)
) ENGINE=InnoDB AUTO_INCREMENT=44508225 DEFAULT CHARSET=utf8 COMMENT='TEST grundlegende Verknüpfungsdaten für Beast';

CREATE TABLE IF NOT EXISTS `trom_2_verlust` (
    `trom_id` VARCHAR(40) NOT NULL,
    `trom_typ` VARCHAR(18) NOT NULL,
    `trom_nr` VARCHAR(10) NOT NULL,
    `werk` VARCHAR(4) NOT NULL,
    `status_verlust` char(1) NOT NULL,
    `datum_verlust` datetime NOT NULL,
    `loeschvormerkung_charge_werk` char(1),
    `dat_letzt_aenderung` datetime,
    `t_gueltig_von` datetime,
    `t_gueltig_bis` datetime,
    primary key(`trom_id`),
    key `trom_id` (`trom_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `trom_basis` (
    `id` INT AUTO_INCREMENT NOT NULL,
    `trom_id` VARCHAR(40) NOT NULL,
    `trom_typ` VARCHAR(18) NOT NULL,
    `trom_nr` VARCHAR(10) NOT NULL,
    `werk` VARCHAR(4) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `trom_id` (`trom_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `check_1_wfmt` (
    `requidbafa` char(15) NOT NULL ,
    `abschluss_jahr` varchar(4) ,
    `in_basis_1_wms_psl` tinyint(1) NOT NULL DEFAULT 0,
    `gruppe` char(15) NOT NULL,
    `abschluss_gruppe` varchar(4),
    `grp_in_basis_1_wms_psl` tinyint(1) NOT NULL DEFAULT 0,
    primary key (`requidbafa`),
    key `gruppe` (`gruppe`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `check_2_sma`(
    `sm_auftragsnummer` bigint(12) unsigned zerofill NOT NULL,
    `abschluss_jahr` varchar(4),
    `in_basis_1_wms_psl` tinyint(1) NOT NULL DEFAULT 0,
    `systemstatus`char(4) NOT NULL,
    `obligo` decimal(12,2) NOT NULL ,
    `abschluss_jahr_komplett` varchar(4),
    PRIMARY KEY (`sm_auftragsnummer`),
    key `in_basis_1_wms_psl` (`in_basis_1_wms_psl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `check_3_bafa_sma`(
    `requidbafa` char(15) NOT NULL,
    `gruppe` char(15) NOT NULL,
    `abschluss_gruppe` varchar(4),
    `grp_in_basis_1_wms_psl` tinyint(1) NOT NULL DEFAULT 0,
    `grp_zu_loeschen` char(15) NOT NULL,
    `loeschen_ohne_sma` tinyint(1) NOT NULL DEFAULT 0,
    `loeschen_ohne_zr` tinyint(1) NOT NULL DEFAULT 0,
    `loeschen` tinyint(1) NOT NULL DEFAULT 0,
    `rekursion` int NOT NULL DEFAULT 0,
    primary key (`requidbafa`),
    key `grp_zu_loeschen` (`grp_zu_loeschen`),
    key `gruppe` (`gruppe`),
    key `loeschen` (`loeschen`),
    key `loeschen_ohne_zr` (`loeschen_ohne_zr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `check_4_sma_bafa`(
    `sma` bigint(12) unsigned zerofill NOT NULL,
    `in_basis_1_wms_psl` tinyint(1) NOT NULL DEFAULT 0,
    `systemstatus`char(4) NOT NULL,
    `abschluss_jahr_komplett` varchar(4),
    `grp_zu_loeschen` char(15) NOT NULL,
    `loeschen_ohne_sma` tinyint(1) NOT NULL DEFAULT 0,
    `loeschen_ohne_zr` tinyint(1) NOT NULL DEFAULT 0,
    `loeschen` tinyint(1) NOT NULL DEFAULT 0,
    `rekursion` int NOT NULL DEFAULT 0,
    PRIMARY KEY (`sma`),
    key `grp_zu_loeschen` (`grp_zu_loeschen`),
    key `loeschen` (`loeschen`),
    key `loeschen_ohne_zr` (`loeschen_ohne_zr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `check_5_kommentar_index` (
    `requidbafa` char(15) NOT NULL,
    `kom_index` varchar(50) NOT NULL,
    `auftr_loeschen` tinyint(1) NOT NULL DEFAULT 0,
    `loeschen` tinyint(1) NOT NULL DEFAULT 0,
    primary key (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE  TABLE IF NOT EXISTS `check_6_bbk_id` (
    `bbk_id` int(9) NOT NULL,
    `requidbafa` char(15) NOT NULL,
    `requidbafa_ok` tinyint(1) NOT NULL DEFAULT 0,
    `requidbafa_del` tinyint(1) NOT NULL DEFAULT 0,
    `bbk_id_auftr` tinyint(1) NOT NULL DEFAULT 0,
    `status_e_a` VARCHAR(1) NOT NULL DEFAULT "",
    `letzt_erstellungsdatum` DATETIME,
    `loeschen` tinyint(1) NOT NULL DEFAULT 0,
    `pruefen` tinyint(1) NOT NULL DEFAULT 0,
    primary key (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create syntax for TABLE 'basis_1_wms_psl'
CREATE TABLE IF NOT EXISTS `check_7_basis_1_wfm_psl` (
  `requidbafa` char(15) NOT NULL DEFAULT '',
  `sma_verbunden_original` bigint(12) unsigned zerofill NOT NULL,
  `sma_verbunden` bigint(12) unsigned zerofill NULL,
  `sma_verb_ohne_sum` bigint(12) unsigned zerofill NULL,
  `sma_verb_nur_sum` bigint(12) unsigned zerofill NULL,
  `sma_verb_ohne_zr` bigint(12) unsigned zerofill NULL,
  `sma_verb_nur_zr` bigint(12) unsigned zerofill NULL,
  UNIQUE KEY `requidbafa` (`requidbafa`),
  KEY `sma_verbunden` (`sma_verbunden`),
  KEY `sma_verb_ohne_sum` (`sma_verb_ohne_sum`),
  KEY `sma_verb_nur_sum` (`sma_verb_nur_sum`),
  KEY `sma_verb_ohne_zr` (`sma_verb_ohne_zr`),
  KEY `sma_verb_nur_zr` (`sma_verb_nur_zr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TEST grundlegende Verknüpfungsdaten für Beast';

CREATE TABLE IF NOT EXISTS `check_9_wfmt_status` (
    `check_9_requidbafa` char(15) NOT NULL,
    `check_9_refidba` char(15) NOT NULL,
    `check_9_datum` DATETIME NOT NULL,
    UNIQUE KEY `check_9_requidbafa` (`check_9_requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `check_9_wfmt_status_valid` (
   `check_9_requidbafa` char(15) NOT NULL,
   `check_9_refidba` char(15) NOT NULL,
   `check_9_datum` DATETIME NOT NULL,
   UNIQUE KEY `check_9_requidbafa` (`check_9_requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_1_wfmt`(
    `requidbafa` char(15) NOT NULL,
    `geloescht` datetime NULL DEFAULT NULL,
    primary key (`requidbafa`),
    key requidbafa (`requidbafa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_1_wfmt_error`(
    `requidbafa` char(15) NOT NULL,
    `ausgewertet` datetime NULL DEFAULT NULL,
    primary key (`requidbafa`),
    unique key `ausgewertet`(`ausgewertet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_2_sma`(
    `sma` bigint(12) unsigned zerofill NOT NULL,
    `geloescht` datetime NULL DEFAULT NULL,
    primary key (`sma`),
    key sma (`sma`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_2_sma_error`(
    `sma` bigint(12) unsigned zerofill NOT NULL,
    `ausgewertet` datetime NULL DEFAULT NULL,
    primary key (`sma`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_3_kom_index`(
    `kom_index` varchar(50) NOT NULL,
    `geloescht` datetime NULL DEFAULT NULL,
    primary key (`kom_index`),
    key kom_index (`kom_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_4_aa_id`(
    `aa_id` char(15) NOT NULL,
    `geloescht` datetime NULL DEFAULT NULL,
    primary key (`aa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_4_aa_id_error`(
    `aa_id` char(15) NOT NULL,
    `ausgewertet` datetime NOT NULL,
    primary key (`aa_id`, `ausgewertet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_5_bbk_id`(
    `bbk_id` int(9) NOT NULL,
    `geloescht` datetime NULL,
    primary key (`bbk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `del_5_bbk_id_error`(
    `bbk_id` int(9) NOT NULL,
    `ausgewertet` datetime NOT NULL,
    primary key (`bbk_id`, `ausgewertet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_bwa_cluster` (
     `bwa` CHAR(3) NOT NULL,
     `cluster` VARCHAR(5) NOT NULL,
     PRIMARY KEY (`bwa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ref_best_trom` (
   trom_typ VARCHAR(18) NOT NULL,
   PRIMARY KEY (`trom_typ`),
   UNIQUE KEY `trom_typ` (`trom_typ`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS trom_3_status
(
    trom_id   varchar(38) not null,
    trom_typ  varchar(18) not null,
    trom_nr   varchar(10) not null,
    werk      char(4)     not null,
    trom_sma  bigint(12)  not null,
    h_l_datum datetime    null,
    l_a_datum datetime    null,
    a_l_datum datetime    null,
    l_h_datum datetime    null,
    best_gef  char        not null,
    primary key (trom_typ, trom_nr, trom_sma),
    key `trom_id` (`trom_id`),
    key `trom_sma` (`trom_sma`)

) Engine=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS trom_4_sma_anzahl
(
    sma_mit_trom        bigint(12)  not null,
    anzahl_best_trom    smallint unsigned,
    anzahl_trom_verlust smallint unsigned,
    anzahl_trom_ausstehend smallint unsigned,
    primary key (sma_mit_trom),
    key `sma_mit_trom` (`sma_mit_trom`)

) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_fol (
    `area_nr` varchar(50) not null,
    `area_status` varchar(20) not null,
    `area_name` varchar(150),
    `int_expans_decision_date` datetime,
    `tenant_id` int unsigned,
    `initiative` varchar(20) not null,
    `passive_provider` int unsigned,
    `aktive_provider` int unsigned,
    `fol_id` bigint unsigned not null,
    `modification_date` datetime,
    `ge` int unsigned not null,
    `installation_status` varchar(15) not null,
    `reason_for_no_constr` varchar(25),
    `we` int unsigned not null,
    `zb` int unsigned not null,
    `kls_id` bigint unsigned not null,
    `has_rule_violation` varchar(1),
    `is_development_location` varchar(1),
    `build_agree_state` varchar(10),
    `asb` int unsigned,
    `nvt` varchar(10),
    `onkz` int unsigned,
    `expan_period_fine_start` datetime,
    `expan_period_fine_end` datetime,
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `ort` varchar(50),
    `plz` int unsigned,
    `strasse` varchar(50),
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `t_ladedatum` datetime,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelaubau_gestartet` varchar(1),
    `technik` varchar(5) NOT NULL,
    `gfap_kostenpflicht` varchar(1),
    `projekt_id` varchar(50),
    `status_not_planned` datetime,
    `status_passed` datetime,
    `status_passed_plus` datetime,
    `status_prepared` datetime,
    `status_ready` datetime,
    `status_connected` datetime,
    `anz_dosen` int unsigned,
    `z_status_ne3` varchar(10),
    `tag_ne_3_zustimmung` datetime,
    `z_status_ne4` varchar(10),
    `tag_ne_4_zustimmung` datetime,
    `tag_erwartete_zustimmung` datetime,
    `lokation_hinweis` varchar(15),
    `ausgekundet_giga` varchar(1),
    `ist_ausgekundet` varchar(1),
    `ne4_bauart` varchar(55) not null,
    `et_daten_geklaert` varchar(1),
    `et_daten_vorhanden` varchar(1),
    `wartegrund` varchar(35),
    `sponsor` varchar(10),
    `datum_wiedervorlage` datetime,
    `quelle` varchar(4),
    primary key (`fol_id`),
    key `kls_id` (`kls_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_fol_multi (
    `area_nr` varchar(50) not null,
    `area_status` varchar(20) not null,
    `area_name` varchar(150),
    `int_expans_decision_date` datetime,
    `tenant_id` int unsigned,
    `initiative` varchar(20) not null,
    `passive_provider` int unsigned,
    `aktive_provider` int unsigned,
    `fol_id` bigint unsigned not null,
    `modification_date` datetime,
    `ge` int unsigned not null,
    `installation_status` varchar(15) not null,
    `reason_for_no_constr` varchar(25),
    `we` int unsigned not null,
    `zb` int unsigned not null,
    `kls_id` bigint unsigned not null,
    `has_rule_violation` varchar(1),
    `is_development_location` varchar(1),
    `build_agree_state` varchar(10),
    `asb` int unsigned,
    `nvt` varchar(10),
    `onkz` int unsigned,
    `expan_period_fine_start` datetime,
    `expan_period_fine_end` datetime,
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `ort` varchar(50),
    `plz` int unsigned,
    `strasse` varchar(50),
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `t_ladedatum` datetime,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelaubau_gestartet` varchar(1),
    `technik` varchar(5) NOT NULL,
    `gfap_kostenpflicht` varchar(1),
    `projekt_id` varchar(50),
    `status_not_planned` datetime,
    `status_passed` datetime,
    `status_passed_plus` datetime,
    `status_prepared` datetime,
    `status_ready` datetime,
    `status_connected` datetime,
    `anz_dosen` int unsigned,
    `z_status_ne3` varchar(10),
    `tag_ne_3_zustimmung` datetime,
    `z_status_ne4` varchar(10),
    `tag_ne_4_zustimmung` datetime,
    `tag_erwartete_zustimmung` datetime,
    `lokation_hinweis` varchar(15),
    `ausgekundet_giga` varchar(1),
    `ist_ausgekundet` varchar(1),
    `ne4_bauart` varchar(55) not null,
    `et_daten_geklaert` varchar(1),
    `et_daten_vorhanden` varchar(1),
    `wartegrund` varchar(35),
    `sponsor` varchar(10),
    `datum_wiedervorlage` datetime,
    `quelle` varchar(1),
    primary key (`fol_id`, `quelle`, `t_ladedatum`),
    key (`fol_id`),
    key `kls_id` (`kls_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bauaufgabe (
    `auftrag_info_supplier` varchar(15),
    `kls_id`            bigint(12) unsigned NOT NULL,
    `fol_id`            bigint(12) unsigned NOT NULL,
    `produktion`        varchar(5) NOT NULL,
    `gebauede_typ`      varchar(20),
    `auskund_erfordl`   varchar(5),
    `ausbau_status`     varchar(15) NULL DEFAULT NULL,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `area_nr` varchar(50) NOT NULL,
    `area_status` varchar(20) NOT NULL,
    `area_name` varchar(150),
    `we` int unsigned NOT NULL,
    `ge` int unsigned NOT NULL,
    `zb` int unsigned NOT NULL,
    `eigent_zust_status` varchar(10),
    `grund_nichtzust` varchar(40),
    `zust_status_ne3` varchar(10),
    `zust_status_ne4` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt` varchar(10),
    `ba_geb_stich_status` varchar(20),
    `ba_geb_stich_fertigstellung` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `phase_geb_stich`   varchar(20),
    `ba_vzk_einbr_status` varchar(20),
    `ba_vzk_einbr_fertigstellung` datetime,
    `vzk_supplier_partyid` bigint unsigned,
    `vzk_supplier` varchar(82),
    `phase_vzk`    varchar(20),
    `ba_nvt_spleiss_stich_status`   varchar(20),
    `ba_nvt_spleiss_fertigstellung` datetime,
    `nvt_supplier_partyid` bigint unsigned,
    `nvt_supplier`  varchar(82),
    `phase_nvt_spleiss` varchar(20),
    `ba_gfap_status`  varchar(20),
    `ba_gfap_fertigstellung` datetime,
    `umgesetzt_bau_ap` datetime,
    `gfap_supplier_partyid` bigint unsigned,
    `gf_ap_supplier` varchar(100),
    `phase_gf_ap` varchar(20),
    `status_bau_gf_ap` varchar(25),
    `wo_id_gf_ap` int unsigned,
    `wo_status_gf_ap` varchar(15),
    `bau_gf_ap_plan_start` datetime,
    `bau_gf_ap_plan_ende` datetime,
    `ba_auskundung_status` varchar(20),
    `ba_auskundung_fertigstellung` datetime,
    `ausk_umgesetzt` datetime,
    `ausk_supplier_partyid` bigint unsigned,
    `supplier_auskundung` varchar(82),
    `phase_ausk` varchar(20),
    `terminstatus_auskund` varchar(25),
    `auskund_erledigt` varchar(5),
    `wo_id_auskundung` int unsigned,
    `wo_status_auskund` varchar(15),
    `ausk_plan_start` datetime,
    `ausk_plan_ende` datetime,
    `ba_steigleitung_status` varchar(20),
    `ba_steigleitung_fertigstellung` datetime,
    `steigleitung_supplier_partyid` bigint unsigned,
    `steigleitung_supplier` varchar(82),
    `phase_steigleitung` varchar(20),
    `plz` varchar(5),
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `inventarstatus_gf_ap` varchar(10),
    `gf_ap` varchar(45),
    `wartegrund_gebaeude` varchar(55),
    `wv_datum_gebaeude` datetime,
    `erw_loesung_gebaeude` datetime,
    `dispo_fuer` varchar(10),
    `eingang_dispo` datetime,
    `neubaulokation` varchar(5),
    `t_ladedatum` datetime,
    `wfmt_ordernr_ne3_ausbau` varchar(15),
    `wfmt_ordernr_ne4_ausbau` varchar(15),
    `kommentar_wartegrund` text,
    `ba_auskundung_fttb_status` varchar(20),
    `ba_auskundung_fttb_fergigstellung` datetime,
    `ausk_fttb_supplier_partyid` bigint unsigned,
    `supplier_auskundung_fttb` varchar(82),
    `phase_ausk_fttb` varchar(20),
    `ba_weges` varchar(20),
    `ba_weges_fergigstellung` datetime,
    `weges_supplier_partyid` bigint unsigned,
    `supplier_weges` varchar(82),
    `phase_weges` varchar(20),
    `wartegrund_gebaeude_weges` varchar(54),
    `wv_datum_gebaeude_weges` datetime,
    `erw_loesung_gebaeude_weges` datetime,
    `kmr` varchar(20),
    `ba_kmr_fergigstellung` datetime,
    `kmr_supplier_partyid` bigint unsigned,
    `supplier_kmr` varchar(82),
    `phase_kmr` varchar(20),
    `wartegrund_gebaeude_kmr` varchar(54),
    `wv_datum_gebaeude_kmr` datetime,
    `erw_loesung_gebaeude_kmr` datetime,
    `sponsor` varchar(35),
    `bedarfspunktname` varchar(150),
    `quelle` varchar(4),
    primary key (fol_id, quelle)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bauaufgabe_multi (
    `auftrag_info_supplier` varchar(15),
    `kls_id`            bigint(12) unsigned NOT NULL,
    `fol_id`            bigint(12) unsigned NOT NULL,
    `produktion`        varchar(5) NOT NULL,
    `gebauede_typ`      varchar(20),
    `auskund_erfordl`   varchar(5),
    `ausbau_status`     varchar(15) NULL DEFAULT NULL,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `area_nr` varchar(50) NOT NULL,
    `area_status` varchar(20) NOT NULL,
    `area_name` varchar(150),
    `we` int unsigned NOT NULL,
    `ge` int unsigned NOT NULL,
    `zb` int unsigned NOT NULL,
    `eigent_zust_status` varchar(10),
    `grund_nichtzust` varchar(40),
    `zust_status_ne3` varchar(10),
    `zust_status_ne4` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt` varchar(10),
    `ba_geb_stich_status` varchar(20),
    `ba_geb_stich_fertigstellung` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `phase_geb_stich`   varchar(20),
    `ba_vzk_einbr_status` varchar(20),
    `ba_vzk_einbr_fertigstellung` datetime,
    `vzk_supplier_partyid` bigint unsigned,
    `vzk_supplier` varchar(82),
    `phase_vzk`    varchar(20),
    `ba_nvt_spleiss_stich_status`   varchar(20),
    `ba_nvt_spleiss_fertigstellung` datetime,
    `nvt_supplier_partyid` bigint unsigned,
    `nvt_supplier`  varchar(82),
    `phase_nvt_spleiss` varchar(20),
    `ba_gfap_status`  varchar(20),
    `ba_gfap_fertigstellung` datetime,
    `umgesetzt_bau_ap` datetime,
    `gfap_supplier_partyid` bigint unsigned,
    `gf_ap_supplier` varchar(100),
    `phase_gf_ap` varchar(20),
    `status_bau_gf_ap` varchar(25),
    `wo_id_gf_ap` int unsigned,
    `wo_status_gf_ap` varchar(15),
    `bau_gf_ap_plan_start` datetime,
    `bau_gf_ap_plan_ende` datetime,
    `ba_auskundung_status` varchar(20),
    `ba_auskundung_fertigstellung` datetime,
    `ausk_umgesetzt` datetime,
    `ausk_supplier_partyid` bigint unsigned,
    `supplier_auskundung` varchar(82),
    `phase_ausk` varchar(20),
    `terminstatus_auskund` varchar(25),
    `auskund_erledigt` varchar(5),
    `wo_id_auskundung` int unsigned,
    `wo_status_auskund` varchar(15),
    `ausk_plan_start` datetime,
    `ausk_plan_ende` datetime,
    `ba_steigleitung_status` varchar(20),
    `ba_steigleitung_fertigstellung` datetime,
    `steigleitung_supplier_partyid` bigint unsigned,
    `steigleitung_supplier` varchar(82),
    `phase_steigleitung` varchar(20),
    `plz` varchar(5),
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `inventarstatus_gf_ap` varchar(10),
    `gf_ap` varchar(45),
    `wartegrund_gebaeude` varchar(55),
    `wv_datum_gebaeude` datetime,
    `erw_loesung_gebaeude` datetime,
    `dispo_fuer` varchar(10),
    `eingang_dispo` datetime,
    `neubaulokation` varchar(5),
    `t_ladedatum` datetime,
    `wfmt_ordernr_ne3_ausbau` varchar(15),
    `wfmt_ordernr_ne4_ausbau` varchar(15),
    `kommentar_wartegrund` text,
    `ba_auskundung_fttb_status` varchar(20),
    `ba_auskundung_fttb_fergigstellung` datetime,
    `ausk_fttb_supplier_partyid` bigint unsigned,
    `supplier_auskundung_fttb` varchar(82),
    `phase_ausk_fttb` varchar(20),
    `ba_weges` varchar(20),
    `ba_weges_fergigstellung` datetime,
    `weges_supplier_partyid` bigint unsigned,
    `supplier_weges` varchar(82),
    `phase_weges` varchar(20),
    `wartegrund_gebaeude_weges` varchar(54),
    `wv_datum_gebaeude_weges` datetime,
    `erw_loesung_gebaeude_weges` datetime,
    `kmr` varchar(20),
    `ba_kmr_fergigstellung` datetime,
    `kmr_supplier_partyid` bigint unsigned,
    `supplier_kmr` varchar(82),
    `phase_kmr` varchar(20),
    `wartegrund_gebaeude_kmr` varchar(54),
    `wv_datum_gebaeude_kmr` datetime,
    `erw_loesung_gebaeude_kmr` datetime,
    `sponsor` varchar(35),
    `bedarfspunktname` varchar(150),
    `quelle` varchar(4),
    primary key (fol_id, quelle, t_ladedatum)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_nvt_area (
    `area_nr` varchar(50) NOT NULL,
    `area_status` varchar(25) NOT NULL,
    `area_name` varchar(150),
    `nvt_name` varchar(20),
    `nvt` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt_area_prg_status` varchar(35),
    `regel_vvm` varchar(70),
    `regel_at_grob_von` varchar(70),
    `regel_at_grob_bis` varchar(70),
    `regel_nachinst_von` varchar(70),
    `regel_nachinst_bis` varchar(70),
    `regel_rvm_start` varchar(70),
    `beginn_vvm` datetime,
    `ende_vvm` datetime,
    `beginn_ausbau` datetime,
    `ende_ausbau` datetime,
    `ausbau_grob_von` datetime,
    `ausbau_grob_bis` datetime,
    `nachinst_von` datetime,
    `nachinst_bis` datetime,
    `rvm_start` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `nvt_vzk_supplier_partyid` bigint unsigned,
    `nvt_vzk_supplier` varchar(82),
    `trassen_supplier_partyid` bigint unsigned,
    `trassen_supplier` varchar(82),
    `gfap_supplier_partyid` bigint unsigned,
    `gfap_supplier` varchar(82),
    `ne4_ftth_supplier_partyid` bigint unsigned,
    `ne4_ftth_supplier` varchar(82),
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `regelausb_gestartet` varchar(5),
    `nvt_kls_id` bigint unsigned not null,
    `plz` varchar(5),
    `ort` varchar(50),
    `strasse` varchar(50),
    `hausnr` varchar(10),
    `hausnr_zusatz` varchar(10),
    `anz_planned` int unsigned not null,
    `summe_bau` int unsigned not null,
    `anz_passed` int unsigned not null,
    `anz_passed_plus` int unsigned not null,
    `anz_tube_deployed` int unsigned not null,
    `anz_prepared` int unsigned not null,
    `anz_ready` int unsigned not null,
    `anz_connected` int unsigned not null,
    `anz_not_planned` int unsigned not null,
    `area_prog_bid` int unsigned,
    `nvt_area` varchar(70),
    `t_ladedatum` datetime,
     `quelle` varchar(4),
     PRIMARY KEY (nvt_area, quelle),
     KEY (nvt_area)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_bulk
(
    `kls_id`                      bigint(12) unsigned NOT NULL,
    `fol_id`                      bigint(12) unsigned NOT NULL,
    `prod_modell`                 varchar(5)   not null,
    `area_nr`                     varchar(50),
    `area_name`                   varchar(150),
    `area_status`                 varchar(20),
    `ausbauentscheid`             datetime,
    `rollout_partn_initial`       varchar(35),
    `rollout_partn_lokal`         varchar(35),
    `rollout_partn_regel`         varchar(35),
    `eigent_zust_status`          varchar(10),
    `grund_nichtzust`             varchar(40),
    `anz_kundenorder_appartm`     int unsigned not null,
    `anz_kundenorder_building`    int unsigned not null,
    `bulk_prefix`                 varchar(5)   not null,
    `proj_name`                   varchar(255),
    `proj_nr`                     int unsigned,
    `plz`                         int unsigned,
    `ort`                         varchar(50),
    `strasse`                     varchar(50),
    `hausnr`                      varchar(10),
    `hausnr_zusatz`               varchar(10),
    `erstelldatum`                datetime     not null,
    `ausbau_status`               varchar(15),
    `phase_ne3_proj`              varchar(20),
    `phase_ne4_proj`              varchar(20),
    `we`                          int unsigned,
    `ge`                          int unsigned,
    `zb`                          int unsigned,
    `onkz`                        int unsigned,
    `asb`                         int unsigned,
    `nvt`                         varchar(10),
    `zu_bauen_bis`                datetime,
    `ne3_supplier`                varchar(100),
    `ne3_kreditor`                bigint unsigned,
    `ne4_supplier`                varchar(100),
    `ne4_kreditor`                bigint unsigned,
    `plandatum_auskundung`        datetime,
    `umgesetzt_auskundung`        datetime,
    `plandatum_gf_ap`             datetime,
    `umgesetzt_gf_ap`             datetime,
    `plandatum_ta`                datetime,
    `umgesetzt_ta`                datetime,
    `plandatum_dpu`               datetime,
    `umgesetzt_dpu`               datetime,
    `technologie`                 varchar(5)   not null,
    `ausbauart`                   varchar(15)  not null,
    `4fs_premium`                 varchar(5),
    `gf_ap`                       varchar(45),
    `invernturstatus_gf_ap`       varchar(10),
    `anz_as`                      int unsigned,
    `ta_gebaut`                   int unsigned,
    `ta_noch_nicht_gebaut`        int unsigned,
    `ta_in_bau`                   int unsigned,
    `ta_canceled`                 int unsigned,
    `status_bulk_proj`            varchar(15)  not null,
    `status_adresse`              varchar(15)  not null,
    `next_step`                   varchar(200),
    `fehler_nachricht`            text,
    `bulk_order_id`               int unsigned not null,
    `vorheriger_ne3_supplier`     varchar(100),
    `vorheriger_ne3_kreditor`     bigint unsigned,
    `vorheriger_ne4_kreditor`     bigint unsigned,
    `vorheriger_ne4_supplier`     varchar(100),
    `ne3_supplier_am`             datetime,
    `ne4_supplier_am`             datetime,
    `wartegrund_bulk`             varchar(60),
    `wv_datum_bulk`               datetime,
    `erw_loesung_am`              datetime,
    `wg_kommentar`                text,
    `wg_bulk_erstellt`            datetime,
    `eigentümer_id`               bigint unsigned,
    `bulk_ersteller_id`           bigint unsigned,
    `nvt_area_status`             varchar(35),
    `kommentar_bulk`              text,
    `ne4_bauart`                  varchar(60),
    `letztes_aend_datum`          datetime,
    `nvt_area_ne4_status`         varchar(35),
    `bedarfspunktname`            varchar(150),
    `bedarfspunkt_id`             varchar(150),
    `anz_geb_dosen`               int unsigned,
    `anz_dunkle_dosen`            int unsigned,
    `tv_bereitgestellt`           datetime,
    `fester_liefertermin`         varchar(5),
    `tv_bereitg_am`               datetime,
    `angebotsnummer`              varchar(15),
    `marktsegment`                varchar(15),
    `htn_committed`               varchar(5),
    `kundord_pruef`               varchar(5),
    `status_auftpruef`            varchar(40),
    `erste_dose_fertig`           datetime,
    `laufz_kt_dosen`              int unsigned,
    `stornogrund`                 varchar(40),
    `nvt_ausb_beauftragt`         varchar(5),
    `t_ladedatum`                 datetime,
    `quelle`                      varchar(4),
    primary key (`bulk_order_id`, `quelle`),
    key `bulk_order_id`(`bulk_order_id`),
    key `fol_id` (`fol_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_multi
(
    `kls_id`                      bigint(12) unsigned NOT NULL,
    `fol_id`                      bigint(12) unsigned NOT NULL,
    `prod_modell`                 varchar(5)   not null,
    `area_nr`                     varchar(50),
    `area_name`                   varchar(150),
    `area_status`                 varchar(20),
    `ausbauentscheid`             datetime,
    `rollout_partn_initial`       varchar(35),
    `rollout_partn_lokal`         varchar(35),
    `rollout_partn_regel`         varchar(35),
    `eigent_zust_status`          varchar(10),
    `grund_nichtzust`             varchar(40),
    `anz_kundenorder_appartm`     int unsigned not null,
    `anz_kundenorder_building`    int unsigned not null,
    `bulk_prefix`                 varchar(5)   not null,
    `proj_name`                   varchar(255),
    `proj_nr`                     int unsigned,
    `plz`                         int unsigned,
    `ort`                         varchar(50),
    `strasse`                     varchar(50),
    `hausnr`                      varchar(10),
    `hausnr_zusatz`               varchar(10),
    `erstelldatum`                datetime     not null,
    `ausbau_status`               varchar(15),
    `phase_ne3_proj`              varchar(20),
    `phase_ne4_proj`              varchar(20),
    `we`                          int unsigned,
    `ge`                          int unsigned,
    `zb`                          int unsigned,
    `onkz`                        int unsigned,
    `asb`                         int unsigned,
    `nvt`                         varchar(10),
    `zu_bauen_bis`                datetime,
    `ne3_supplier`                varchar(100),
    `ne3_kreditor`                bigint unsigned,
    `ne4_supplier`                varchar(100),
    `ne4_kreditor`                bigint unsigned,
    `plandatum_auskundung`        datetime,
    `umgesetzt_auskundung`        datetime,
    `plandatum_gf_ap`             datetime,
    `umgesetzt_gf_ap`             datetime,
    `plandatum_ta`                datetime,
    `umgesetzt_ta`                datetime,
    `plandatum_dpu`               datetime,
    `umgesetzt_dpu`               datetime,
    `technologie`                 varchar(5)   not null,
    `ausbauart`                   varchar(15)  not null,
    `4fs_premium`                 varchar(5),
    `gf_ap`                       varchar(45),
    `invernturstatus_gf_ap`       varchar(10),
    `anz_as`                      int unsigned,
    `ta_gebaut`                   int unsigned,
    `ta_noch_nicht_gebaut`        int unsigned,
    `ta_in_bau`                   int unsigned,
    `ta_canceled`                 int unsigned,
    `status_bulk_proj`            varchar(15)  not null,
    `status_adresse`              varchar(15)  not null,
    `next_step`                   varchar(200),
    `fehler_nachricht`            text,
    `bulk_order_id`               int unsigned not null,
    `vorheriger_ne3_supplier`     varchar(100),
    `vorheriger_ne3_kreditor`     bigint unsigned,
    `vorheriger_ne4_kreditor`     bigint unsigned,
    `vorheriger_ne4_supplier`     varchar(100),
    `ne3_supplier_am`             datetime,
    `ne4_supplier_am`             datetime,
    `wartegrund_bulk`             varchar(60),
    `wv_datum_bulk`               datetime,
    `erw_loesung_am`              datetime,
    `wg_kommentar`                text,
    `wg_bulk_erstellt`            datetime,
    `eigentümer_id`               bigint unsigned,
    `bulk_ersteller_id`           bigint unsigned,
    `nvt_area_status`             varchar(35),
    `kommentar_bulk`              text,
    `ne4_bauart`                  varchar(60),
    `letztes_aend_datum`          datetime,
    `nvt_area_ne4_status`         varchar(35),
    `bedarfspunktname`            varchar(150),
    `bedarfspunkt_id`             varchar(150),
    `anz_geb_dosen`               int unsigned,
    `anz_dunkle_dosen`            int unsigned,
    `tv_bereitgestellt`           datetime,
    `fester_liefertermin`         varchar(5),
    `tv_bereitg_am`               datetime,
    `angebotsnummer`              varchar(15),
    `marktsegment`                varchar(15),
    `htn_committed`               varchar(5),
    `kundord_pruef`               varchar(5),
    `status_auftpruef`            varchar(40),
    `erste_dose_fertig`           datetime,
    `laufz_kt_dosen`              int unsigned,
    `stornogrund`                 varchar(40),
    `nvt_ausb_beauftragt`         varchar(5),
    `t_ladedatum`                 datetime,
    `quelle`                      varchar(4),
    primary key (`bulk_order_id`, `quelle`, `t_ladedatum`),
    key `bulk_order_id`(`bulk_order_id`),
    key `fol_id` (`fol_id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_6_ba_ne3_4
(
    `ba_nr_intern` int unsigned NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `eigent_zust_status` varchar(10),
    `zust_status_ne3` varchar(10),
    `zust_status_ne4` varchar(10),
    `grund_nichtzust` varchar(40),
    `wartegrund_ba` varchar(35),
    `wv_datum_ba` datetime,
    `erw_loesung_am` datetime,
    `wartegrund_ba_supplier` varchar(100),
    `warteg_ba_supplier_partyid` bigint unsigned,
    `auskund_erfordl` varchar(5),
    `wartegrund_gebaeude` varchar(55),
    `wv_datum_gebaeude` datetime,
    `erw_loesung_gebaeude` datetime,
    `wartegrund_gebaeude_supplier` varchar(100),
    `warteg_gebaeude_supplier_partyid` bigint unsigned,
    `t_ladedatum` datetime NOT NULL,
    key (ba_nummer),
    primary key (ba_nr_intern)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS gbgs_basis_3_fol_id
(
    `id` int NOT NULL AUTO_INCREMENT,
    `fol_id` bigint(12) unsigned NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `ba_nr_intern` int NOT NULL,
    `t_ladedatum` datetime,
    vorhanden tinyint(2) unsigned NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE key `ba_nr_intern` (ba_nr_intern),
    KEY `ba_nummer` (ba_nummer),
    KEY `fol_id_2` (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS gbgs_basis_1_area
(
    `id` int NOT NULL AUTO_INCREMENT,
    `area_nr` varchar(50) NOT NULL,
    `area_name` varchar(150),
    `kls_id` bigint(12) unsigned NOT NULL,
    `t_ladedatum` datetime,
    vorhanden_fol tinyint(2) unsigned NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE key `kls_id` (kls_id),
    KEY `area_nr` (`area_nr`)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS ggs_fol_log1_area_status
(
    `area_status` varchar(20) NOT NULL,
    `area_nr` varchar(50) NOT NULL,
    `t_ladedatum` datetime,
    primary key (area_status, area_nr, t_ladedatum)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_fol_3_ausbau_status
(
    `fol_id` bigint(12) unsigned NOT NULL,
    `not_planned` datetime NULL DEFAULT NULL,
    `planned` datetime NULL DEFAULT NULL,
    `passed` datetime NULL DEFAULT NULL,
    `passed_plus` datetime NULL DEFAULT NULL,
    `tube_deployed` datetime NULL DEFAULT NULL,
    `prepared` datetime NULL DEFAULT NULL,
    `ready` datetime NULL DEFAULT NULL,
    `connected` datetime NULL DEFAULT NULL,
    `ausbau_status` varchar(15) NULL DEFAULT NULL,
    primary key (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_fol_log2_ausbau_status
(
    `fol_id` bigint(12) unsigned NOT NULL,
    `ausbau_status` varchar(15) NOT NULL,
    `t_ladedatum` datetime,
    primary key (fol_id, t_ladedatum)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_basis_4_ba_nr
(
    `id` int NOT NULL AUTO_INCREMENT,
    `ba_nr_intern` int NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `wo_id_auskundung` int unsigned,
    `wo_id_gf_ap` int unsigned,
    `wo_id_gf_ta` int unsigned,
    `t_ladedatum` datetime,
    PRIMARY KEY (`id`),
    UNIQUE key `ba_nr_intern` (ba_nr_intern),
    key `ba_nummer` (ba_nummer)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS gbgs_5_ba_allgemein
(
    `ba_nr_intern` int NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `best_nummer` varchar(25),
    `ba_status` varchar(25) NOT NULL,
    `best_status` varchar(20),
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `mandant` int unsigned,
    `gebauede_typ` varchar(20),
    `gf_id` varchar(10),
    `erstelldatum` datetime NOT NULL,
    `aenderungsdatum` datetime NOT NULL,
    `auftrags_typ` varchar(20) NOT NULL,
    `t_ladedatum` datetime,
    key (ba_nummer),
    primary key (ba_nr_intern)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_7_ba_wo_ausk
(
    `ba_nr_intern` int unsigned NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `wo_id_auskundung` int unsigned,
    `wo_status_auskund` varchar(15),
    `terminstatus_auskund` varchar(25),
    `auskund_erledigt` varchar(5),
    `ausk_plan_start` datetime,
    `ausk_plan_ende` datetime,
    `ausk_umgesetzt` datetime,
    `ausk_supplier_partyid` bigint unsigned,
    `supplier_auskundung` varchar(82),
    `phase_ausk` varchar(20),
    `t_ladedatum` datetime,
    primary key (ba_nr_intern),
    key (ba_nummer)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS gbgs_8_ba_wo_ne3
(
    `ba_nr_intern` int unsigned NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `wo_id_gf_ap` int unsigned,
    `wo_status_gf_ap` varchar(15),
    `gf_ap` varchar(45),
    `inventarstatus_gf_ap` varchar(10),
    `phase_gf_ap` varchar(20),
    `gfap_supplier_partyid` bigint unsigned,
    `gf_ap_supplier` varchar(100),
    `status_bau_gf_ap` varchar(25),
    `inst_status_bau_gf_ap` varchar(10),
    `bau_gf_ap_plan_start` datetime,
    `bau_gf_ap_plan_ende` datetime,
    `umgesetzt_bau_ap` datetime,
    `t_ladedatum` datetime,
    primary key (ba_nr_intern),
    key (ba_nummer)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS gbgs_9_ba_wo_ne4
(
    `ba_nr_intern` int unsigned NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `wo_id_gf_ta` int unsigned,
    `wo_status_gf_ta` varchar(15),
    `phase_bauauftrag` varchar(20),
    `ba_supplier_partyid` bigint unsigned,
    `supplier_bauauftrag` varchar(82),
    `start_inst` datetime,
    `ende_inst` datetime,
    `bau_gf_ta_umgesetzt` datetime,
    `terminbuchung_status` varchar(25) NOT NULL,
    `t_ladedatum` datetime,
    primary key (ba_nr_intern),
    key (ba_nummer)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_1_area_status
(
    `area_nr` varchar(25) NOT NULL,
    `vvm_ongoing` datetime,
    `under_construction` datetime,
    `ctrl_operation` datetime,
    `canceled` datetime,
    `area_planed` datetime,
    primary key (area_nr)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_21_fol_area
(
    `area_nr` varchar(50) not null,
    `int_expans_decision_date` datetime,
    `tenant_id` int unsigned,
    `initiative` varchar(20) not null,
    `passive_provider` int unsigned,
    `aktive_provider` int unsigned,
    `expan_period_fine_start` datetime,
    `expan_period_fine_end` datetime,
    `t_ladedatum` datetime,
    primary key (area_nr)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_22_fol_detail
(
    `area_nr` varchar(50) NOT NULL,
    `modification_date` datetime,
    `reason_for_no_constr` varchar(25),
    `has_rule_violation` varchar(1),
    `is_development_location` varchar(1),
    `build_agree_state` varchar(10),
    `t_ladedatum` datetime,
    primary key (area_nr)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_23_supplier (
    `supplier_name` varchar(100),
    `supplier_partyid` bigint unsigned NOT NULL,
    `kreditor_id` varchar(40),
    `t_ladedatum` datetime,
    primary key (`supplier_partyid`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_24_ba_allgemein2 (
    `ba_nr_intern` int unsigned NOT NULL,
    `ba_nummer` int unsigned NOT NULL,
    `auftrag_info_supplier` varchar(15),
    `naechster_schritt` varchar(125),
    `nbg_erfassungsstatus` varchar(15),
    `zu_bauen_bis` DATETIME,
    `wunschtermin` DATETIME,
    `erstell_datum_bestellung` DATETIME NOT NULL,
    `statusgrund` varchar(80),
    `carrier_name` varchar(60),
    `quelle` varchar(10),
    `terminbu_bauauftrag` varchar(10),
    `terminbu_gf_ap` varchar(10),
    `terminbu_auskund` varchar(10),
    `stornierungsgrund` varchar(80),
    `tarifbest` varchar(5),
    `aenddatum_bestellung` DATETIME,
    `nvt_prog_status_ba` varchar(30),
    `passiv_provid` int unsigned,
    `bppd_id` bigint unsigned,
    `t_ladedatum` datetime,
    primary key (`ba_nr_intern`)
) Engine=InnoDB DEFAULT CHARSET=utf8;



CREATE TABLE IF NOT EXISTS gbgs_25_nvt_lang (
    `fol_id` bigint unsigned NOT NULL,
    `kls_id` bigint unsigned NOT NULL,
    `nvt` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `area_nr` varchar(50) not null,
    `nvt_lang` varchar(255),
    `nvt_area` varchar(255),
    primary key (`fol_id`),
    KEY `nvt_area` (`nvt_area`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_26_baa_allgem (
    `fol_id`            bigint(12) unsigned NOT NULL,
    `produktion`        varchar(5) NOT NULL,
    `gebauede_typ`      varchar(20),
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `neubaulokation` varchar(5),
    `sponsor` varchar(35),
    `bedarfspunktname` varchar(150),
    `t_ladedatum` datetime,
    primary key (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_basis_1_fol (
    `id` int auto_increment,
    `fol_id` bigint unsigned NOT NULL,
    `kls_id` bigint unsigned NOT NULL,
    `area_nr` varchar(50) not null,
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `t_ladedatum` datetime,
    `vorhanden_fol` tinyint(2) NOT NULL,
    `ident_cio` tinyint(2) NOT NULL,
    `ident_bulk` tinyint(2) NOT NULL,
    `ident_baa` tinyint(2) NOT NULL,
    `quelle` varchar(4),
    primary key (`id`),
    unique key fol_id_unique (`fol_id`),
    key fol_id (`fol_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_basis_1_nvt (
    `id` int auto_increment,
    `nvt_area` varchar(70),
    `nvt_kls_id` bigint unsigned NOT NULL,
    `area_nr` varchar(50) not null,
    `nvt_name` varchar(20),
    `rollout_partn_initial` varchar(35),
    `rollout_partn_lokal` varchar(35),
    `rollout_partn_regel` varchar(35),
    `t_ladedatum` datetime,
    `quelle` varchar(4),
    `vorhanden_nvt_area` tinyint(2) NOT NULL,
    primary key (`id`),
    unique key fol_id_unique (`nvt_area`, `quelle`),
    key nvt_area (`nvt_area`),
    key nvt_area_quelle (`nvt_area`, `quelle`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS kio_1_geo
(
    `kls_id` bigint unsigned not null,
    `aenderungs_kz` varchar(1) not null,
    `geo_laenge` decimal(8,5),
    `geo_breite` decimal(8,5),
    `laengen_kz` varchar(1),
    `breiten_kz` varchar(1),
    primary key (kls_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS kio_2_onkz_asb
(
    `kls_id` bigint unsigned not null,
    `aenderungs_kz` varchar(1) not null,
    `onkz` int unsigned,
    `asb` int unsigned,
    primary key (kls_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS kio_3_adresse_inl
(
    `kls_id` bigint unsigned not null,
    `aenderungs_kz` varchar(1) not null,
    `adresstyp` varchar(2) not null,
    `hausnummer` int unsigned,
    `hsnr_zusatz` varchar(6),
    `strasse_name` varchar(40),
    `strasse_kurz` varchar(25),
    `wohnort_name` varchar(40),
    `wohnort_zusatz` varchar(40),
    `gemeinde_name` varchar(40),
    `gemeinde_zusatz` varchar(40),
    `postort_name` varchar(40),
    `postort_zusatz` varchar(40),
    `postort_kurz` varchar(24),
    `posfach` varchar(6),
    `postleitzahl` varchar(5),
    `bundesland` varchar(3),
    `bezirk` varchar(40),
    `landkreis_name` varchar(40),
    `landkreis_zusatz` varchar(40),
    primary key (kls_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS kio_basis_1
(
    `id` int NOT NULL AUTO_INCREMENT,
    `kls_id` bigint unsigned not null,
    `aenderungs_kz` varchar(1) not null,
    `aenderungsdatum` datetime not null,
    `objekt_id` bigint unsigned,
    `adresstyp` varchar(2) not null,
    `id_strasse` bigint unsigned,
    `id_wohort` bigint unsigned,
    `id_gemeinde` bigint unsigned,
    `id_postort` bigint unsigned,
    `aenderungs_nr` int unsigned,
    PRIMARY KEY (`id`),
    key `kls_id1` (kls_id),
    UNIQUE key `kls_id` (kls_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_27_baa_allgem_2 (
    `fol_id`            bigint(12) unsigned NOT NULL,
    `auskund_erfordl`   varchar(5),
    `eigent_zust_status` varchar(10),
    `grund_nichtzust` varchar(40),
    `zust_status_ne3` varchar(10),
    `zust_status_ne4` varchar(10),
    `wartegrund_gebaeude` varchar(55),
    `wv_datum_gebaeude` datetime,
    `erw_loesung_gebaeude` datetime,
    `t_ladedatum` datetime,
    primary key (fol_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_28_baa_gebaude_stich (
    `fol_id`   bigint(12) unsigned NOT NULL,
    `ba_geb_stich_status` varchar(20),
    `ba_geb_stich_fertigstellung` datetime,
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `phase_geb_stich`   varchar(20),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_29_baa_vzk_einbringen (
    `fol_id`            bigint(12) unsigned NOT NULL,
    `ba_vzk_einbr_status` varchar(20),
    `ba_vzk_einbr_fertigstellung` datetime,
    `vzk_supplier_partyid` bigint unsigned,
    `vzk_supplier` varchar(82),
    `phase_vzk`    varchar(20),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_30_baa_nvt_spleiss (
    `fol_id`            bigint(12) unsigned NOT NULL,
    `ba_nvt_spleiss_stich_status`   varchar(20),
    `ba_nvt_spleiss_fertigstellung` datetime,
    `nvt_supplier_partyid` bigint unsigned,
    `nvt_supplier`  varchar(82),
    `phase_nvt_spleiss` varchar(20),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_31_baa_gfap (
    `fol_id`            bigint(12) unsigned NOT NULL,
    `ba_gfap_status`  varchar(20),
    `ba_gfap_fertigstellung` datetime,
    `gfap_supplier_partyid` bigint unsigned,
    `gf_ap_supplier` varchar(100),
    `phase_gf_ap` varchar(20),
    `status_bau_gf_ap` varchar(25),
    `wo_id_gf_ap` int unsigned,
    `wo_status_gf_ap` varchar(15),
    `inventarstatus_gf_ap` varchar(10),
    `gf_ap` varchar(45),
    `bau_gf_ap_plan_start` datetime,
    `bau_gf_ap_plan_ende` datetime,
    `t_ladedatum` datetime,
    primary key (fol_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS gbgs_32_baa_auskundung (
      `fol_id`            bigint(12) unsigned NOT NULL,
      `ba_auskundung_status` varchar(20),
      `ba_auskundung_fertigstellung` datetime,
      `ausk_supplier_partyid` bigint unsigned,
      `supplier_auskundung` varchar(82),
      `phase_ausk` varchar(20),
      `terminstatus_auskund` varchar(25),
      `auskund_erledigt` varchar(5),
      `wo_id_auskundung` int unsigned,
      `wo_status_auskund` varchar(15),
      `ausk_plan_start` datetime,
      `ausk_plan_ende` datetime,
      `ba_auskundung_fttb_status` varchar(20),
      `ba_auskundung_fttb_fergigstellung` datetime,
      `ausk_fttb_supplier_partyid` bigint unsigned,
      `supplier_auskundung_fttb` varchar(82),
      `phase_ausk_fttb` varchar(20),
      `t_ladedatum` datetime,
      primary key (fol_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS gbgs_33_baa_steigleitung (
    `fol_id`            bigint(12) unsigned NOT NULL,
    `ba_steigleitung_status` varchar(20),
    `ba_steigleitung_fertigstellung` datetime,
    `steigleitung_supplier_partyid` bigint unsigned,
    `steigleitung_supplier` varchar(82),
    `phase_steigleitung` varchar(20),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_baa_08_weges (
    `fol_id` bigint(12) unsigned NOT NULL,
    `ba_weges` varchar(20),
    `ba_weges_fergigstellung` datetime,
    `weges_supplier_partyid` bigint unsigned,
    `supplier_weges` varchar(82),
    `phase_weges` varchar(20),
    `wartegrund_gebaeude_weges` varchar(54),
    `wv_datum_gebaeude_weges` datetime,
    `erw_loesung_gebaeude_weges` datetime,
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_baa_09_kmr (
    `fol_id` bigint(12) unsigned NOT NULL,
    `kmr` varchar(20),
    `ba_kmr_fergigstellung` datetime,
    `kmr_supplier_partyid` bigint unsigned,
    `supplier_kmr` varchar(82),
    `phase_kmr` varchar(20),
    `wartegrund_gebaeude_kmr` varchar(54),
    `wv_datum_gebaeude_kmr` datetime,
    `erw_loesung_gebaeude_kmr` datetime,
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS wfm_ggs_1_area (
    `requidbafa` varchar(15) NOT NULL,
    `area_nr_mont_a` varchar(50) NOT NULL,
    `area_name_mont_a` varchar(150),
    `area_nr_klsid_a` varchar(50),
    `area_name_klsid_a` varchar(150),
    `area_nr_strukt` varchar(50),
    `area_name_strukt` varchar(150),
    `area_nr_kask` varchar(50),
    `area_name_kask` varchar(150),
    PRIMARY KEY (`requidbafa`),
    KEY (`area_nr_kask`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS check_8_auftragsnrag (
    `requidbafa` char(15) NOT NULL,
    `auftragsnrag` varchar(50),
    `auftr_loeschen` tinyint(1),
    `loeschen` tinyint(1),
    PRIMARY KEY (requidbafa),
    KEY `auftragsnrag` (`auftragsnrag`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS del_6_auftragsnrag (
    `auftragsnrag` varchar(50),
    `geloescht` datetime NULL DEFAULT NULL,
    PRIMARY KEY (auftragsnrag)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS wfm_ggs_2_nvt_area (
    `requidbafa` varchar(15) NOT NULL,
    `nvt_lang_wfm`  varchar(60),
    `nvt_area_wfm`  varchar(125),
    `nvt_area_wfm_ggs` varchar(125),
    PRIMARY KEY (`requidbafa`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS hashtag_1_bulk (
    `bulk_order_id` int unsigned NOT NULL,
    `fol_id` bigint unsigned,
    `hashtag` varchar(10) NOT NULL,
    `hashtag_detail` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (bulk_order_id),
    KEY fol_id(fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_nvt_area_1(
    `nvt_area` varchar(255),
    `nvt_anz_not_planned` int,
    `nvt_anz_planned` int,
    `nvt_anz_passed` int,
    `nvt_anz_passed_plus` int,
    `nvt_anz_tube_deployed` int,
    `nvt_anz_prepared` int,
    `nvt_anz_ready` int,
    `nvt_anz_connected` int,
    `nvt_min_firstpassed` datetime,
    `nvt_max_firstpassed` datetime,
    `nvt_all_firstpassed` tinyint(1),
    primary key (nvt_area)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS hashtag_2_ibt_geb(
    `fol_id` bigint unsigned NOT NULL,
    `hashtag` varchar(10) NOT NULL,
    `hashtag_detail` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id),
    KEY fol_id (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS hashtag_3_ibt_auft (
    `ba_nummer` int unsigned,
    `fol_id` bigint unsigned,
    `hashtag` varchar(10) NOT NULL,
    `hashtag_detail` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (ba_nummer),
    KEY fol_id (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_basis_2_fol_bulk (
    `id` int auto_increment,
    `fol_id` bigint unsigned,
    `bulk_order_id` bigint unsigned NOT NULL,
    `letzte_bulk_order_id` tinyint(1),
    `offen_bulk_order_id` tinyint(1),
    `vorhanden` tinyint(1),
    `t_ladedatum` datetime,
    PRIMARY KEY (id),
    UNIQUE KEY (bulk_order_id),
    KEY `bulk_order_id_index` (bulk_order_id),
    KEY `fol_id_index` (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_fol_1_hhsto (
    `fol_id` bigint unsigned,
    `we` int unsigned NOT NULL,
    `ge` int unsigned NOT NULL,
    `zb` int unsigned NOT NULL,
    `hhsto` int unsigned,
    `faserbedarf` int unsigned,
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_fol_2_detail (
    `fol_id` bigint unsigned,
    `letzt_aenderungsdatum` datetime,
    `grund_nichtzustimmung` varchar(25),
    `regelkonformer_stand` tinyint(1),
    `neubaulokation` tinyint(1),
    `eigentuem_zustimmung` varchar(10),
    `ausbau_fein_von` datetime,
    `ausbau_fein_bis` datetime,
    `technik` varchar(5) NOT NULL,
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `regelaubau_gestartet` varchar(1),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_area_1 (
    `area_nr` varchar(50) NOT NULL,
    `area_status` varchar(20) NOT NULL,
    `area_name` varchar(150),
    `ausbauentscheidung` datetime,
    `mandant` int unsigned,
    `initiative` varchar(20) not null,
    `betreiber_passiv` int unsigned,
    `betreiber_aktiv` int unsigned,
    `t_ladedatum` datetime,
    `giga_projekt_id` varchar(50),
    PRIMARY KEY (area_nr)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_basis_6_orderid_projbulk(
    `id` int auto_increment,
    `bulk_order_id`               int unsigned not null,
    `proj_nr`                     int unsigned,
    `t_ladedatum`                 datetime,
    vorhanden tinyint(2) unsigned NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY bulk_order_id (bulk_order_id),
    KEY bulk_order_id_index (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_1_allgemein
(
    `bulk_order_id`               int unsigned not null,
    `status_adresse`              varchar(15)  not null,
    `ausbauart`                   varchar(15)  not null,
    `technologie`                 varchar(5)   not null,
    `prod_modell`                 varchar(5)   not null,
    `erstelldatum`                datetime NOT NULL,
    `letztes_aend_datum`          datetime,
    `t_ladedatum`                 datetime,
    primary key (bulk_order_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_bulk_2_ne3_4`
(
    `bulk_order_id`               int unsigned NOT NULL,
    `ausbauentscheid`             datetime,
    `eigent_zust_status`          varchar(10),
    `grund_nichtzust`             varchar(40),
    `zu_bauen_bis`                datetime,
    `next_step`                   varchar(200),
    `fehler_nachricht`            varchar(220),
    `t_ladedatum`                 datetime,
    PRIMARY KEY (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_3_menge
(
    `bulk_order_id`               int unsigned not null,
    `anz_kundenorder_appartm`     int unsigned not null,
    `anz_kundenorder_building`    int unsigned not null,
    `anz_as`                      int unsigned,
    `ta_gebaut`                   int unsigned,
    `ta_noch_nicht_gebaut`        int unsigned,
    `ta_in_bau`                   int unsigned,
    `anz_geb_dosen`               int unsigned,
    `anz_dunkle_dosen`            int unsigned,
    `t_ladedatum`                 datetime,
    primary key (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_4_ausk
(
    `bulk_order_id`        int unsigned not null,
    `plandatum_auskundung`        datetime,
    `umgesetzt_auskundung`        datetime,
    `t_ladedatum`                 datetime,
    PRIMARY KEY (`bulk_order_id`)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_5_dpu
(
    `bulk_order_id`               int unsigned not null,
    `plandatum_dpu`               datetime,
    `umgesetzt_dpu`               datetime,
    `t_ladedatum`                 datetime,
    primary key (bulk_order_id)
) Engine=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_6_ne3
(
    `bulk_order_id`               int unsigned not null,
    `phase_ne3_proj`              varchar(20),
    `ne3_kreditor`                bigint unsigned,
    `plandatum_gf_ap`             datetime,
    `umgesetzt_gf_ap`             datetime,
    `gf_ap`                       varchar(45),
    `invernturstatus_gf_ap`       varchar(10),
    `ne3_supplier_am`             datetime,
    `t_ladedatum` datetime,
    primary key (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_7_ne4
(
    `bulk_order_id`               int unsigned not null,
    `phase_ne4_proj`              varchar(20),
    `ne4_kreditor`                bigint unsigned,
    `plandatum_ta`                datetime,
    `umgesetzt_ta`                datetime,
    `4fs_premium`                 varchar(5)   not null,
    `ne4_supplier_am`             datetime,
    `ne4_bauart`                  varchar(60),
    `t_ladedatum`                 datetime,
    primary key (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_8_projekt
(
    `proj_nr`                     int unsigned,
    `proj_name`                   varchar(255),
    `bulk_prefix`                 varchar(5)   not null,
    `status_bulk_proj`            varchar(15)  not null,
    `t_ladedatum`                 datetime,
    PRIMARY KEY (proj_nr)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;;

CREATE TABLE IF NOT EXISTS ggs_bulk_9_wg
(
    `bulk_order_id`               int unsigned not null,
    `wartegrund_bulk`             varchar(60),
    `wv_datum_bulk`               datetime,
    `erw_loesung_am`              datetime,
    `wg_bulk_erstellt`            datetime,
    `t_ladedatum` datetime,
    primary key (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_log3_status_bulk
(
    `proj_nr`                     int unsigned,
    `status_bulk_proj`            varchar(15)  not null,
    `t_ladedatum`                 datetime,
    primary key (proj_nr, status_bulk_proj, t_ladedatum)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_10_status_adresse
(
    `bulk_order_id` int unsigned not null,
    `created`       datetime,
    `waiting`       datetime,
    `approved`      datetime,
    `in_progress`   datetime,
    `completed`     datetime,
    `error`         datetime,
    primary key (bulk_order_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_11_status_bulk
(
    `proj_nr`     int unsigned,
    `created`     datetime,
    `approved`    datetime,
    `in_progress` datetime,
    `completed`   datetime,
    `error`       datetime,
    primary key (proj_nr)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_log3_status_bulk
(
    `proj_nr`                     int unsigned,
    `status_bulk_proj`            varchar(15)  not null,
    `t_ladedatum`                 datetime,
    primary key (proj_nr, status_bulk_proj, t_ladedatum)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS ggs_bulk_log4_status_adresse
(
    `bulk_order_id`               int unsigned not null,
    `status_adresse`              varchar(15)  not null,
    `t_ladedatum`                 datetime,
    primary key (bulk_order_id, status_adresse, t_ladedatum)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `filter_baa_1_aktuell` (
    `fol_id` bigint(12) unsigned NOT NULL,
    `t_ladedatum` datetime,
    primary key (`fol_id`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;


CREATE TABLE IF NOT EXISTS `ggs_nvt_1_allgemein` (
    `nvt_area` varchar(70),
    `nvt` varchar(10),
    `onkz` int unsigned,
    `asb` int unsigned,
    `nvt_area_prg_status` varchar(35),
    `anz_rvm_proj` int unsigned not null,
    `regelausb_gestartet` varchar(5),
    `t_ladedatum` datetime,
    PRIMARY KEY (nvt_area)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_nvt_2_regel` (
     `nvt_area` varchar(70),
     `regel_vvm` varchar(70),
     `regel_at_grob_von` varchar(70),
     `regel_at_grob_bis` varchar(70),
     `regel_nachinst_von` varchar(70),
     `regel_nachinst_bis` varchar(70),
     `regel_rvm_start` varchar(70),
     `t_ladedatum` datetime,
     PRIMARY KEY (nvt_area)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_nvt_3_termine` (
   `beginn_vvm` datetime,
   `ende_vvm` datetime,
   `beginn_ausbau` datetime,
   `ende_ausbau` datetime,
   `ausbau_grob_von` datetime,
   `ausbau_grob_bis` datetime,
   `nachinst_von` datetime,
   `nachinst_bis` datetime,
   `rvm_start` datetime,
   `nvt_area` varchar(70),
   `t_ladedatum` datetime,
   PRIMARY KEY (nvt_area)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_nvt_4_supplier` (
    `geb_stich_supplier_partyid` bigint unsigned,
    `geb_stich_supplier` varchar(82),
    `nvt_vzk_supplier_partyid` bigint unsigned,
    `nvt_vzk_supplier` varchar(82),
    `trassen_supplier_partyid` bigint unsigned,
    `trassen_supplier` varchar(82),
    `gfap_supplier_partyid` bigint unsigned,
    `gfap_supplier` varchar(82),
    `ne4_ftth_supplier_partyid` bigint unsigned,
    `ne4_ftth_supplier` varchar(82),
    `nvt_area` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (nvt_area)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;


CREATE TABLE IF NOT EXISTS `ggs_nvt_7_anzahl_ap` (
    `anz_planned` int unsigned not null,
    `summe_bau` int unsigned not null,
    `anz_passed` int unsigned not null,
    `anz_passed_plus` int unsigned not null,
    `anz_tube_deployed` int unsigned not null,
    `anz_prepared` int unsigned not null,
    `anz_ready` int unsigned not null,
    `anz_connected` int unsigned not null,
    `anz_not_planned` int unsigned not null,
    `nvt_area` varchar(70),
    `t_ladedatum` datetime,
    PRIMARY KEY (nvt_area)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `dbbgl_1_menge` (
   `sma` bigint(12) unsigned zerofill NOT NULL,
   `updated_at` datetime NOT NULL,
   `kreditor_nr` bigint unsigned,
   `kreditor_name` varchar(255),
   `t_ladedatum` datetime,
   `bw_001`decimal(12,2),
   `bw_002`decimal(12,2),
   `bw_003`decimal(12,2),
   `bw_004`decimal(12,2),
   `bw_005`decimal(12,2),
   `bw_006`decimal(12,2),
   `bw_007`decimal(12,2),
   `bw_008`decimal(12,2),
   `bw_009`decimal(12,2),
   `bw_010`decimal(12,2),
   `bw_011`decimal(12,2),
   `bw_012`decimal(12,2),
   `bw_013`decimal(12,2),
   `bw_014`decimal(12,2),
   `bw_015`decimal(12,2),
   `bw_016`decimal(12,2),
   `bw_017`decimal(12,2),
   `bw_018`decimal(12,2),
   `bw_019`decimal(12,2),
   `bw_020`decimal(12,2),
   `bw_021`decimal(12,2),
   `bw_022`decimal(12,2),
   `bw_023`decimal(12,2),
   `bw_024`decimal(12,2),
   `bw_025`decimal(12,2),
    primary key (sma)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `dbbgl_1_fehlermenge` (
   `sma` varchar(50) NOT NULL,
   `updated_at` datetime NOT NULL,
   `kreditor_nr` bigint unsigned,
   `kreditor_name` varchar(255),
   `t_ladedatum` datetime,
   `bw_001`decimal(12,2),
   `bw_002`decimal(12,2),
   `bw_003`decimal(12,2),
   `bw_004`decimal(12,2),
   `bw_005`decimal(12,2),
   `bw_006`decimal(12,2),
   `bw_007`decimal(12,2),
   `bw_008`decimal(12,2),
   `bw_009`decimal(12,2),
   `bw_010`decimal(12,2),
   `bw_011`decimal(12,2),
   `bw_012`decimal(12,2),
   `bw_013`decimal(12,2),
   `bw_014`decimal(12,2),
   `bw_015`decimal(12,2),
   `bw_016`decimal(12,2),
   `bw_017`decimal(12,2),
   `bw_018`decimal(12,2),
   `bw_019`decimal(12,2),
   `bw_020`decimal(12,2),
   `bw_021`decimal(12,2),
   `bw_022`decimal(12,2),
   `bw_023`decimal(12,2),
   `bw_024`decimal(12,2),
   `bw_025`decimal(12,2),
   primary key (sma)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_gfap_1_allgemein` (
    `fol_id` bigint(12) unsigned NOT NULL,
    `kls_id` bigint unsigned not null,
    `mandant` int unsigned,
    `nl` varchar(30) NOT NULL,
    `bedarfspunktname` varchar(150),
    `bedarfspunkt_id` varchar(150),
    `gebaut_von` varchar(10) NOT NULL,
    `ausbau_status` varchar(15) NOT NULL,
    `regelkonform` varchar(1),
    `technologie` varchar(5) NOT NULL,
    `inventurstatus_gf_ap` Varchar(10),
    `supplier_partyid` bigint unsigned,
    `supplier` varchar(82),
    `gebaut_am` datetime,
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `ggs_gfap_2_ausbau` (
    `fol_id` bigint(12) unsigned NOT NULL,
    `gfap_zuklein` varchar(1),
    `anz_ang_dosen` int unsigned NOT NULL,
    `dosenvergleich` varchar(30) NOT NULL,
    `gfap_bezeichner` varchar(15),
    `material` varchar(40),
    `materialbez` varchar(45),
    `material_nr` varchar(30),
    `min` int unsigned default 0,
    `max` int unsigned default 0,
    `soll_umbau` varchar(5),
    `umbau_material_nr` varchar(30),
    `umbau_materialbez` varchar(45),
    `t_ladedatum` datetime,
    PRIMARY KEY (fol_id)
)ENGINE=InnoDB DEFAULT CHARSET = utf8;
