create table if not exists tx_beast_domain_model_comment
(
    uid                       int unsigned auto_increment,
    pid                       int unsigned      default 0  not null,
    tstamp                    int unsigned      default 0  not null,
    crdate                    int unsigned      default 0  not null,
    cruser_id                 int unsigned      default 0  not null,
    deleted                   smallint unsigned default 0  not null,
    hidden                    smallint unsigned default 0  not null,
    starttime                 int unsigned      default 0  not null,
    endtime                   int unsigned      default 0  not null,
    type                      int unsigned      default 0  null,
    bodytext                  text                         not null,
    newest                    smallint unsigned default 0  not null,
    requidba<PERSON>                varchar(255)      default '' not null,
    sma                       int               default 0  not null,
    tnid                      int               default 0  not null,
    themenspezifisch          smallint unsigned default 0  not null,
    user                      int unsigned      default 0  null,
    kommentar_index           varchar(255)      default '' not null,
    bafa_newest               smallint unsigned default 0  not null,
    index_newest              smallint unsigned default 0  not null,
    sma_newest                smallint unsigned default 0  not null,
    relevant                  smallint unsigned default 1  not null,
    test                      smallint unsigned default 0  not null,
    auftragsnr_auftraggeber   varchar(255)      default '' not null,
    auftragsnr_newest         smallint unsigned default 0  not null,
    area_nr                   varchar(255)      default '' not null,
    kls_id                    int               default 0  not null,
    fol_id                    bigint            default 0  not null,
    nvt_lang                  varchar(255)      default '' not null,
    ba_nummer                 int               default 0  not null,
    bulk_order_id             int               default 0  not null,
    proj_nr                   int               default 0  not null,
    kls_id_newest             smallint unsigned default 0  not null,
    fol_id_newest             smallint unsigned default 0  not null,
    nvt_lang_newest           smallint unsigned default 0  not null,
    bauauftrag_nummer_newest  smallint unsigned default 0  not null,
    bulk_order_id_newest      smallint unsigned default 0  not null,
    bulk_projektnummer_newest smallint unsigned default 0  not null,
    area_nr_newest            smallint unsigned default 0  not null,
    PRIMARY KEY (`uid`),
    KEY `parent` (`pid`,`deleted`,`hidden`),
    KEY `type_2` (`type`,`requidbafa`,`bafa_newest`),
    KEY `type_3` (`type`,`sma`,`sma_newest`),
    KEY `type_4` (`type`,`kommentar_index`,`index_newest`),
    KEY `type_5` (`type`,`auftragsnr_auftraggeber`,`auftragsnr_newest`),
    KEY `type_6` (`type`,`area_nr`,`area_nr_newest`),
    KEY `type_7` (`type`,`kls_id`,`kls_id_newest`),
    KEY `type_8` (`type`,`fol_id`,`fol_id_newest`),
    KEY `type_9` (`type`,`nvt_lang`,`nvt_lang_newest`),
    KEY `type_11` (`type`,`bulk_order_id`,`bulk_order_id_newest`),
    KEY `type_10` (`type`,`ba_nummer`,`bauauftrag_nummer_newest`),
    KEY `type_12` (`type`,`proj_nr`,`bulk_projektnummer_newest`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 collate = utf8mb4_unicode_ci;


create table IF NOT EXISTS `tx_beast_domain_model_activeoptionvalue` (
    uid          int unsigned NOT NULL  auto_increment,
    pid          int unsigned      default 0  not null,
    tstamp       int unsigned      default 0  not null,
    crdate       int unsigned      default 0  not null,
    cruser_id    int unsigned      default 0  not null,
    deleted      smallint unsigned default 0  not null,
    hidden       smallint unsigned default 0  not null,
    starttime    int unsigned      default 0  not null,
    endtime      int unsigned      default 0  not null,
    sma          int                          null,
    bafa         varchar(255)      default '' not null,
    refidba      varchar(255)      default '' not null,
    aaid         varchar(255)      default '' not null,
    option_field int unsigned      default 0  null,
    option_value int unsigned      default 0  not null,
    PRIMARY KEY (`uid`),
    KEY `aaid` (`aaid`),
    KEY `bafa` (`bafa`),
    KEY `refidba` (`refidba`),
    KEY `sma` (`sma`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;


CREATE TABLE IF NOT EXISTS `tx_beast_domain_model_todo` (
`uid` int(10) unsigned NOT NULL AUTO_INCREMENT,
`pid` int(10) unsigned NOT NULL DEFAULT 0,
`tstamp` int(10) unsigned NOT NULL DEFAULT 0,
`crdate` int(10) unsigned NOT NULL DEFAULT 0,
`cruser_id` int(10) unsigned NOT NULL DEFAULT 0,
`description` text NOT NULL,
`due_date` datetime DEFAULT NULL,
`access_type` int(11) NOT NULL DEFAULT 0,
`sma` int(11) NOT NULL DEFAULT 0,
`sma_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`requidbafa` varchar(255) NOT NULL DEFAULT '',
`bafa_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`kommentar_index` varchar(255) NOT NULL DEFAULT '',
`index_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`category` int(10) unsigned DEFAULT 0,
`usergroup` int(10) unsigned NOT NULL DEFAULT 0,
`user` int(10) unsigned DEFAULT 0,
`area_nr` varchar(255) NOT NULL DEFAULT '',
`area_nr_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`kls_id` int(11) NOT NULL DEFAULT 0,
`kls_id_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`fol_id` bigint(11) NOT NULL DEFAULT 0,
`fol_id_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`nvt_lang` varchar(255) NOT NULL DEFAULT '',
`nvt_lang_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`ba_nummer` int(11) NOT NULL DEFAULT 0,
`ba_nummer_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`bulk_order_id` int(11) NOT NULL DEFAULT 0,
`bulk_order_id_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
`proj_nr` int(11) NOT NULL DEFAULT 0,
`proj_nr_newest` smallint(5) unsigned NOT NULL DEFAULT 0,
PRIMARY KEY (`uid`),
KEY `parent` (`pid`),
KEY `sma_key` (`category`,`access_type`,`sma_newest`),
KEY `bafa_key` (`category`,`access_type`,`bafa_newest`),
KEY `index_key` (`category`,`access_type`,`index_newest`),
KEY `area_key` (`category`,`access_type`,`area_nr_newest`),
KEY `kls_key` (`category`,`access_type`,`kls_id_newest`),
KEY `fol_key` (`category`,`access_type`,`fol_id_newest`),
KEY `nvt_key` (`category`,`access_type`,`nvt_lang_newest`),
KEY `ba_key` (`category`,`access_type`,`ba_nummer_newest`),
KEY `bulk_key` (`category`,`access_type`,`bulk_order_id_newest`),
KEY `proj_key` (`category`,`access_type`,`proj_nr_newest`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
