import collections
from multiprocessing import Event

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.basis1wms_filter import basis1wms_filter


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
        target_table (str): Target table name
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/basis1wms_filter/basic_filter_test",
                target_table="test123",
            ),
            id="basic_filter_test"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/basis1wms_filter/null_values_test",
                target_table="test123",
            ),
            id="null_values_test"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/basis1wms_filter/zero_values_test",
                target_table="test123",
            ),
            id="zero_values_test"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/basis1wms_filter/mixed_data_test",
                target_table="test123",
            ),
            id="mixed_data_test"
        ),
    ],
)
@pytest.mark.freeze_time("2024-01-15 14:30:00")
def test_basis1wms_filter(
    load_test_data_from_csv, 
    test_data_folder, 
    f_test_tables, 
    target_table, 
    database_manager, 
    create_csv_files
):
    """Test für basis1wms_filter - Filtert NULL/0 sma_verbunden Werte heraus"""

    basis1wms_filter.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["requidbafa"])
