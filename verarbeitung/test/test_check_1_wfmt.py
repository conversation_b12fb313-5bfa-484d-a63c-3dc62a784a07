import collections

import pytest
from verarbeitung.src.tables.check_1_wfmt import check_1_wfmt
from verarbeitung.test.utils.helper import compare_csv_files


class TestParams(
    collections.namedtuple(
        "TestParams",
        (
            "test_data_folder",
            "target_table",
        ),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(test_data_folder="csv_files/check_1_wfmt/initial", target_table="check_1_wfmt"),
            id="check_1_wfmt_initial"
        ),
        pytest.param(
            *TestParams(test_data_folder="csv_files/check_1_wfmt/1894_erste_erweiterung", target_table="check_1_wfmt"),
            id="check_1_erste_erweiterung"
        ),
        pytest.param(
            *TestParams(test_data_folder="csv_files/check_1_wfmt/2205_zweite_erweiterung", target_table="check_1_wfmt"),
            id="check_1_zweite_erweiterung"
        ),
    ],
)
@pytest.mark.freeze_time("2022-03-08")
def test_check_1_wfmt(
    load_test_data_from_csv, test_data_folder, f_test_tables, target_table, database_manager, create_csv_files
):

    check_1_wfmt.run(max_package_size=1, min_package_size=1)

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["requidbafa"])
