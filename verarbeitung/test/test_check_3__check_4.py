import collections
from os import path

import pandas as pd
import pytest
from verarbeitung.src.tables.check_3__check_4__bafa_sma import check_3__check_4__bafa_sma
from verarbeitung.test.utils.helper import compare_csv_files


class TestParams(
    collections.namedtuple(
        "TestParams",
        ("test_data_folder"),
    )
):
    """Parametrization of a test.

    Attributes:
        test_data_folder (str): Test data folder
    """


def group_members_consistent(directory, table_name, index_column, compare_column="grp_zu_loeschen"):

    expected_csv_file = path.join(directory, "expected_result_data", "{}.csv".format(table_name))
    result_csv_file = path.join(directory, "result_data", "{}.csv".format(table_name))

    _expected = pd.read_csv(expected_csv_file, delimiter=";")
    _result = pd.read_csv(result_csv_file, delimiter=";")

    changes = []

    for group in _expected[compare_column].drop_duplicates().tolist():
        sr_grp_members_expected = _expected[_expected[compare_column] == group][index_column]

        sr_grp_members_result = _result[_result[index_column].isin(sr_grp_members_expected)][index_column]

        if not sr_grp_members_result.tolist() == sr_grp_members_result.tolist():
            changes.append(
                f"Members of group {group} changed from {sr_grp_members_expected.tolist()} to {sr_grp_members_result.tolist()}"
            )

    if changes:
        test_name = path.split(directory)[-1]
        _newline = "\n"
        with open(path.join(directory, "result_data", "report_{}.csv".format(test_name)), "w") as file_out:
            file_out.write(f"{_newline.join([change for change in changes])}")

    return changes == []


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/check_3__check_4/1468_initial",
            ),
            id="check_3__check4__initial",
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/check_3__check_4/1468_special",
            ),
            id="check_3__check4__special",
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/check_3__check_4/1913_2_umstellung_auf_check_7",
            ),
            id="check_3__check_4__umstellung_auf_check_7",
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/check_3__check_4/1925_summenaufträge_und_zentrale_sma",
            ),
            id="check_3__check_4__summenaufträge_und_zentrale_sma",
        ),
    ],
)
@pytest.mark.freeze_time("2022-06-01")
def test_check_3__check_4(
    load_test_data_from_csv, test_data_folder, f_test_tables, database_manager, create_csv_files, debug_data_folder
):

    check_3__check_4__bafa_sma.run_pre(debug=True, dump_dir=debug_data_folder)
    check_3__check_4__bafa_sma.run(debug=True, dump_dir=debug_data_folder)
    check_3__check_4__bafa_sma.run_post(debug=True, dump_dir=debug_data_folder)

    create_csv_files(test_data_folder, "check_3_bafa_sma")
    create_csv_files(test_data_folder, "check_4_sma_bafa")

    assert compare_csv_files(
        test_data_folder, "check_3_bafa_sma", index_columns=["requidbafa"], ignored_columns=["grp_zu_loeschen"]
    )
    assert compare_csv_files(
        test_data_folder, "check_4_sma_bafa", index_columns=["sma"], ignored_columns=["grp_zu_loeschen"]
    )

    assert group_members_consistent(
        test_data_folder,
        "check_4_sma_bafa",
        "sma",
    )
    assert group_members_consistent(
        test_data_folder,
        "check_3_bafa_sma",
        "requidbafa",
    )
