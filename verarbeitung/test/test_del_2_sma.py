import collections

import pytest
from verarbeitung.src.tables.del_2_sma import del_2_sma
from verarbeitung.test.utils.helper import compare_csv_files


class TestParams(
    collections.namedtuple(
        "TestParams",
        (
            "test_data_folder",
            "target_table",
        ),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(test_data_folder="csv_files/del_2_sma/Telekom-1469", target_table="del_2_sma"),
            id="del_2_sma-1469"
        ),
    ],
)
@pytest.mark.freeze_time("2022-06-02")
def test_del_2_sma(
    load_test_data_from_csv, test_data_folder, f_test_tables, target_table, database_manager, create_csv_files
):

    del_2_sma.run()

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["sma"])
