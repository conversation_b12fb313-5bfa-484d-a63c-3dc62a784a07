import collections

import pytest
from verarbeitung.src.tables.del_tx_domain_model_todo import del_tx_beast_domain_model_todo
from verarbeitung.test.utils.helper import compare_csv_files


class TestParams(
    collections.namedtuple(
        "TestParams",
        (
            "test_data_folder",
            "target_table",
        ),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/del_tx_beast_domain_model_todo/default",
                target_table="tx_beast_domain_model_todo",
            ),
            id="del_tx_beast_domain_model_todo_default"
        ),
    ],
)
@pytest.mark.freeze_time("2023-11-10")
def test_del_tx_beast_domain_model_todo(
    load_test_data_from_csv, test_data_folder, f_test_tables, target_table, typo3_database_manager, create_csv_files
):

    del_tx_beast_domain_model_todo.run()

    create_csv_files(test_data_folder, target_table, database_manager=typo3_database_manager)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["uid"])
