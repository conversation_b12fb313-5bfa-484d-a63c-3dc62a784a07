import collections

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.gbgs_30_baa_nvt_spleiss import gbgs_30_baa_nvt_spleiss
from multiprocessing import Event


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    pass


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/gbgs_30_baa_nvt_spleiss/default",
                target_table="gbgs_30_baa_nvt_spleiss",
            ),
            id="gbgs_30_baa_nvt_spleiss_default"
        ),
    ],
)
def test_gbgs_30_baa_nvt_spleiss(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    create_csv_files,
):
    gbgs_30_baa_nvt_spleiss.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["fol_id"])
