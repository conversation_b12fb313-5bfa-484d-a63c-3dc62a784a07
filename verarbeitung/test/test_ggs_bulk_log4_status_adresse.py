import collections

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.ggs_bulk_log4_status_adresse import ggs_bulk_log4_status_adresse
from multiprocessing import Event


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/ggs_bulk_log4_status_adresse/2959_initialTest",
                target_table="ggs_bulk_log4_status_adresse",
            ),
            id="ggs_bulk_log4_status_adresse_initialTest"
        ),
    ],
)
def test_ggs_bulk_log4_status_adresse(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    create_csv_files,
):
    ggs_bulk_log4_status_adresse.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(
        test_data_folder, target_table, index_columns=["bulk_order_id", "status_adresse", "t_ladedatum"]
    )
