import collections

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.ggs_fol_1_hhsto import ggs_fol_1_hhsto
from multiprocessing import Event


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    pass


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/ggs_fol_1_hhsto/2876_initialTest",
                target_table="ggs_fol_1_hhsto",
            ),
            id="ggs_fol_1_hhsto_default"
        ),
    ],
)
def test_ggs_fol_1_hhsto(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    create_csv_files,
):
    ggs_fol_1_hhsto.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["fol_id"])
