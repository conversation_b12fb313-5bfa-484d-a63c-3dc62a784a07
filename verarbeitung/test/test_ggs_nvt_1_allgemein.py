import collections

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.ggs_nvt_1_allgemein import ggs_nvt_1_allgemein
from multiprocessing import Event


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/ggs_nvt_1_allgemein/3024_initialTest",
                target_table="ggs_nvt_1_allgemein",
            ),
            id="ggbs_nvt_1_allgemein_initial"
        ),
    ],
)
def test_ggs_nvt_1_allgemein(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    create_csv_files,
):

    ggs_nvt_1_allgemein.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["nvt_area"])
