import collections

import pytest
from freezegun import freeze_time

from verarbeitung.src.tables.Common.task import IndexType
from verarbeitung.src.tables.loeschung_nach_id import loeschung_nach_id
from verarbeitung.test.utils.helper import compare_csv_files


class TestParams(
    collections.namedtuple(
        "TestParams",
        (
            "test_data_folder",
            "target_tables",
            "test_time",
        ),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/loeschung_bafa/1472_initial",
                target_tables={
                    "del_1_wfmt": "requidbafa",
                    "wmsti_psl_001": "wmsti_requidbafa",
                    "wfmt_bemerkung": "wmsti_requidbafa",
                    "wfmt_betr_korr": "requidbafa",
                    "wfmt_vzg_hist": "requidbafa",
                    "wfmt_ps": "ref_ba",
                    "statusergaenzungen": "ref_bafa_requestid",
                    "workflow_auftrag_zeitpunkte": "twf_auftrag_id",
                    "basis_1_wms_psl": "requidbafa",
                    "wms_1_allgemein": "requidbafa",
                    "wms_2_termine": "requidbafa",
                    "wms_3_lieferzeit": "requidbafa",
                    "wms_4_arbeitsplan": "requidbafa",
                    "wms_5_prodreif": "requidbafa",
                    "wms_6_master": "requidbafa",
                    "wms_7_gefaehrdet": "requidbafa",
                    "wms_8_prio": "requidbafa",
                    "wms_9_zusatz": "requidbafa",
                    "wms_10_steuernd": "requidbafa",
                    "wfm_11_montageort": "requidbafa",
                    "wfm_12_technisch": "requidbafa",
                    "wfm_13_storno": "requidbafa",
                    "wfm_14_kunde": "requidbafa",
                    "wfm_15_asp": "requidbafa",
                    "wfm_16_termine_mhl": "requidbafa",
                    "phasen_1_daprida": "requidbafa",
                    "phasen_2_diagnose": "requidbafa",
                    "phasen_3_angebot": "requidbafa",
                    "phasen_4_projektierung": "requidbafa",
                    "phasen_5_realisierung": "requidbafa",
                    "phasen_6_inbetriebnahme": "requidbafa",
                    "phasen_7_dokumentation": "requidbafa",
                    "phasen_8_abschluss": "requidbafa",
                    "phasen_9_esel": "requidbafa",
                    "phasen_10_gefaehrdet": "requidbafa",
                    "phasen_11_sergaktuell": "requidbafa",
                    "phasen_12_pslserg": "requidbafa",
                    "phasen_13_ausk_status": "requidbafa",
                    "phasen_2_diagnose_lz": "requidbafa",
                    "phasen_3_angebot_lz": "requidbafa",
                    "phasen_4_projektierung_lz": "requidbafa",
                    "phasen_5_realisierung_lz": "requidbafa",
                    "phasen_6_inbetriebnahme_lz": "requidbafa",
                    "phasen_7_dokumentation_lz": "requidbafa",
                    "ueweg_1_allgemein": "requidbafa",
                    "fr_1_wms": "requidbafa",
                    "nap_1_analyse": "requidbafa",
                    "vrt_termin": "requidbafa",
                    "betr_korr_1_historie": "requidbafa",
                    "betr_korr_2_auftrag": "requidbafa",
                    "betr_korr_3_1vzg": "requidbafa",
                    "prod_steht_1": "requidbafa",
                },
                test_time="2022-07-20",
            ),
            id="1472_loeschung_bafa",
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/loeschung_bafa/1645_erweiterung_2023",
                target_tables={
                    "del_1_wfmt": "requidbafa",
                    "wmsti_psl_001": "wmsti_requidbafa",
                    "wfmt_bemerkung": "wmsti_requidbafa",
                    "wfmt_betr_korr": "requidbafa",
                    "wfmt_vzg_hist": "requidbafa",
                    "wfmt_ps": "ref_ba",
                    "statusergaenzungen": "ref_bafa_requestid",
                    "workflow_auftrag_zeitpunkte": "twf_auftrag_id",
                    "basis_1_wms_psl": "requidbafa",
                    "wms_1_allgemein": "requidbafa",
                    "wms_2_termine": "requidbafa",
                    "wms_3_lieferzeit": "requidbafa",
                    "wms_4_arbeitsplan": "requidbafa",
                    "wms_5_prodreif": "requidbafa",
                    "wms_6_master": "requidbafa",
                    "wms_7_gefaehrdet": "requidbafa",
                    "wms_8_prio": "requidbafa",
                    "wms_9_zusatz": "requidbafa",
                    "wms_10_steuernd": "requidbafa",
                    "wfm_11_montageort": "requidbafa",
                    "wfm_12_technisch": "requidbafa",
                    "wfm_13_storno": "requidbafa",
                    "wfm_14_kunde": "requidbafa",
                    "wfm_15_asp": "requidbafa",
                    "wfm_16_termine_mhl": "requidbafa",
                    "wfm_17_nvt": "requidbafa",
                    "phasen_1_daprida": "requidbafa",
                    "phasen_2_diagnose": "requidbafa",
                    "phasen_3_angebot": "requidbafa",
                    "phasen_4_projektierung": "requidbafa",
                    "phasen_5_realisierung": "requidbafa",
                    "phasen_6_inbetriebnahme": "requidbafa",
                    "phasen_7_dokumentation": "requidbafa",
                    "phasen_8_abschluss": "requidbafa",
                    "phasen_9_esel": "requidbafa",
                    "phasen_10_gefaehrdet": "requidbafa",
                    "phasen_11_sergaktuell": "requidbafa",
                    "phasen_12_pslserg": "requidbafa",
                    "phasen_13_ausk_status": "requidbafa",
                    "phasen_2_diagnose_lz": "requidbafa",
                    "phasen_3_angebot_lz": "requidbafa",
                    "phasen_4_projektierung_lz": "requidbafa",
                    "phasen_5_realisierung_lz": "requidbafa",
                    "phasen_6_inbetriebnahme_lz": "requidbafa",
                    "phasen_7_dokumentation_lz": "requidbafa",
                    "ueweg_1_allgemein": "requidbafa",
                    "fr_1_wms": "requidbafa",
                    "nap_1_analyse": "requidbafa",
                    "vrt_termin": "requidbafa",
                    "betr_korr_1_historie": "requidbafa",
                    "betr_korr_2_auftrag": "requidbafa",
                    "betr_korr_3_1vzg": "requidbafa",
                    "prod_steht_1": "requidbafa",
                    "wfm_ggs_1_area": "requidbafa",
                },
                test_time="2023-12-21",
            ),
            id="1645_loeschung_bafa_erweiterung_2023",
        ),
    ],
)
# @pytest.mark.freeze_time("2022-07-20")
def test_loeschung_bafa(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_tables,
    test_time,
    database_manager,
    create_csv_files,
):
    with freeze_time(test_time):

        additional_data = {
            "delete_base_table": "del_1_wfmt",
            "delete_base_column": "requidbafa",
            "delete_index_type": IndexType.bafa,
        }
        loeschung_nach_id.run(additional_data=additional_data)

        # create all csv files first
        for target_table, index_column in target_tables.items():
            create_csv_files(test_data_folder, target_table)
        # compare all tables
        for target_table, index_column in target_tables.items():
            assert compare_csv_files(test_data_folder, target_table, index_columns=[index_column])
