import collections
import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.phasen_02 import phasen_2_diagnose_lz


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table", "test_field"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


def ignored_columns(column_to_test):
    columns = ["eingbbn_der_at"]
    if column_to_test:
        columns.remove(column_to_test)
    return columns


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_2_LZ/eingbbn_der_at",
                target_table="phasen_2_diagnose_lz",
                test_field="eingbbn_der_at",
            ),
            id="Test_AP_19_Q"
        ),
    ],
)
@pytest.mark.freeze_time("2019-04-01 11:31:57")
def test_ap_19_a(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    test_field,
    create_csv_files,
):
    phasen_2_diagnose_lz.run()

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, ignored_columns=ignored_columns(test_field))
