# -*- coding=utf-8 -*-
from __future__ import absolute_import

import collections
import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.phasen_04 import phasen_4_projektierung_lz


class TestParams(
    collections.namedtuple(
        "TestParams",
        ("test_data_folder", "target_table", "test_field"),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
        target_table (str): Result Table in Database and in expected_result_data folder
    """


def ignored_columns(column_to_test):
    columns = [
        "pjg_pe_at",
        "pjg_pja_at",
        "pjg_wsn_at",
        "pjg_wsa_at",
        "wsa_wse_at",
        "fmbm_pe_at",
        "n4an_nae_at",
        "pjg_fmbm_at",
        "pjg_n4an_at",
    ]
    if column_to_test:
        columns.remove(column_to_test)
    return columns


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/pjg_pe_at/Test_AP_19_C",
                target_table="phasen_4_projektierung_lz",
                test_field="pjg_pe_at",
            ),
            id="Test_AP_19_C"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/pjg_pja_at/Test_AP_19_D",
                target_table="phasen_4_projektierung_lz",
                test_field="pjg_pja_at",
            ),
            id="Test_AP_19_D"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/pjg_wsn_at/Test_AP_19_E",
                target_table="phasen_4_projektierung_lz",
                test_field="pjg_wsn_at",
            ),
            id="Test_AP_19_E"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/pjg_wsa_at/Test_AP_19_F",
                target_table="phasen_4_projektierung_lz",
                test_field="pjg_wsa_at",
            ),
            id="Test_AP_19_F"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/fmbm_pe_at/Test_AP_19_H",
                target_table="phasen_4_projektierung_lz",
                test_field="fmbm_pe_at",
            ),
            id="Test_AP_19_H"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/pjg_fmbm_at/Test_AP_19_R",
                target_table="phasen_4_projektierung_lz",
                test_field="pjg_fmbm_at",
            ),
            id="Test_AP_19_R"
        ),
    ],
)
@pytest.mark.freeze_time("2019-03-29 10:00:00")
def test_csv_imports(
    load_test_data_from_csv, test_data_folder, target_table, database_manager, test_field, create_csv_files
):
    phasen_4_projektierung_lz.run()
    create_csv_files(test_data_folder, target_table)
    assert compare_csv_files(test_data_folder, target_table, ignored_columns=ignored_columns(test_field))


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/wsa_wse_at/Test_AP_19_G",
                target_table="phasen_4_projektierung_lz",
                test_field="wsa_wse_at",
            ),
            id="Test_AP_19_G"
        ),
    ],
)
@pytest.mark.freeze_time("2019-03-19 10:00:00")
def test_csv_imports1(
    load_test_data_from_csv, test_data_folder, target_table, database_manager, test_field, create_csv_files
):
    phasen_4_projektierung_lz.run()
    create_csv_files(test_data_folder, target_table)
    assert compare_csv_files(test_data_folder, target_table, ignored_columns=ignored_columns(test_field))


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/n4an_nae_at/Test_AP_19_I",
                target_table="phasen_4_projektierung_lz",
                test_field="n4an_nae_at",
            ),
            id="Test_AP_19_I"
        ),
    ],
)
@pytest.mark.freeze_time("2019-03-07 10:00:00")
def test_csv_imports2(
    load_test_data_from_csv, test_data_folder, target_table, database_manager, test_field, create_csv_files
):
    phasen_4_projektierung_lz.run()
    create_csv_files(test_data_folder, target_table)
    assert compare_csv_files(test_data_folder, target_table, ignored_columns=ignored_columns(test_field))


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_4_LZ/pjg_n4an_at/Test_AP_19_S",
                target_table="phasen_4_projektierung_lz",
                test_field="pjg_n4an_at",
            ),
            id="Test_AP_19_S"
        ),
    ],
)
@pytest.mark.freeze_time("2019-03-28 11:31:57")
def test_csv_imports3(
    load_test_data_from_csv, test_data_folder, target_table, database_manager, test_field, create_csv_files
):
    phasen_4_projektierung_lz.run()
    create_csv_files(test_data_folder, target_table)
    assert compare_csv_files(test_data_folder, target_table, ignored_columns=ignored_columns(test_field))
