import collections
import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.phasen_07 import phasen_07_dokumentation


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table", "test_field"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


def ignored_columns(column_to_test):
    columns = [
        "dog_sezst",
        "dbg_sezst",
        "doa_sezst",
        "dok_sezst",
        "rft_sezst",
        "qs_sezst",
        "dic_sezst",
        "nmv_sezst",
        "dov_sezst",
    ]
    if column_to_test:
        columns.remove(column_to_test)
    return columns


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/dog_sezst/Test_326",
                test_field="dog_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_326"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/dbg_sezst/Test_327",
                test_field="dbg_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_327"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/doa_sezst/Test_328",
                test_field="doa_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_328"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/dok_sezst/Test_329",
                test_field="dok_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_329"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/rft_sezst/Test_330",
                test_field="rft_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_330"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/qs_sezst/Test_331",
                test_field="qs_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_331"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/dic_sezst/Test_AP_04_j",
                test_field="dic_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_AP_04_j"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/nmv_sezst/2021-08-11-08-45_Telekom2020_951",
                test_field="nmv_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_nmv_sezst"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/Phasen_7/dov_sezst/2021-08-11-08-45_Telekom2020_952",
                test_field="dov_sezst",
                target_table="phasen_7_dokumentation",
            ),
            id="Test_dov_sezst"
        ),
    ],
)
def test_telekom(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    test_field,
    create_csv_files,
):
    phasen_07_dokumentation.run()

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, ignored_columns=ignored_columns(test_field))
