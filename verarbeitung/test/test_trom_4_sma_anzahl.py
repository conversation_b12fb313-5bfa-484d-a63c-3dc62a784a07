import collections
import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.trom_4_sma_anzahl import trom_4_sma_anzahl


class TestParams(
    collections.namedtuple(
        "TestParams",
        (
            "test_data_folder",
            "target_table",
        ),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/trom_4_sma_anzahl/data",
                target_table="trom_4_sma_anzahl",
            ),
            id="Initial"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/trom_4_sma_anzahl/data_update_al_lh",
                target_table="trom_4_sma_anzahl",
            ),
            id="data_update_al_lh"
        ),
    ],
)
def test_processing(
    load_test_data_from_csv, test_data_folder, f_test_tables, target_table, database_manager, create_csv_files
):
    trom_4_sma_anzahl.run()

    create_csv_files(test_data_folder, name=target_table, sql="""SELECT * FROM trom_4_sma_anzahl;""")

    assert compare_csv_files(
        test_data_folder,
        target_table,
        index_columns=["sma_mit_trom"],
    )
