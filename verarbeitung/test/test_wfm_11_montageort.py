import collections
import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.wfm_11 import wfm_11


class TestParams(
    collections.namedtuple(
        "TestParams",
        (
            "test_data_folder",
            "target_table",
        ),
    )
):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        # Default Export with
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfm_11_montageort/mont_b_adresse/Test_Telekom_136",
                target_table="wfm_11_montageort",
            ),
            id="Test_Telekom_136"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfm_11_montageort/2521",
                target_table="wfm_11_montageort",
            ),
            id="2521"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfm_11_montageort/kls_id_",
                target_table="wfm_11_montageort",
            ),
            id="kls_id_"
        ),
    ],
)
def test_der_sezst(
    load_test_data_from_csv, test_data_folder, f_test_tables, target_table, database_manager, create_csv_files
):

    wfm_11.run()

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["requidbafa"])
