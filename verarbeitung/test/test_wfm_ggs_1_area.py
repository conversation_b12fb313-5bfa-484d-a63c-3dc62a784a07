import collections

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.wfm_ggs_1_area import wfm_ggs_1_area
from multiprocessing import Event


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfm_ggs_1_area/default",
                target_table="wfm_ggs_1_area",
            ),
            id="default"
        ),
    ],
)
def test_wfm_ggs_1_area(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    create_csv_files,
):
    wfm_ggs_1_area.fill_processing_table()
    wfm_ggs_1_area.run_a(stop_event=Event())
    wfm_ggs_1_area.run_mont_a(stop_event=Event())
    wfm_ggs_1_area.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["requidbafa"])


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfm_ggs_1_area/2609_area_kaskade_master",
                target_table="wfm_ggs_1_area",
            ),
            id="wfm_ggs_1_area__area_kaskade_master"
        ),
    ],
)
def test_wfm_ggs_1_area_kaskade(
    load_test_data_from_csv,
    test_data_folder,
    f_test_tables,
    target_table,
    database_manager,
    create_csv_files,
):
    wfm_ggs_1_area.run_kaskade(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["requidbafa"])
