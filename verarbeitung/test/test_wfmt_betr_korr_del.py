import collections
from multiprocessing import Event

import pytest

from verarbeitung.test.utils.helper import compare_csv_files
from verarbeitung.src.tables.wfmt_betr_korr_del import wfmt_betr_korr_del


class TestParams(collections.namedtuple("TestParams", ("test_data_folder", "target_table"))):
    """Parametrization of a export test.

    Attributes:
        test_data_folder (str): Test data folder
        target_table (str): Target table name
    """


@pytest.mark.parametrize(
    TestParams._fields,
    [
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfmt_betr_korr_del/regel1_aufeinanderfolgend",
                target_table="wfmt_betr_korr_del",
            ),
            id="regel1_aufeinanderfolgend"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfmt_betr_korr_del/regel2_zeitbasiert_0_2h",
                target_table="wfmt_betr_korr_del",
            ),
            id="regel2_zeitbasiert_0_2h"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfmt_betr_korr_del/beide_regeln_kombiniert",
                target_table="wfmt_betr_korr_del",
            ),
            id="beide_regeln_kombiniert"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfmt_betr_korr_del/keine_duplikate",
                target_table="wfmt_betr_korr_del",
            ),
            id="keine_duplikate"
        ),
        pytest.param(
            *TestParams(
                test_data_folder="csv_files/wfmt_betr_korr_del/nur_nacht_datensaetze",
                target_table="wfmt_betr_korr_del",
            ),
            id="nur_nacht_datensaetze"
        ),
    ],
)
@pytest.mark.freeze_time("2024-01-15 14:30:00")
def test_wfmt_betr_korr_del(
    load_test_data_from_csv, 
    test_data_folder, 
    f_test_tables, 
    target_table, 
    database_manager, 
    create_csv_files
):
    """Test für wfmt_betr_korr_del - Duplikat-Bereinigung mit zwei Regeln"""

    wfmt_betr_korr_del.run(stop_event=Event())

    create_csv_files(test_data_folder, target_table)

    assert compare_csv_files(test_data_folder, target_table, index_columns=["requidbafa", "aenderungsdatum"])
