import csv
from os import path, listdir, mkdir

import csvdiff

from verarbeitung.src.tables.Database.DatabaseManager import DatabaseManager


CURRENT_PATH = path.dirname(path.realpath(__file__))


def load_data_from_csv(directory, path_to_test):
    path_to_input_data = path.join(CURRENT_PATH, "csv_files", directory, path_to_test, "input_data")
    csv_files = [f for f in listdir(path_to_input_data) if path.isfile(path.join(path_to_input_data, f))]
    database_manager = DatabaseManager()
    for csv_file in csv_files:
        path_to_csv = path.join(path_to_input_data, csv_file)
        table = csv_file.replace("test.", "")
        table = table.replace(".csv", "")
        # get existing column headers
        column_name_query = "SELECT column_name from information_schema.columns WHERE table_schema = '{schema}' AND table_name = '{table}'".format(
            schema="beast_aggregation_testdata", table=table
        )
        column_name_row_proxy_list = database_manager.deadlock_save_execute(column_name_query).all()
        column_name_list = [column[0] for column in column_name_row_proxy_list]
        column_string = ",".join(column_name_list)
        query = "LOAD DATA INFILE '{path}' REPLACE INTO TABLE {table} FIELDS TERMINATED BY ';' IGNORE 1 LINES ({columns});".format(
            path=path_to_csv, table=table, columns=column_string
        )
        database_manager.connection.execute(query)


def compare_csv_files(directory, table_name, index_columns=["requidbafa"], ignored_columns=None):

    if isinstance(ignored_columns, str):
        ignored_columns = [ignored_columns]

    expected_csv_file = path.join(directory, "expected_result_data", "{}.csv".format(table_name))
    result_csv_file = path.join(directory, "result_data", "{}.csv".format(table_name))

    diff = csvdiff.diff_files(
        expected_csv_file, result_csv_file, index_columns=index_columns, sep=";", ignored_columns=ignored_columns
    )

    counted_changes = len(diff["changed"])
    counted_added = len(diff["added"])
    counted_removed = len(diff["removed"])
    sum_of_changes = counted_changes + counted_added + counted_removed
    test_name = path.split(directory)[-1]
    if sum_of_changes:
        with open(path.join(directory, "result_data", "report_{}.csv".format(test_name)), "w") as file_out:
            for change in diff["changed"]:
                changes = []
                for key, field in change["fields"].items():
                    changes.append(
                        "{field} from expected: '{value_from}' to calculated: '{"
                        "to}'".format(field=key, value_from=field["from"], to=field["to"])
                    )
                file_out.write("{key}; {changes}\n".format(key=change["key"][0], changes=";".join(changes)))

    if sum_of_changes:
        print("{} records are not as expected".format(counted_added + counted_removed + counted_changes))

    return sum_of_changes == 0


def find_test_folders(directory):
    path_to_csv_files = path.join(CURRENT_PATH, "csv_files/" + directory)
    test_directories = [f for f in listdir(path_to_csv_files) if path.isdir(path.join(path_to_csv_files, f))]
    return test_directories


def create_csv_files(
    test_data_folder,
    tables=None,
    sql=None,
    name=None,
    df=None,
    date_columns=None,
    int_columns=[],
    database_manager=DatabaseManager(),
):
    result_data_directory = path.join(test_data_folder, "result_data")
    if not path.exists(result_data_directory):
        mkdir(result_data_directory)

    if tables is not None:
        if isinstance(tables, str):
            tables = [tables]
        for table in tables:
            csv_file = "{}/{}.csv".format(result_data_directory, table)
            df_table = database_manager.get_dataframe_by_table(
                table,
                date_columns=date_columns,
                int_columns=int_columns,
            )
            df_table.to_csv(csv_file, date_format="%Y-%m-%d %H:%M:%S", index=False, sep=";")
    elif sql is not None and isinstance(sql, str) and name is not None:
        csv_file = "{}/{}.csv".format(result_data_directory, name)
        df_table = database_manager.get_dataframe_by_query(sql)
        df_table.to_csv(csv_file, date_format="%Y-%m-%d %H:%M:%S", index=False, sep=";")
    elif df is not None:
        csv_file = "{}/{}.csv".format(result_data_directory, name)
        df.to_csv(csv_file, date_format="%Y-%m-%d %H:%M:%S", index=False, sep=";")
    else:
        raise Exception("something is missing or wrong to create csv file")


def read_csv_headline(csv_file: str) -> list:
    """Read the header of a csv file and return it.
    Snatched from importer.

    Args:
        csv_file (str): path to the csv file

    Returns:
        list with headers
    """
    with open(csv_file, "r", encoding="utf-8") as input_csv_file:
        csv_reader = csv.reader(input_csv_file, delimiter=";")
        for row in csv_reader:
            # we need only the first headline row
            return row
